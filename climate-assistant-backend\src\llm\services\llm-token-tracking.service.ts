import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LlmTokenUsage } from '../entities/llm-token-usage.entity';
import { RequestStatus, LLM_MODELS, LLM_PROVIDER } from '../enums';
import { WorkerLogger } from 'src/shared/logger.service';

interface TokenUsageContext {
  workspaceId?: string;
  userId?: string;
  projectId?: string;
  taskType: string;
  taskSubtype?: string;
  taskRelatedEntityId?: string;
  endpoint?: string;
}

export interface TokenUsageRecord extends TokenUsageContext {
  model: LLM_MODELS;
  llmProvider: LLM_PROVIDER;
  modelVersion?: string;
  inputTokens: number;
  outputTokens: number;
  totalTokens?: number;
  responseTimeMs?: number;
  status: RequestStatus;
  errorMessage?: string;
}

@Injectable()
export class LlmTokenTrackingService {
  private readonly logger = new WorkerLogger(LlmTokenTrackingService.name);

  constructor(
    @InjectRepository(LlmTokenUsage)
    private readonly tokenUsageRepository: Repository<LlmTokenUsage>
  ) {}

  /**
   * Record token usage for an LLM API interaction
   */
  async recordTokenUsage(usage: TokenUsageRecord): Promise<LlmTokenUsage> {
    try {
      const tokenUsage = this.tokenUsageRepository.create({
        ...usage,
        totalTokens:
          usage.totalTokens || usage.inputTokens + usage.outputTokens,
      });

      return this.tokenUsageRepository.save(tokenUsage);
    } catch (err) {
      console.error('Error recording token usage:', err);
      this.logger.error('Failed to record token usage', err);
    }
  }

  /**
   * Get token usage statistics for a workspace
   */
  async getWorkspaceUsage(
    workspaceId: string,
    startDate?: Date,
    endDate?: Date
  ) {
    const query = this.tokenUsageRepository
      .createQueryBuilder('usage')
      .where('usage.workspaceId = :workspaceId', { workspaceId });

    if (startDate) {
      query.andWhere('usage.createdAt >= :startDate', { startDate });
    }
    if (endDate) {
      query.andWhere('usage.createdAt <= :endDate', { endDate });
    }

    return query.getMany();
  }

  /**
   * Get aggregated usage statistics by model
   */
  async getUsageByModel(workspaceId: string, startDate?: Date, endDate?: Date) {
    const query = this.tokenUsageRepository
      .createQueryBuilder('usage')
      .select('usage.model', 'model')
      .addSelect('SUM(usage.inputTokens)', 'totalInputTokens')
      .addSelect('SUM(usage.outputTokens)', 'totalOutputTokens')
      .addSelect('SUM(usage.totalTokens)', 'totalTokens')
      .addSelect('COUNT(*)', 'requestCount')
      .where('usage.workspaceId = :workspaceId', { workspaceId })
      .groupBy('usage.model');

    if (startDate) {
      query.andWhere('usage.createdAt >= :startDate', { startDate });
    }
    if (endDate) {
      query.andWhere('usage.createdAt <= :endDate', { endDate });
    }

    return query.getRawMany();
  }

  /**
   * Get usage statistics by task type
   */
  async getUsageByTaskType(
    workspaceId: string,
    startDate?: Date,
    endDate?: Date
  ) {
    const query = this.tokenUsageRepository
      .createQueryBuilder('usage')
      .select('usage.taskType', 'taskType')
      .addSelect('SUM(usage.totalTokens)', 'totalTokens')
      .addSelect('COUNT(*)', 'requestCount')
      .where('usage.workspaceId = :workspaceId', { workspaceId })
      .groupBy('usage.taskType');

    if (startDate) {
      query.andWhere('usage.createdAt >= :startDate', { startDate });
    }
    if (endDate) {
      query.andWhere('usage.createdAt <= :endDate', { endDate });
    }

    return query.getRawMany();
  }

  /**
   * Get error statistics
   */
  async getErrorStats(workspaceId: string, startDate?: Date, endDate?: Date) {
    const query = this.tokenUsageRepository
      .createQueryBuilder('usage')
      .select('usage.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .where('usage.workspaceId = :workspaceId', { workspaceId });

    if (startDate) {
      query.andWhere('usage.createdAt >= :startDate', { startDate });
    }
    if (endDate) {
      query.andWhere('usage.createdAt <= :endDate', { endDate });
    }

    return query.groupBy('usage.status').getRawMany();
  }
}
