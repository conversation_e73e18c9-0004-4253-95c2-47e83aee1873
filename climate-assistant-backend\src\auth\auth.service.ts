import {
  Injectable,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { RegisterWithCompanyDto } from './auth.dto';
import { Token } from 'src/users/entities/token.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { WorkspaceSSO } from '../workspace/entities/workspace-sso.entity';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Workspace)
    private workspaceRepository: Repository<Workspace>,
    @InjectRepository(WorkspaceSSO)
    private workspaceSSORepository: Repository<WorkspaceSSO>
  ) {}

  async login(email: string, password: string): Promise<string> {
    const user = await this.usersService.findByEmailWithPassword(email);

    if (user === null) {
      throw new UnauthorizedException({
        message: 'E-Mail oder Passwort ist inkorrekt',
      });
    }

    // Security: Prevent SSO users from using password-based login
    if (user.authMethod === 'sso') {
      throw new UnauthorizedException({
        message: 'This account is configured for SSO login only. Please use your organization\'s single sign-on to access your account.',
      });
    }

    // Ensure user has a password for password-based auth
    if (!user.password) {
      throw new UnauthorizedException({
        message: 'E-Mail oder Passwort ist inkorrekt',
      });
    }

    const isPasswordCorrect = await bcrypt.compare(password, user?.password);
    if (!isPasswordCorrect) {
      throw new UnauthorizedException({
        message: 'E-Mail oder Passwort ist inkorrekt',
      });
    }

    const workspace = await this.usersService.findFirstWorkspaceIdByUser(user);

    // Security: Check if the user's email domain has SSO configured - prevent password login for SSO domains
    const emailDomain = user.email.split('@')[1]?.toLowerCase();
    
    if (emailDomain) {
      // Check if this specific domain has SSO configured for this workspace
      const ssoConfig = await this.workspaceSSORepository.findOne({
        where: {
          domain: emailDomain,
          workspaceId: workspace.workspaceId,
          enabled: true,
        },
      });

      if (ssoConfig) {
        throw new UnauthorizedException({
          message: `This email domain (@${emailDomain}) is configured for SSO login only. Please use your organization's single sign-on to access your account.`,
        });
      }

      // Fallback: Check workspace-level SSO setting (for legacy configurations)
      const workspaceEntity = await this.workspaceRepository.findOne({
        where: { id: workspace.workspaceId },
        select: ['id', 'ssoEnabled', 'ssoConfig'],
      });

      if (workspaceEntity?.ssoEnabled && workspaceEntity?.ssoConfig?.allowedDomains?.includes(emailDomain)) {
        throw new UnauthorizedException({
          message: `This email domain (@${emailDomain}) is configured for SSO login only. Please use your organization's single sign-on to access your account.`,
        });
      }
    }

    const payload = {
      sub: user.id,
      id: user.id,
      email: user.email,
      workspaceId: workspace.workspaceId,
    };
    return await this.jwtService.signAsync(payload);
  }

  async registerWithCompany(
    registerDto: RegisterWithCompanyDto
  ): Promise<string> {
    const { email, password, companyName } = registerDto;

    // Check if the user already exists
    const existingUser = await this.usersService.findByEmail(email);
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create the user, company, and workspace (you need to implement this method)
    const register = await this.usersService.createUserWithCompanyAndWorkspace({
      email,
      password: hashedPassword,
      companyName,
    });

    const payload = {
      sub: register.user.id,
      id: register.user.id,
      email: email,
      workspaceId: register.workspace.id,
    };
    return await this.jwtService.signAsync(payload);
  }

  async resetPassword(password: string, token: string, fullName?: string) {
    const userToken = await this.usersService.validateToken(token);
    const user = await this.usersService.resetUserPassword(
      userToken,
      password,
      fullName
    );
    const payload = {
      sub: user.id,
      id: user.id,
      email: user.email,
      workspaceId: user.userWorkspaces[0].workspaceId,
    };
    return await this.jwtService.signAsync(payload);
  }

  async sendPasswordResetEmail(email: string, origin: string): Promise<void> {
    await this.usersService.sendPasswordResetEmail({
      email,
      origin,
      shouldSendEmail: origin.endsWith('.glacier.eco'),
    });
  }

  async validateToken(token: string): Promise<Token> {
    return this.usersService.validateToken(token);
  }

  async switchUserWorkspace(
    userId: string,
    workspaceId: string
  ): Promise<string> {
    const user = await this.usersService.switchWorkspace(userId, workspaceId);

    const payload = {
      sub: user.id,
      id: user.id,
      email: user.email,
      workspaceId: workspaceId,
    };

    return await this.jwtService.signAsync(payload);
  }

  // SSO-related methods
  async findBySSOId(
    ssoUserId: string,
    ssoProviderId: string
  ): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { ssoUserId, ssoProviderId },
    });
  }

  async createSSOUser(userData: {
    email: string;
    name: string;
    authMethod: 'sso';
    ssoUserId: string;
    ssoProviderId: string;
    lastSsoLogin: Date;
  }): Promise<User> {
    const user = this.userRepository.create({
      ...userData,
      authMethod: 'sso',
    });
    return await this.userRepository.save(user);
  }
}
