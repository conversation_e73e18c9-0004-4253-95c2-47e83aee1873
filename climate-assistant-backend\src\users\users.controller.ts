import {
  Body,
  Controller,
  Get,
  Post,
  Request,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { UsersService } from './users.service';
import { ApiTags } from '@nestjs/swagger';
import { AuthGuard } from 'src/auth/auth.guard';
import { SystemPermissions } from 'src/constants';
import { PermissionGuard } from 'src/auth/guard/permission.guard';
import { Permissions } from 'src/auth/decorators/permissions.decorator';

@ApiTags('users')
@UseGuards(AuthGuard)
@UseGuards(PermissionGuard)
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get('/prompt-context')
  async getUserPromptSettings(@Request() req) {
    const userId = req.user.id;

    const context = await this.usersService.getUserPromptContext(userId);

    return {
      context,
    };
  }

  @Post('/user-prompt-settings')
  async savePromptSettings(@Request() req, @Body() body: { context: string }) {
    const userId = req.user.id;
    const { context } = body;

    await this.usersService.saveUserPromptContext(userId, context);
    return { success: true };
  }

  @Permissions(SystemPermissions.CREATE_WORKSPACE)
  @Post('/create-workspace')
  async createUserCompanyWorkspace(
    @Body()
    body: {
      email: string;
      password?: string;
      name: string;
      ssoConfig?: {
        enabled: boolean;
        domain: string;
        protocol: 'saml' | 'oauth' | 'oidc';
        workosOrganizationId: string;
        workosConnectionId: string;
      };
    }
  ) {
    const { email, password, name, ssoConfig } = body;

    // Validate password requirement based on SSO configuration
    const isSSOEnabled = ssoConfig?.enabled === true;
    if (!isSSOEnabled && !password) {
      throw new BadRequestException('Password is required when SSO is not enabled');
    }
    if (isSSOEnabled && password) {
      throw new BadRequestException('Password should not be provided when SSO is enabled');
    }

    const create = await this.usersService.createUserWithCompanyAndWorkspace({
      email,
      password,
      companyName: name,
      ssoConfig,
    });
    return create;
  }
}
