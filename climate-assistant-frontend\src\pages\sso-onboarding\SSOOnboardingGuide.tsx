import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Shield,
  Users,
  Settings,
  Mail,
  // Phone,
  // FileText,
  CheckCircle,
  Clock,
  AlertCircle,
  Copy,
  ExternalLink,
  Info,
  Globe,
  Key,
  Server,
  ArrowLeft,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export const SSOOnboardingGuide: React.FC = () => {
  const [copiedText, setCopiedText] = useState<string | null>(null);
  const navigate = useNavigate();

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    setCopiedText(label);
    setTimeout(() => setCopiedText(null), 2000);
  };

  const glacierEndpoints = {
    production: {
      entityId: 'https://app.glacier.eco/api/auth/saml/metadata',
      acsUrl: 'https://app.glacier.eco/api/auth/sso/callback',
      sloUrl: 'https://app.glacier.eco/api/auth/sso/logout',
      metadataUrl: 'https://app.glacier.eco/api/auth/saml/metadata',
      redirectUris: [
        'https://app.glacier.eco/api/auth/sso/callback',
        'https://app.glacier.eco/dashboard',
      ],
    },
    development: {
      entityId: 'https://dev.glacier.eco/api/auth/saml/metadata',
      acsUrl: 'https://dev.glacier.eco/api/auth/sso/callback',
      sloUrl: 'https://dev.glacier.eco/api/auth/sso/logout',
      metadataUrl: 'https://dev.glacier.eco/api/auth/saml/metadata',
      redirectUris: [
        'https://dev.glacier.eco/api/auth/sso/callback',
        'https://dev.glacier.eco/dashboard',
      ],
    },
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-16 px-4 max-w-6xl">
        {/* Back to Login Button */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate('/sso-login')}
            className="text-glacier-bluedark hover:bg-glacier-green/10 hover:text-glacier-bluedark"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Login
          </Button>
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <Badge
            variant="secondary"
            className="px-4 py-2 mb-6 bg-glacier-bluedark text-white rounded-full"
          >
            <Settings className="w-4 h-4 mr-2" />
            Technical Setup Guide
          </Badge>
          <h1 className="text-4xl font-bold mb-6 text-glacier-bluedark font-pantea">
            SSO Integration Guide for IT Teams
          </h1>
          <p className="text-lg text-slate-600 max-w-3xl mx-auto mb-8">
            This guide provides technical information for integrating your
            organization's identity provider with Glacier Climate Assistant.
            Choose your protocol below to get started.
          </p>

          {/* Quick Overview for IT Teams */}
          <Alert className="max-w-4xl mx-auto text-left bg-glacier-blue/5 border-glacier-blue/20">
            <Info className="h-4 w-4 text-glacier-blue" />
            <AlertTitle className="text-glacier-bluedark">
              For IT Administrators
            </AlertTitle>
            <AlertDescription className="text-slate-700">
              This guide covers the technical requirements for SSO integration.
              We support both SAML 2.0 and OAuth/OIDC protocols. Most enterprise
              identity providers support at least one of these standards. If
              you're unsure which protocol your system uses, SAML 2.0 is the
              most common for enterprise SSO implementations.
            </AlertDescription>
          </Alert>
        </div>

        <Tabs defaultValue="oauth" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8 bg-slate-100 p-1 rounded-lg">
            <TabsTrigger
              value="oauth"
              className="data-[state=active]:bg-glacier-bluedark data-[state=active]:text-white rounded-md"
            >
              <Key className="w-4 h-4 mr-2" />
              OAuth/OIDC
            </TabsTrigger>
            <TabsTrigger
              value="saml"
              className="data-[state=active]:bg-glacier-bluedark data-[state=active]:text-white rounded-md"
            >
              <Shield className="w-4 h-4 mr-2" />
              SAML 2.0
            </TabsTrigger>
            <TabsTrigger
              value="providers"
              className="data-[state=active]:bg-glacier-bluedark data-[state=active]:text-white rounded-md"
            >
              <Server className="w-4 h-4 mr-2" />
              Providers
            </TabsTrigger>
            <TabsTrigger
              value="contact"
              className="data-[state=active]:bg-glacier-bluedark data-[state=active]:text-white rounded-md"
            >
              <Mail className="w-4 h-4 mr-2" />
              Contact
            </TabsTrigger>
          </TabsList>

          {/* OAuth/OIDC Tab */}
          <TabsContent value="oauth" className="space-y-6">
            <Card className="border-slate-200">
              <CardHeader>
                <CardTitle className="flex items-center text-glacier-bluedark">
                  <Key className="w-6 h-6 mr-3 text-glacier-green" />
                  OAuth 2.0 / OpenID Connect Configuration
                </CardTitle>
                <CardDescription className="text-slate-600">
                  <strong>What is OAuth/OIDC?</strong> OAuth 2.0 with OpenID
                  Connect is a modern authentication protocol commonly used by
                  cloud providers like Google Workspace, Azure AD, and Auth0.
                  It's ideal for organizations using cloud-first identity
                  solutions and provides excellent security with a simpler setup
                  process.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Benefits explanation */}
                <Alert className="bg-glacier-green/5 border-glacier-green/20">
                  <CheckCircle className="h-4 w-4 text-glacier-green" />
                  <AlertTitle className="text-glacier-bluedark">
                    Why Choose OAuth/OIDC?
                  </AlertTitle>
                  <AlertDescription className="text-slate-700">
                    OAuth/OIDC is perfect for modern, cloud-native
                    organizations. It offers faster setup, better mobile
                    support, and seamless integration with popular cloud
                    identity providers. The protocol is also more lightweight
                    and easier to troubleshoot than SAML.
                  </AlertDescription>
                </Alert>

                {/* Application Registration Info */}
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-glacier-bluedark">
                    Application Registration Details
                  </h3>
                  <p className="text-slate-600 mb-4">
                    You'll need to register Glacier Climate Assistant as an
                    application in your OAuth provider. Use these details during
                    the registration process:
                  </p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <Card className="border-slate-100 bg-slate-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark flex items-center">
                          <Globe className="w-5 h-5 mr-2 text-glacier-blue" />
                          Production Environment
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {[
                          {
                            label: 'Application Name',
                            value: 'Glacier Climate Assistant',
                            description: 'Display name for the application',
                          },
                          {
                            label: 'Application Type',
                            value: 'Web Application',
                            description: 'Type of OAuth application',
                          },
                          {
                            label: 'Authorization Grant',
                            value: 'Authorization Code with PKCE',
                            description: 'Recommended OAuth flow',
                          },
                        ].map((item) => (
                          <div key={item.label} className="space-y-2">
                            <label className="text-sm font-medium text-glacier-bluedark">
                              {item.label}:
                            </label>
                            <div className="bg-white p-2 rounded border text-sm">
                              {item.value}
                            </div>
                            <p className="text-xs text-slate-500">
                              {item.description}
                            </p>
                          </div>
                        ))}

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <label className="text-sm font-medium text-glacier-bluedark">
                              Redirect URIs:
                            </label>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                copyToClipboard(
                                  glacierEndpoints.production.redirectUris.join(
                                    '\n'
                                  ),
                                  'Redirect URIs (Prod)'
                                )
                              }
                              className="h-6 px-2 text-xs rounded-full"
                            >
                              {copiedText === 'Redirect URIs (Prod)' ? (
                                <CheckCircle className="w-3 h-3 text-glacier-green" />
                              ) : (
                                <Copy className="w-3 h-3" />
                              )}
                            </Button>
                          </div>
                          <div className="bg-white p-2 rounded border space-y-1">
                            {glacierEndpoints.production.redirectUris.map(
                              (uri) => (
                                <div
                                  key={uri}
                                  className="text-xs font-mono break-all"
                                >
                                  {uri}
                                </div>
                              )
                            )}
                          </div>
                          <p className="text-xs text-slate-500">
                            Allowed callback URLs after authentication
                          </p>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-slate-100 bg-slate-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark flex items-center">
                          <Settings className="w-5 h-5 mr-2 text-glacier-greenmid" />
                          Development Environment
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {[
                          {
                            label: 'Application Name',
                            value: 'Glacier Climate Assistant (Dev)',
                            description: 'Development instance name',
                          },
                          {
                            label: 'Application Type',
                            value: 'Web Application',
                            description: 'Type of OAuth application',
                          },
                          {
                            label: 'Authorization Grant',
                            value: 'Authorization Code with PKCE',
                            description: 'Recommended OAuth flow',
                          },
                        ].map((item) => (
                          <div key={item.label} className="space-y-2">
                            <label className="text-sm font-medium text-glacier-bluedark">
                              {item.label}:
                            </label>
                            <div className="bg-white p-2 rounded border text-sm">
                              {item.value}
                            </div>
                            <p className="text-xs text-slate-500">
                              {item.description}
                            </p>
                          </div>
                        ))}

                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <label className="text-sm font-medium text-glacier-bluedark">
                              Redirect URIs:
                            </label>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() =>
                                copyToClipboard(
                                  glacierEndpoints.development.redirectUris.join(
                                    '\n'
                                  ),
                                  'Redirect URIs (Dev)'
                                )
                              }
                              className="h-6 px-2 text-xs rounded-full"
                            >
                              {copiedText === 'Redirect URIs (Dev)' ? (
                                <CheckCircle className="w-3 h-3 text-glacier-green" />
                              ) : (
                                <Copy className="w-3 h-3" />
                              )}
                            </Button>
                          </div>
                          <div className="bg-white p-2 rounded border space-y-1">
                            {glacierEndpoints.development.redirectUris.map(
                              (uri) => (
                                <div
                                  key={uri}
                                  className="text-xs font-mono break-all"
                                >
                                  {uri}
                                </div>
                              )
                            )}
                          </div>
                          <p className="text-xs text-slate-500">
                            Development callback URLs for testing
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <Separator />

                {/* Required Scopes and Claims */}
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-glacier-bluedark">
                    Required Scopes and Claims
                  </h3>
                  <p className="text-slate-600 mb-4">
                    <strong>Scopes</strong> define what information the
                    application can access, while <strong>Claims</strong>
                    are the actual user information returned. Configure these in
                    your OAuth provider:
                  </p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <Card className="border-slate-100 bg-slate-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark">
                          Required Scopes
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {[
                            {
                              scope: 'openid',
                              description: 'Basic OIDC identity information',
                            },
                            {
                              scope: 'profile',
                              description:
                                'User profile information (name, etc.)',
                            },
                            {
                              scope: 'email',
                              description: 'User email address',
                            },
                            {
                              scope: 'groups',
                              description: 'User group memberships (optional)',
                            },
                          ].map((item) => (
                            <div
                              key={item.scope}
                              className="flex items-center space-x-3"
                            >
                              <Badge className="bg-glacier-bluedark text-white text-xs rounded-full">
                                {item.scope}
                              </Badge>
                              <span className="text-sm text-slate-600">
                                {item.description}
                              </span>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-slate-100 bg-slate-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark">
                          Required Claims
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {[
                            {
                              claim: 'sub',
                              description: 'Unique user identifier',
                              required: true,
                            },
                            {
                              claim: 'email',
                              description: 'User email address',
                              required: true,
                            },
                            {
                              claim: 'given_name',
                              description: 'User first name',
                              required: true,
                            },
                            {
                              claim: 'family_name',
                              description: 'User last name',
                              required: true,
                            },
                            {
                              claim: 'groups',
                              description: 'User groups/roles',
                              required: false,
                            },
                          ].map((item) => (
                            <div key={item.claim} className="space-y-1">
                              <div className="flex items-center space-x-3">
                                <Badge
                                  variant={
                                    item.required ? 'default' : 'secondary'
                                  }
                                  className={
                                    item.required
                                      ? 'bg-glacier-green text-white text-xs rounded-full'
                                      : 'bg-slate-200 text-slate-700 text-xs rounded-full'
                                  }
                                >
                                  {item.claim}
                                </Badge>
                                <span className="text-sm text-slate-600">
                                  {item.description}
                                </span>
                              </div>
                              {!item.required && (
                                <p className="text-xs text-slate-500 ml-16">
                                  Optional
                                </p>
                              )}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <Alert className="bg-glacier-blue/5 border-glacier-blue/20">
                  <AlertCircle className="h-4 w-4 text-glacier-blue" />
                  <AlertTitle className="text-glacier-bluedark">
                    OAuth Configuration Notes
                  </AlertTitle>
                  <AlertDescription className="text-slate-700">
                    <ul className="list-disc list-inside space-y-1 mt-2">
                      <li>
                        <strong>Client Authentication:</strong> Client Secret
                        (confidential client)
                      </li>
                      <li>
                        <strong>Token Endpoint Auth Method:</strong>{' '}
                        client_secret_post or client_secret_basic
                      </li>
                      <li>
                        <strong>Response Type:</strong> code (Authorization Code
                        flow)
                      </li>
                      <li>
                        <strong>PKCE:</strong> Required for enhanced security
                      </li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* SAML 2.0 Tab */}
          <TabsContent value="saml" className="space-y-6">
            <Card className="border-slate-200">
              <CardHeader>
                <CardTitle className="flex items-center text-glacier-bluedark">
                  <Shield className="w-6 h-6 mr-3 text-glacier-green" />
                  SAML 2.0 Configuration
                </CardTitle>
                <CardDescription className="text-slate-600">
                  <strong>What is SAML 2.0?</strong> Security Assertion Markup
                  Language (SAML) is an industry-standard protocol for
                  enterprise single sign-on. It allows your identity provider to
                  securely authenticate users and pass their information to
                  Glacier Climate Assistant. Most enterprise systems like Active
                  Directory Federation Services (ADFS), Azure AD, and Okta
                  support SAML 2.0.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Benefits explanation */}
                <Alert className="bg-glacier-green/5 border-glacier-green/20">
                  <CheckCircle className="h-4 w-4 text-glacier-green" />
                  <AlertTitle className="text-glacier-bluedark">
                    Why Choose SAML 2.0?
                  </AlertTitle>
                  <AlertDescription className="text-slate-700">
                    SAML 2.0 is ideal for organizations that need centralized
                    user management, strong security controls, and detailed
                    audit trails. It works especially well in corporate
                    environments where IT teams need to maintain strict control
                    over user access and authentication policies.
                  </AlertDescription>
                </Alert>

                {/* Service Provider Information */}
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-glacier-bluedark">
                    Glacier Climate Assistant Service Provider Details
                  </h3>
                  <p className="text-slate-600 mb-4">
                    <strong>Service Provider (SP)</strong> refers to Glacier
                    Climate Assistant in the SAML relationship. You'll need to
                    configure these endpoints in your Identity Provider:
                  </p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <Card className="border-slate-100 bg-slate-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark flex items-center">
                          <Globe className="w-5 h-5 mr-2 text-glacier-blue" />
                          Production Environment
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {[
                          {
                            label: 'Entity ID',
                            value: glacierEndpoints.production.entityId,
                            description:
                              'Unique identifier for Glacier in SAML',
                          },
                          {
                            label: 'ACS URL',
                            value: glacierEndpoints.production.acsUrl,
                            description: 'Where SAML responses are sent',
                          },
                          {
                            label: 'SLO URL',
                            value: glacierEndpoints.production.sloUrl,
                            description: 'Single logout endpoint',
                          },
                          {
                            label: 'Metadata URL',
                            value: glacierEndpoints.production.metadataUrl,
                            description: 'Service provider metadata',
                          },
                        ].map((item) => (
                          <div key={item.label} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <label className="text-sm font-medium text-glacier-bluedark">
                                {item.label}:
                              </label>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  copyToClipboard(item.value, item.label)
                                }
                                className="h-6 px-2 text-xs rounded-full"
                              >
                                {copiedText === item.label ? (
                                  <CheckCircle className="w-3 h-3 text-glacier-green" />
                                ) : (
                                  <Copy className="w-3 h-3" />
                                )}
                              </Button>
                            </div>
                            <div className="bg-white p-2 rounded border text-xs font-mono break-all">
                              {item.value}
                            </div>
                            <p className="text-xs text-slate-500">
                              {item.description}
                            </p>
                          </div>
                        ))}
                      </CardContent>
                    </Card>

                    <Card className="border-slate-100 bg-slate-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark flex items-center">
                          <Settings className="w-5 h-5 mr-2 text-glacier-greenmid" />
                          Development Environment
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        {[
                          {
                            label: 'Entity ID',
                            value: glacierEndpoints.development.entityId,
                            description: 'Testing environment identifier',
                          },
                          {
                            label: 'ACS URL',
                            value: glacierEndpoints.development.acsUrl,
                            description: 'Development callback URL',
                          },
                          {
                            label: 'SLO URL',
                            value: glacierEndpoints.development.sloUrl,
                            description: 'Development logout endpoint',
                          },
                          {
                            label: 'Metadata URL',
                            value: glacierEndpoints.development.metadataUrl,
                            description: 'Development metadata',
                          },
                        ].map((item) => (
                          <div key={item.label} className="space-y-2">
                            <div className="flex items-center justify-between">
                              <label className="text-sm font-medium text-glacier-bluedark">
                                {item.label}:
                              </label>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  copyToClipboard(
                                    item.value,
                                    `${item.label} (Dev)`
                                  )
                                }
                                className="h-6 px-2 text-xs rounded-full"
                              >
                                {copiedText === `${item.label} (Dev)` ? (
                                  <CheckCircle className="w-3 h-3 text-glacier-green" />
                                ) : (
                                  <Copy className="w-3 h-3" />
                                )}
                              </Button>
                            </div>
                            <div className="bg-white p-2 rounded border text-xs font-mono break-all">
                              {item.value}
                            </div>
                            <p className="text-xs text-slate-500">
                              {item.description}
                            </p>
                          </div>
                        ))}
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <Separator />

                {/* User Attributes */}
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-glacier-bluedark">
                    Required User Attributes
                  </h3>
                  <p className="text-slate-600 mb-4">
                    <strong>User Attributes</strong> (also called "Claims" or
                    "Assertions") are pieces of user information that your
                    identity provider sends to Glacier during login. These allow
                    us to identify the user and set up their account properly.
                  </p>

                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-slate-200 rounded-lg">
                      <thead>
                        <tr className="bg-glacier-bluedark text-white">
                          <th className="border border-slate-300 p-3 text-left font-medium">
                            Attribute Name
                          </th>
                          <th className="border border-slate-300 p-3 text-left font-medium">
                            SAML Claim
                          </th>
                          <th className="border border-slate-300 p-3 text-left font-medium">
                            Required
                          </th>
                          <th className="border border-slate-300 p-3 text-left font-medium">
                            Description
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white">
                        {[
                          {
                            name: 'Email Address',
                            claim:
                              'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress',
                            required: 'Yes',
                            description:
                              'Primary email address for user identification and communication',
                          },
                          {
                            name: 'First Name',
                            claim:
                              'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname',
                            required: 'Yes',
                            description:
                              "User's given name for profile display",
                          },
                          {
                            name: 'Last Name',
                            claim:
                              'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname',
                            required: 'Yes',
                            description:
                              "User's family name for profile display",
                          },
                          {
                            name: 'Unique User ID',
                            claim:
                              'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier',
                            required: 'Yes',
                            description:
                              'Permanent unique identifier (employee ID, UUID, etc.)',
                          },
                          {
                            name: 'User Groups/Roles',
                            claim:
                              'http://schemas.microsoft.com/ws/2008/06/identity/claims/groups',
                            required: 'Optional',
                            description:
                              'Group memberships for role-based access control',
                          },
                        ].map((attr, index) => (
                          <tr
                            key={index}
                            className={
                              index % 2 === 0 ? 'bg-slate-50' : 'bg-white'
                            }
                          >
                            <td className="border border-slate-200 p-3 font-medium text-glacier-bluedark">
                              {attr.name}
                            </td>
                            <td className="border border-slate-200 p-3 font-mono text-xs break-all">
                              {attr.claim}
                            </td>
                            <td className="border border-slate-200 p-3">
                              <Badge
                                variant={
                                  attr.required === 'Yes'
                                    ? 'default'
                                    : 'secondary'
                                }
                                className={
                                  attr.required === 'Yes'
                                    ? 'bg-glacier-green text-white'
                                    : 'bg-slate-200 text-slate-700'
                                }
                              >
                                {attr.required}
                              </Badge>
                            </td>
                            <td className="border border-slate-200 p-3 text-slate-600">
                              {attr.description}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                <Alert className="bg-glacier-blue/5 border-glacier-blue/20">
                  <AlertCircle className="h-4 w-4 text-glacier-blue" />
                  <AlertTitle className="text-glacier-bluedark">
                    Important Configuration Notes
                  </AlertTitle>
                  <AlertDescription className="text-slate-700">
                    <ul className="list-disc list-inside space-y-1 mt-2">
                      <li>
                        <strong>Name ID Format:</strong> Use "unspecified" or
                        "emailAddress" format
                      </li>
                      <li>
                        <strong>Signature Algorithm:</strong> RSA-SHA256
                        (minimum) or RSA-SHA512 (preferred)
                      </li>
                      <li>
                        <strong>Binding:</strong> HTTP-POST for both SSO and SLO
                      </li>
                      <li>
                        <strong>Certificate:</strong> X.509 certificate will be
                        provided during setup call
                      </li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Identity Providers Tab */}
          <TabsContent value="providers" className="space-y-6">
            <Card className="border-slate-200">
              <CardHeader>
                <CardTitle className="flex items-center text-glacier-bluedark">
                  <Server className="w-6 h-6 mr-3 text-glacier-green" />
                  Identity Provider Specific Guides
                </CardTitle>
                <CardDescription className="text-slate-600">
                  Detailed configuration instructions for popular identity
                  providers. Each provider has slightly different terminology
                  and setup steps, but the core concepts remain the same.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    {
                      name: 'Microsoft Azure AD / Entra ID',
                      protocols: ['SAML 2.0', 'OAuth/OIDC'],
                      commonFor:
                        'Organizations using Office 365, Teams, SharePoint',
                      setupTime: '15-30 minutes',
                      complexity: 'Medium',
                      notes:
                        'Most common enterprise provider. Excellent documentation and support.',
                    },
                    {
                      name: 'Google Workspace',
                      protocols: ['OAuth/OIDC', 'SAML 2.0'],
                      commonFor:
                        'Organizations using Gmail, Google Drive, Google Cloud',
                      setupTime: '10-20 minutes',
                      complexity: 'Easy',
                      notes:
                        'Straightforward setup. OAuth/OIDC is recommended for Google.',
                    },
                    {
                      name: 'Okta',
                      protocols: ['SAML 2.0', 'OAuth/OIDC'],
                      commonFor: 'Organizations with Okta as identity provider',
                      setupTime: '15-25 minutes',
                      complexity: 'Easy',
                      notes:
                        'Very user-friendly admin interface. Excellent for testing.',
                    },
                    {
                      name: 'ADFS (Active Directory)',
                      protocols: ['SAML 2.0'],
                      commonFor: 'On-premise Windows environments',
                      setupTime: '30-45 minutes',
                      complexity: 'Hard',
                      notes:
                        'Requires PowerShell commands. More complex certificate management.',
                    },
                    {
                      name: 'PingFederate',
                      protocols: ['SAML 2.0', 'OAuth/OIDC'],
                      commonFor: 'Large enterprises with hybrid cloud setups',
                      setupTime: '20-35 minutes',
                      complexity: 'Medium',
                      notes:
                        'Enterprise-grade features. Requires admin access to PingFederate.',
                    },
                    {
                      name: 'Auth0',
                      protocols: ['OAuth/OIDC', 'SAML 2.0'],
                      commonFor: 'Modern applications, SaaS companies',
                      setupTime: '10-15 minutes',
                      complexity: 'Easy',
                      notes:
                        'Developer-friendly. Excellent for OAuth/OIDC implementations.',
                    },
                  ].map((provider) => (
                    <Card
                      key={provider.name}
                      className="border-slate-100 hover:border-glacier-green/30 transition-colors"
                    >
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark">
                          {provider.name}
                        </CardTitle>
                        <div className="flex flex-wrap gap-1">
                          {provider.protocols.map((protocol) => (
                            <Badge
                              key={protocol}
                              variant="outline"
                              className="text-xs rounded-full border-glacier-blue text-glacier-blue"
                            >
                              {protocol}
                            </Badge>
                          ))}
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div>
                          <p className="text-sm font-medium text-glacier-bluedark">
                            Common for:
                          </p>
                          <p className="text-xs text-slate-600">
                            {provider.commonFor}
                          </p>
                        </div>
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium text-glacier-bluedark">
                              Setup Time:
                            </p>
                            <p className="text-xs text-slate-600">
                              {provider.setupTime}
                            </p>
                          </div>
                          <Badge
                            variant="outline"
                            className={`text-xs rounded-full ${
                              provider.complexity === 'Easy'
                                ? 'border-glacier-green text-glacier-green'
                                : provider.complexity === 'Medium'
                                  ? 'border-glacier-blue text-glacier-blue'
                                  : 'border-orange-500 text-orange-500'
                            }`}
                          >
                            {provider.complexity}
                          </Badge>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-glacier-bluedark">
                            Notes:
                          </p>
                          <p className="text-xs text-slate-600">
                            {provider.notes}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Alert className="mt-6 bg-glacier-green/5 border-glacier-green/20">
                  <Info className="h-4 w-4 text-glacier-green" />
                  <AlertTitle className="text-glacier-bluedark">
                    Need Help with Your Provider?
                  </AlertTitle>
                  <AlertDescription className="text-slate-700">
                    Don't see your identity provider listed? No problem! We
                    support any provider that implements SAML 2.0 or OAuth/OIDC
                    standards. Contact our team for provider-specific guidance.
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Contact Tab */}
          <TabsContent value="contact" className="space-y-6">
            <Card className="border-slate-200">
              <CardHeader>
                <CardTitle className="flex items-center text-glacier-bluedark">
                  <Mail className="w-6 h-6 mr-3 text-glacier-green" />
                  Contact Information & Next Steps
                </CardTitle>
                <CardDescription className="text-slate-600">
                  Ready to set up SSO? Our technical team will guide you through
                  the process. Here's what to expect and how to get in touch.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Process Overview */}
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-glacier-bluedark">
                    What Happens Next?
                  </h3>
                  <div className="space-y-4">
                    {[
                      {
                        step: '1',
                        title: 'Initial Contact',
                        description:
                          'Send us an email with your organization details and preferred protocol',
                        duration: 'Immediate',
                      },
                      {
                        step: '2',
                        title: 'Technical Consultation',
                        description:
                          'We schedule a 30-minute call to review your identity provider setup',
                        duration: 'Within 24 hours',
                      },
                      {
                        step: '3',
                        title: 'Development Setup',
                        description:
                          'We configure SSO in our development environment for testing',
                        duration: '2-3 business days',
                      },
                      {
                        step: '4',
                        title: 'Testing & Validation',
                        description:
                          'You test the integration with a few users in the dev environment',
                        duration: '1-2 business days',
                      },
                      {
                        step: '5',
                        title: 'Production Deployment',
                        description:
                          'We deploy the configuration to production and provide user guides',
                        duration: '1 business day',
                      },
                    ].map((phase) => (
                      <div
                        key={phase.step}
                        className="flex items-start space-x-4 p-4 border border-slate-200 rounded-lg"
                      >
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-glacier-bluedark rounded-full flex items-center justify-center text-white font-bold text-sm">
                            {phase.step}
                          </div>
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold text-glacier-bluedark">
                              {phase.title}
                            </h4>
                            <Badge
                              variant="outline"
                              className="text-xs rounded-full border-glacier-green text-glacier-green"
                            >
                              <Clock className="w-3 h-3 mr-1" />
                              {phase.duration}
                            </Badge>
                          </div>
                          <p className="text-sm text-slate-600">
                            {phase.description}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Information to Prepare */}
                <div>
                  <h3 className="text-xl font-semibold mb-4 text-glacier-bluedark">
                    Information to Prepare Before Contacting Us
                  </h3>
                  <p className="text-slate-600 mb-4">
                    To expedite the setup process, please gather this
                    information before reaching out. If you're not sure about
                    any of these details, that's okay – we can help you find
                    them during our call.
                  </p>

                  <div className="grid md:grid-cols-2 gap-6">
                    <Card className="border-slate-100 bg-slate-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark flex items-center">
                          <Users className="w-5 h-5 mr-2 text-glacier-blue" />
                          Organization Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-glacier-bluedark">
                            Company Name:
                          </label>
                          <div className="bg-white p-2 rounded border text-sm text-slate-500">
                            Your organization's legal name
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-glacier-bluedark">
                            Primary Domain:
                          </label>
                          <div className="bg-white p-2 rounded border text-sm text-slate-500">
                            e.g., yourcompany.com
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-glacier-bluedark">
                            Number of Users:
                          </label>
                          <div className="bg-white p-2 rounded border text-sm text-slate-500">
                            Approximate count for license planning
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-glacier-bluedark">
                            IT Contact:
                          </label>
                          <div className="bg-white p-2 rounded border text-sm text-slate-500">
                            Name and email of IT administrator
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-slate-100 bg-slate-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-lg text-glacier-bluedark flex items-center">
                          <Settings className="w-5 h-5 mr-2 text-glacier-greenmid" />
                          Technical Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-glacier-bluedark">
                            Identity Provider:
                          </label>
                          <div className="bg-white p-2 rounded border text-sm text-slate-500">
                            e.g., Azure AD, Okta, ADFS, Google
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-glacier-bluedark">
                            Preferred Protocol:
                          </label>
                          <div className="bg-white p-2 rounded border text-sm text-slate-500">
                            SAML 2.0 or OAuth/OIDC (if known)
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-glacier-bluedark">
                            Environment Preference:
                          </label>
                          <div className="bg-white p-2 rounded border text-sm text-slate-500">
                            Start with dev or go directly to production
                          </div>
                        </div>
                        <div className="space-y-2">
                          <label className="text-sm font-medium text-glacier-bluedark">
                            Timeline:
                          </label>
                          <div className="bg-white p-2 rounded border text-sm text-slate-500">
                            When do you need SSO to be live?
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                <Separator />

                {/* Contact Information */}
                <div className="text-center">
                  <h3 className="text-xl font-semibold mb-6 text-glacier-bluedark">
                    Ready to Get Started?
                  </h3>

                  <div className="grid md:grid-cols-3 gap-6 mb-8">
                    <Card className="border-glacier-green/20 hover:border-glacier-green/40 transition-colors">
                      <CardContent className="pt-6 text-center">
                        <Mail className="w-8 h-8 mx-auto mb-3 text-glacier-green" />
                        <h4 className="font-semibold text-glacier-bluedark mb-2">
                          Email Support
                        </h4>
                        <p className="text-sm text-slate-600 mb-3">
                          Send us your requirements and we'll respond within 24
                          hours
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            window.open(
                              'mailto:<EMAIL>?subject=SSO Setup Request&body=Please include:%0A- Company name%0A- Identity provider (if known)%0A- Preferred timeline%0A- Number of users',
                              '_blank'
                            )
                          }
                          className="border-glacier-green text-glacier-green hover:bg-glacier-green hover:text-white rounded-full"
                        >
                          <EMAIL>
                          <ExternalLink className="w-3 h-3 ml-2" />
                        </Button>
                      </CardContent>
                    </Card>

                    {/* <Card className="border-glacier-blue/20 hover:border-glacier-blue/40 transition-colors">
                      <CardContent className="pt-6 text-center">
                        <Phone className="w-8 h-8 mx-auto mb-3 text-glacier-blue" />
                        <h4 className="font-semibold text-glacier-bluedark mb-2">
                          Phone Support
                        </h4>
                        <p className="text-sm text-slate-600 mb-3">
                          Call for urgent SSO requirements or complex setups
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            window.open('tel:+15551234567', '_blank')
                          }
                          className="border-glacier-blue text-glacier-blue hover:bg-glacier-blue hover:text-white rounded-full"
                        >
                          +****************
                          <ExternalLink className="w-3 h-3 ml-2" />
                        </Button>
                      </CardContent>
                    </Card> */}

                    {/* <Card className="border-glacier-greenmid/20 hover:border-glacier-greenmid/40 transition-colors">
                      <CardContent className="pt-6 text-center">
                        <FileText className="w-8 h-8 mx-auto mb-3 text-glacier-greenmid" />
                        <h4 className="font-semibold text-glacier-bluedark mb-2">
                          Documentation
                        </h4>
                        <p className="text-sm text-slate-600 mb-3">
                          Access detailed technical documentation and guides
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            window.open(
                              'https://docs.glacier.eco/sso',
                              '_blank'
                            )
                          }
                          className="border-glacier-greenmid text-glacier-greenmid hover:bg-glacier-greenmid hover:text-white rounded-full"
                        >
                          docs.glacier.eco
                          <ExternalLink className="w-3 h-3 ml-2" />
                        </Button>
                      </CardContent>
                    </Card> */}
                  </div>

                  <Alert className="bg-glacier-bluedark/5 border-glacier-bluedark/20 text-left">
                    <Shield className="h-4 w-4 text-glacier-bluedark" />
                    <AlertTitle className="text-glacier-bluedark">
                      Security & Privacy Commitment
                    </AlertTitle>
                    <AlertDescription className="text-slate-700">
                      We take security seriously and follow industry best
                      practices for SSO implementations. All communications are
                      encrypted, test environments are isolated, and we provide
                      comprehensive security documentation upon request. Your
                      identity provider credentials and configurations are never
                      stored on our systems.
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};
