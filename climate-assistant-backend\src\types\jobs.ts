export enum JobProcessor {
  ChunkExtraction = 'chunk-extraction',
  ChunkDpLinking = 'chunk-dp-linking',
  DatapointGeneration = 'datapoint-generation',
  DatapointReview = 'datapoint-review',
  LlmRequest = 'llm-request',
}

export enum JobQueue {
  ChunkExtract = 'chunk-extract',
  ChunkDpLink = 'chunk-dp-link',
  DatapointGenerate = 'datapoint-generate',
  DatapointReview = 'datapoint-review',
  LlmRequest = 'llm-request',
}
