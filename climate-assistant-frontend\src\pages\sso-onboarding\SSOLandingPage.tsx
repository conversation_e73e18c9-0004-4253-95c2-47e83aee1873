import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Shield,
  Users,
  ArrowRight,
  CheckCircle,
  Building2,
  Lock,
  Zap,
  Globe,
  Clock,
  UserCheck,
  Settings,
  ArrowLeft,
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export const SSOLandingPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto py-16 px-4 max-w-6xl">
        {/* Back to Login Button */}
        <div className="mb-8">
          <Button
            variant="ghost"
            onClick={() => navigate('/sso-login')}
            className="text-glacier-bluedark hover:bg-glacier-green/10 hover:text-glacier-bluedark"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Login
          </Button>
        </div>

        {/* Header */}
        <div className="text-center mb-16">
          <Badge
            variant="secondary"
            className="px-4 py-2 mb-6 bg-glacier-bluedark text-white rounded-full"
          >
            <Shield className="w-4 h-4 mr-2" />
            Enterprise Single Sign-On
          </Badge>
          <h1 className="text-5xl font-bold mb-8 text-glacier-bluedark font-pantea">
            Streamline Access with
            <span className="text-glacier-green"> Your Identity Provider</span>
          </h1>

          {/* Simple explanation for non-technical users */}
          <div className="mb-8 p-6 bg-slate-50 rounded-lg border max-w-4xl mx-auto">
            <h2 className="text-xl font-semibold mb-4 text-glacier-bluedark">
              What is Single Sign-On (SSO)?
            </h2>
            <p className="text-lg text-slate-700 mb-4">
              SSO allows your team to access Glacier Climate Assistant using the
              same login they already use for other business applications like
              Microsoft 365, Google Workspace, or your company's internal
              systems.
            </p>
            <div className="grid md:grid-cols-2 gap-4 text-left">
              <div className="flex items-start space-x-3">
                <UserCheck className="w-5 h-5 text-glacier-green mt-1 flex-shrink-0" />
                <div>
                  <p className="font-medium text-glacier-bluedark">
                    For Your Team
                  </p>
                  <p className="text-sm text-slate-600">
                    One login for all business tools - no more password fatigue
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Shield className="w-5 h-5 text-glacier-green mt-1 flex-shrink-0" />
                <div>
                  <p className="font-medium text-glacier-bluedark">
                    For Your IT Team
                  </p>
                  <p className="text-sm text-slate-600">
                    Centralized user management and enhanced security control
                  </p>
                </div>
              </div>
            </div>
          </div>

          <Button
            size="lg"
            onClick={() => navigate('/sso-setup')}
            className="bg-glacier-bluedark hover:bg-glacier-bluedark/90 text-white rounded-full px-8 h-12 text-lg"
          >
            Get Started with SSO Setup
            <ArrowRight className="ml-2 w-5 h-5" />
          </Button>
        </div>

        {/* Benefits Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12 text-glacier-bluedark">
            Why Organizations Choose SSO
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-slate-200">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 w-16 h-16 bg-glacier-green/10 rounded-full flex items-center justify-center">
                  <Shield className="w-8 h-8 text-glacier-green" />
                </div>
                <CardTitle className="text-xl text-glacier-bluedark">
                  Enhanced Security
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center text-slate-600">
                  Reduce security risks with centralized authentication,
                  multi-factor authentication, and immediate access revocation
                  when employees leave. Your IT policies apply consistently
                  across all applications.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="border-slate-200">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 w-16 h-16 bg-glacier-blue/10 rounded-full flex items-center justify-center">
                  <Zap className="w-8 h-8 text-glacier-blue" />
                </div>
                <CardTitle className="text-xl text-glacier-bluedark">
                  Improved Productivity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center text-slate-600">
                  Eliminate password fatigue and login friction. Your team
                  spends more time on climate reporting and less time managing
                  passwords or waiting for IT support to reset accounts.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="border-slate-200">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 w-16 h-16 bg-glacier-greenmid/10 rounded-full flex items-center justify-center">
                  <Building2 className="w-8 h-8 text-glacier-greenmid" />
                </div>
                <CardTitle className="text-xl text-glacier-bluedark">
                  Compliance & Control
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-center text-slate-600">
                  Meet enterprise compliance requirements with centralized user
                  provisioning, deprovisioning, and audit trails. Maintain full
                  visibility and control over who accesses your climate data.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Supported Providers */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-8 text-glacier-bluedark">
            Supported Identity Providers
          </h2>
          <p className="text-center text-lg text-slate-600 mb-12 max-w-3xl mx-auto">
            Glacier Climate Assistant integrates with leading enterprise
            identity providers. If you're using any of these systems, SSO setup
            is straightforward and well-supported.
          </p>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                name: 'Microsoft Azure AD / Entra ID',
                icon: Building2,
                description: 'Office 365, Teams integration',
              },
              {
                name: 'Google Workspace',
                icon: Globe,
                description: 'Gmail, Google Drive users',
              },
              {
                name: 'Okta',
                icon: Shield,
                description: 'Enterprise identity platform',
              },
              {
                name: 'ADFS / PingFederate',
                icon: Lock,
                description: 'On-premise and hybrid setups',
              },
            ].map((provider) => (
              <Card
                key={provider.name}
                className="border-slate-200 hover:border-glacier-green/30 transition-colors"
              >
                <CardHeader className="text-center pb-2">
                  <provider.icon className="w-8 h-8 mx-auto mb-2 text-glacier-bluedark" />
                  <CardTitle className="text-sm font-medium text-glacier-bluedark">
                    {provider.name}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <CardDescription className="text-xs text-center text-slate-500">
                    {provider.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <p className="text-slate-600">
              Don't see your provider?{' '}
              <span className="text-glacier-blue font-medium">
                Most SAML 2.0 and OAuth/OIDC providers are supported.
              </span>
            </p>
          </div>
        </div>

        {/* Implementation Timeline */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12 text-glacier-bluedark">
            Simple Implementation Process
          </h2>
          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {[
                {
                  step: '1',
                  title: 'Initial Consultation',
                  description:
                    'We discuss your identity provider setup and requirements',
                  duration: '30 minutes',
                  icon: Users,
                },
                {
                  step: '2',
                  title: 'Configuration & Testing',
                  description:
                    'Our team configures SSO and tests it in your development environment',
                  duration: '3-5 business days',
                  icon: Settings,
                },
                {
                  step: '3',
                  title: 'Production Deployment',
                  description:
                    'We deploy to production and provide user training materials',
                  duration: '1-2 business days',
                  icon: CheckCircle,
                },
              ].map((phase) => (
                <div key={phase.step} className="flex items-start space-x-6">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-glacier-bluedark rounded-full flex items-center justify-center text-white font-bold text-lg">
                      {phase.step}
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-2">
                      <h3 className="text-xl font-semibold text-glacier-bluedark">
                        {phase.title}
                      </h3>
                      <Badge
                        variant="outline"
                        className="text-xs rounded-full border-glacier-green text-glacier-green"
                      >
                        <Clock className="w-3 h-3 mr-1" />
                        {phase.duration}
                      </Badge>
                    </div>
                    <p className="text-slate-600 mb-4">{phase.description}</p>
                  </div>
                  <phase.icon className="w-6 h-6 text-glacier-green flex-shrink-0 mt-2" />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-slate-50 rounded-xl p-12">
          <h2 className="text-3xl font-bold mb-4 text-glacier-bluedark">
            Ready to Get Started?
          </h2>
          <p className="text-lg text-slate-600 mb-8 max-w-2xl mx-auto">
            Our technical setup guide provides all the information your IT team
            needs to configure SSO with Glacier Climate Assistant. The process
            is straightforward and well-documented.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => navigate('/sso-setup')}
              size="lg"
              className="bg-glacier-bluedark hover:bg-glacier-bluedark/90 text-white rounded-full px-8"
            >
              View Technical Setup Guide
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() =>
                window.open(
                  'mailto:<EMAIL>?subject=SSO Integration Inquiry',
                  '_blank'
                )
              }
              className="border-glacier-bluedark text-glacier-bluedark hover:bg-glacier-bluedark hover:text-white rounded-full px-8"
            >
              Contact Our Team
            </Button>
          </div>

          <div className="mt-8 pt-8 border-t border-slate-200">
            <p className="text-sm text-slate-500">
              Questions? Email us at{' '}
              <span className="text-glacier-blue font-medium">
                <EMAIL>
              </span>{' '}
              or call{' '}
              <span className="text-glacier-blue font-medium">
                +1 (555) 123-4567
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
