# SSO with WorkOS - Quick Reference Guide

## **Our Current Implementation: WorkOS Standalone SSO** 🔧

### **What We're Using:**
```typescript
// We use WorkOS SSO APIs directly
const authorizationUrl = this.workos.sso.getAuthorizationUrl({
  organization: workspace.ssoConfig.workosOrganizationId,
  clientId: workosConfig.clientId,
  redirectUri: workosConfig.redirectUri,
  state,
});

const { profile } = await this.workos.sso.getProfileAndToken({
  code,
  clientId: workosConfig.clientId,
});
```

### **Key Characteristics of Our Implementation:**

1. **✅ Custom Authentication UI**: We built our own login pages (`/login`, `/sso-login`)
2. **✅ Direct API Integration**: We call WorkOS APIs directly (`workos.sso.*`)
3. **✅ Custom State Management**: We handle JWT tokens and cookies ourselves
4. **✅ Custom User Provisioning**: We manage user creation and workspace assignment
5. **✅ Custom Session Management**: We use our own JWT tokens and session handling

---

## **WorkOS AuthKit vs Standalone SSO Comparison**

| Feature | **AuthKit** | **Standalone SSO** (Our Setup) |
|---------|-------------|--------------------------------|
| **UI Components** | Pre-built login/signup widgets | ✅ Custom UI (our React components) |
| **Session Management** | Managed by WorkOS | ✅ Custom JWT tokens |
| **User Management** | WorkOS-hosted user database | ✅ Our own user database |
| **Customization** | Limited theme customization | ✅ Full control over UX |
| **Integration** | Drop-in widgets | ✅ API-based integration |
| **Complexity** | Lower (managed service) | ✅ Higher (we handle everything) |
| **Control** | Less control | ✅ Full control |

---

## **Why We Chose Standalone SSO** 🎯

1. **✅ Full Brand Control**: Our custom UI matches Glacier's design
2. **✅ Complex Business Logic**: Multi-workspace user management
3. **✅ Existing Auth System**: Integration with existing password-based auth
4. **✅ Custom User Flow**: Specific onboarding and permission systems
5. **✅ Advanced Features**: Custom redirect validation, workspace-scoped SSO

---

## **What AuthKit Would Look Like** (If We Used It)

```typescript
// AuthKit approach (we DON'T use this)
import { WorkOSAuthKit } from '@workos-inc/authkit-nextjs';

// Would use hosted login pages
const loginUrl = 'https://auth.workos.com/login?client_id=...';

// Would get sessions from WorkOS
const session = await WorkOSAuthKit.getSession();
```

---

## **Benefits of Our Standalone Approach** ✅

1. **Complete Control**: We own the entire user experience
2. **Flexibility**: Can implement complex workspace logic
3. **Security**: Full control over authentication flow and validation
4. **Integration**: Seamless with existing Glacier permission system
5. **Customization**: Can add features like domain-based SSO detection
6. **Performance**: Optimized domain lookups with indexed database structure

## **🚀 Performance Optimizations**

### **Domain Lookup Performance:**
```typescript
// Fast indexed domain lookup O(log n)
const ssoConfig = await workspaceSSORepository.findOne({
  where: { domain: normalizedDomain, enabled: true }
});
```

| Operation | Implementation | Performance | Benefits |
|-----------|----------------|-------------|----------|
| **Domain Check** | Indexed lookup | O(log n) | **Lightning fast** |
| **Multi-domain** | Separate table rows | Scalable | **Enterprise ready** |
| **Management** | Standard CRUD | Maintainable | **Developer friendly** |

## **⚡ Super Admin Workspace Creation**

### **New SSO-Enabled Workspace Creation:**
```typescript
// Create workspace with SSO in one step
const workspace = await createWorkspace({
  name: "Acme Corp",
  email: "<EMAIL>",
  password: "secure123",
  ssoConfig: {
    enabled: true,
    domain: "acme.com",
    protocol: "saml",
    workosOrganizationId: "org_123",
    workosConnectionId: "conn_456"
  }
});
// ✅ Automatically creates WorkspaceSSO record
// ✅ Sets up indexed domain lookup
// ✅ Ready for enterprise SSO immediately
```

### **Benefits:**
- ✅ **Streamlined Setup**: SSO configuration during workspace creation
- ✅ **Real-time Validation**: WorkOS ID format checking (`org_`, `conn_`)
- ✅ **Enterprise Ready**: Supports SAML 2.0, OAuth 2.0, OpenID Connect
- ✅ **Admin Friendly**: Visual feedback and helpful error messages

## **Trade-offs** ⚖️

1. **More Code**: We maintain more authentication logic
2. **Security Responsibility**: We handle security implementation
3. **Updates**: We need to maintain WorkOS SDK updates

---


## 🚀 Quick Start

### Environment Setup
```bash
# Backend .env
WORKOS_API_KEY=wk_live_xxxxxxxxxxxx
WORKOS_CLIENT_ID=client_xxxxxxxxxxxx
WORKOS_REDIRECT_URI=https://app.glacier.eco/api/auth/sso/callback
WORKOS_WEBHOOK_SECRET=wh_secret_xxxxxxxxxxxx

# Frontend .env (optional)
VITE_WORKOS_CLIENT_ID=client_xxxxxxxxxxxx
VITE_SSO_ENABLED=true
```

### Database Migration
```bash
cd climate-assistant-backend
npm run migration:run
```

## 📚 Key Files & Locations

### Backend
```
src/auth/
├── sso.service.ts                    # Main SSO service (OPTIMIZED)
├── webhook-processor.service.ts     # Async webhook handling
├── auth.service.ts                  # Updated with SSO methods
├── auth.controller.ts               # SSO + SAML endpoints
├── auth.dto.ts                      # SSO DTOs + Workspace creation
└── auth.module.ts                   # Module configuration

src/users/
├── entities/user.entity.ts          # Updated with SSO fields
├── users.service.ts                 # SSO user + workspace creation
├── users.controller.ts              # Enhanced /create-workspace endpoint
└── users.module.ts                  # Includes WorkspaceSSO entity

src/workspace/
├── entities/workspace.entity.ts     # Basic SSO config (JSON)
└── entities/workspace-sso.entity.ts # SSO domain management (indexed)

src/util/
└── config.ts                        # WorkOS configuration

src/database/migrations/
└── 1700000000000-add-workspace-sso-table.ts # SSO domain schema
```

### Frontend
```
src/api/
├── authentication/authentication.api.ts     # SSO API functions
└── workspace-settings/workspace-settings.api.ts # Enhanced workspace creation

src/components/login/
└── SSOLoginButton.tsx                        # SSO button component

src/pages/login/
├── LoginForm.tsx                             # Updated with SSO detection
├── SSOLogin.tsx                              # Dedicated SSO login page
└── SSOCallback.tsx                           # SSO callback handler

src/pages/dashboard/settings/
└── SuperAdminSettings.tsx                    # SSO workspace creation UI

src/pages/sso-onboarding/
├── SSOLandingPage.tsx                        # Public SSO landing page
└── SSOOnboardingGuide.tsx                    # Technical setup guide

src/components/router/
└── Routes.tsx                                # Updated with SSO routes
```

## 🔧 Common Operations

### SSO Flow Types Support

Our implementation supports both **SP-initiated** and **IdP-initiated** SSO flows:

```typescript
// SP-initiated (Service Provider initiated)
1. User starts at your app → clicks SSO login
2. App generates JWT state with workspace info
3. Redirects to IdP with state parameter  
4. IdP authenticates → redirects back WITH state
5. App verifies state → extracts workspace from JWT

// IdP-initiated (Identity Provider initiated) ✨ NEW
1. User starts at IdP dashboard → clicks your app
2. IdP authenticates → redirects WITHOUT state 
3. App gets profile → finds workspace by organizationId
4. App provisions user and logs them in
```

**Benefits:**
- ✅ **Flexible Login**: Users can start from either app or IdP
- ✅ **Enterprise UX**: Natural workflow from corporate dashboards
- ✅ **WorkOS Test IdP**: Perfect for development and testing
- ✅ **Backward Compatible**: Existing SP-initiated flows unchanged

### **SSO User Invitation System** 🎟️

#### **Invitation-Only Mode (Secure Workspaces)**
```sql
-- Disable auto-provisioning for client workspaces
UPDATE workspace SET "allowAutoProvisioning" = false WHERE id = 'client-workspace-id';
```

#### **Invite SSO Users**
```typescript
// Admin invites user (works for both SSO and password users)
POST /api/workspace/inviteUsers
{
  emails: ["<EMAIL>"],
  role: "Admin",  // Specific role assignment
  workspaceId: "workspace-uuid"
}

// System auto-detects SSO and sends appropriate email:
// ✅ SSO domains → SSO invitation (links to /sso-login?invited=true)
// ✅ Non-SSO domains → Password setup invitation
```

#### **Auto-Provisioning Mode (Open Workspaces)**
```sql
-- Enable auto-provisioning for internal teams
UPDATE workspace SET "allowAutoProvisioning" = true WHERE id = 'internal-workspace-id';
```

#### **Hybrid Access Control**
```typescript
// Configuration options per workspace:
interface AccessControl {
  allowAutoProvisioning: boolean;
  // true: Any domain user can self-provision (default role)
  // false: Only invited users can access (assigned role)
}

// Examples:
// Internal team workspace: allowAutoProvisioning = true
// Client project workspace: allowAutoProvisioning = false
```

### Create SSO-Enabled Workspace (Super Admin)
```typescript
// Frontend: SuperAdminSettings.tsx
const ssoConfig = {
  enabled: true,
  domain: "acme.com",
  protocol: "saml",
  workosOrganizationId: "org_123",
  workosConnectionId: "conn_456"
};

const workspace = await createWorkspace({
  name: "Acme Corp",
  email: "<EMAIL>", 
  password: "secure123",
  ssoConfig
});
```

### Enable SSO for Existing Workspace
```sql
UPDATE workspace 
SET 
  sso_enabled = true,
  sso_config = jsonb_build_object(
    'workosOrganizationId', 'org_xxxxxxxxxxxx',
    'allowedDomains', ARRAY['company.com'],
    'defaultRoleId', (SELECT id FROM role WHERE name = 'CONTRIBUTOR'),
    'autoProvision', true,
    'requireSso', false
  )
WHERE id = 'workspace-uuid';
```

### Check Domain SSO Status
```typescript
const { hasSso, workspaceId } = await checkDomainSSO('company.com');
```

### Initiate SSO Login
```typescript
const response = await initiateSSOLogin(workspaceId, '/dashboard');
window.location.href = response.authorizationUrl;
```

### Link Existing Account to SSO
```typescript
await linkAccountToSSO({
  ssoUserId: 'sso_user_xxxxxxxxxxxx',
  ssoProviderId: 'conn_xxxxxxxxxxxx'
});
```

## 🐛 Quick Debugging

### Test Both SSO Flow Types

#### **SP-Initiated Testing:**
```bash
# 1. Use your app's SSO login button
# 2. Should include state parameter in callback
# Expected log: "SP-initiated flow"
curl -X POST "https://app.glacier.eco/api/auth/sso/init" \
  -H "Content-Type: application/json" \
  -d '{"workspaceId": "workspace-id"}'
```

#### **IdP-Initiated Testing:**
```bash
# 1. Use WorkOS Test IdP or corporate dashboard
# 2. Click your app from IdP dashboard  
# 3. No state parameter in callback
# Expected callback: /api/auth/sso/callback?code=abc123 (no state)
# Expected log: "IdP-initiated flow"
```

### Check SSO Configuration
```sql
-- Verify workspace SSO setup
SELECT w.id, w.name, w.sso_enabled, w.sso_config->>'workosOrganizationId' as org_id
FROM workspace w 
WHERE w.sso_enabled = true;

-- Check optimized domain configuration  
SELECT domain, protocol, enabled, workos_organization_id, workspace_id
FROM workspace_sso 
WHERE enabled = true;

-- Debug organization ID mapping (critical for IdP-initiated)
SELECT ws.domain, ws.workos_organization_id, w.name as workspace_name
FROM workspace_sso ws
JOIN workspace w ON w.id = ws.workspace_id
WHERE ws.enabled = true;
```

### View SSO Users
```sql
SELECT id, email, auth_method, sso_provider_id, last_sso_login 
FROM "user" 
WHERE auth_method = 'sso';
```

### Test Domain Check
```bash
curl -X GET "https://app.glacier.eco/api/auth/sso/check-domain/company.com"
```

## 🔐 Security Checklist

- ✅ HTTPS enabled for production
- ✅ WorkOS webhook secret configured
- ✅ Redirect URI matches production domain
- ✅ Rate limiting enabled
- ✅ Domain restrictions configured
- ✅ JWT secret properly set
- ✅ Cookies set as httpOnly and secure

## 🚨 Common Issues & Solutions

| Issue | Solution |
|-------|----------|
| SSO button not showing | Check domain in workspace `allowedDomains` |
| "SSO not enabled" error | Set `sso_enabled = true` in workspace |
| Callback redirect loops | Verify JWT_SECRET and state parameter |
| Webhook signature invalid | Check WORKOS_WEBHOOK_SECRET matches dashboard |
| Rate limit exceeded | Review throttling configuration |

## 📊 API Endpoints Reference

| Method | Endpoint | Auth Required | Rate Limit |
|--------|----------|---------------|------------|
| POST | `/auth/sso/init` | No | 5/min |
| GET | `/auth/sso/callback` | No | None |
| GET | `/auth/sso/check-domain/:domain` | No | 10/min |
| POST | `/auth/sso/link` | Yes | None |
| GET | `/auth/sso/providers/:workspaceId` | Yes | None |
| POST | `/auth/webhooks/workos` | No | 100/min |

## 💡 Pro Tips

1. **Development**: Use ngrok for local WorkOS webhook testing
2. **Testing**: Test with multiple identity providers (Google, Microsoft, Okta)
3. **Monitoring**: Watch for SSO_EVENT logs for audit trails
4. **Performance**: Cache workspace SSO configs in Redis
5. **Security**: Regular rotation of WorkOS API keys
6. **UX**: Test email domain detection with various formats

## 🔗 Useful Links

- [WorkOS Documentation](https://workos.com/docs)
- [WorkOS Dashboard](https://dashboard.workos.com)
- [Implementation Plan](./sso-workos-implementation-plan.md)
- [Technical Documentation](./sso-workos-technical-documentation.md)

## 📞 Support

For implementation questions or issues:
1. Check troubleshooting section in technical docs
2. Review error logs with SSO_EVENT pattern
3. Test with WorkOS dashboard tools
4. Verify environment configuration 