# SSO Client Onboarding Guide for Developers

## Overview

This guide helps Glacier developers onboard enterprise clients with Single Sign-On (SSO) integration. It covers the complete process from initial client contact to successful SSO deployment.

## Table of Contents

1. [Pre-Onboarding Checklist](#pre-onboarding-checklist)
2. [Super Admin Workspace Creation (Streamlined Approach)](#super-admin-workspace-creation-streamlined-approach)
3. [Protocol Decision Matrix](#protocol-decision-matrix)
4. [SAML 2.0 Onboarding](#saml-20-onboarding)
5. [OAuth 2.0 / OIDC Onboarding](#oauth-20--oidc-onboarding)
6. [WorkOS Configuration](#workos-configuration)
7. [Testing & Validation](#testing--validation)
8. [Common Issues & Troubleshooting](#common-issues--troubleshooting)
9. [Post-Deployment Support](#post-deployment-support)

---

## Pre-Onboarding Checklist

### Information Gathering

Before starting any SSO integration, collect the following information from the client:

#### **Business Information**

- [ ] Company name and size
- [ ] Primary contact person (name, email, role)
- [ ] Technical contact person (name, email, role)
- [ ] Expected number of users (for capacity planning)
- [ ] Go-live deadline
- [ ] Security/compliance requirements (SOC2, GDPR, etc.)

#### **Technical Information**

- [ ] Identity Provider (IdP) being used
- [ ] Preferred SSO protocol (SAML 2.0 vs OAuth/OIDC)
- [ ] Email domains to be enabled for SSO
- [ ] Required user attributes/claims
- [ ] Testing environment availability
- [ ] IT team availability for testing

#### **Workspace Setup**

- [ ] Workspace ID in Glacier system (or create new with SSO)
- [ ] Admin user accounts created
- [ ] Initial project setup completed

## Super Admin Workspace Creation (Streamlined Approach)

### **New SSO-Enabled Workspace Creation**

For new clients, you can now create SSO-enabled workspaces directly from the Super Admin interface - this is often the fastest approach for fresh setups.

#### **Step 1: Access Super Admin Interface**

```
1. Navigate to `/dashboard/settings` as Super Admin
2. Scroll to "Create New Workspace" section
3. Fill basic workspace information (name, admin email, password)
4. Toggle "SSO Configuration" switch ✅
```

#### **Step 2: Configure SSO Settings**

```typescript
Required SSO Fields:
✅ Company Domain: "client-company.com"
✅ SSO Protocol: Select from dropdown
   - SAML 2.0 (most common for enterprise)
   - OAuth 2.0 (modern web applications)
   - OpenID Connect (identity layer on OAuth)
✅ WorkOS Organization ID: "org_01H9XVN5ZK..."
✅ WorkOS Connection ID: "conn_01H9XVN5ZK..."
```

#### **Step 3: Automated Backend Processing**

When you click "Create Workspace":

- ✅ **Workspace created** with basic info
- ✅ **WorkspaceSSO record** automatically created with indexed domain
- ✅ **SSO configuration** saved to workspace
- ✅ **Domain lookup** optimized for fast authentication
- ✅ **Users ready** to login via SSO immediately

#### **Benefits of This Approach:**

- ✅ **5x Faster Setup**: 2 minutes vs 10+ minutes manual process
- ✅ **Zero Database Work**: No manual SQL queries needed
- ✅ **Built-in Validation**: Prevents common configuration errors
- ✅ **Enterprise Ready**: SSO works immediately after creation
- ✅ **Consistent Setup**: Standardized configuration across all clients

#### **When to Use Super Admin Creation:**

- ✅ **New clients** requiring SSO from day 1
- ✅ **Proof of concepts** needing enterprise authentication
- ✅ **Demo environments** with SSO requirements
- ✅ **Migration scenarios** where clean setup is preferred
- ✅ **Time-sensitive** implementations with tight deadlines

#### **When to Use Manual Setup:**

- ❌ **Existing workspaces** that need SSO added later
- ❌ **Complex multi-domain** configurations
- ❌ **Custom attribute mapping** requirements
- ❌ **Non-standard** SSO configurations

---

## SSO User Access Control Models

The system supports two user access models for SSO workspaces:

### **Auto-Provisioning Mode (Default: allowAutoProvisioning = true)**

**Best For**: Internal teams, open collaboration environments

```typescript
// Any user from allowed SSO domains can self-provision
Behavior:
✅ User from @client.com logs in via SSO
✅ System auto-creates user account  
✅ Assigns default "Contributor" role
✅ Grants immediate workspace access
```

**Setup**: No additional configuration needed (default behavior)

### **Invitation-Only Mode (allowAutoProvisioning = false)**  

**Best For**: Client workspaces, controlled environments, sensitive projects

```typescript
// Only explicitly invited users can access workspace
Behavior:
1. Admin invites user via /api/workspace/inviteUsers
2. System detects SSO domain → sends SSO invitation email
3. User clicks invitation link → redirected to /sso-login?invited=true
4. User completes SSO → gets assigned role from invitation
5. User accesses workspace with proper permissions
```

**Setup**:
```sql
-- Enable invitation-only mode for client workspaces
UPDATE workspace SET "allowAutoProvisioning" = false 
WHERE id = 'client-workspace-id';
```

### **Configuration Recommendation**

```typescript
// For different workspace types:
const accessControlRecommendations = {
  "Internal Teams": { allowAutoProvisioning: true },  // Open access
  "Client Projects": { allowAutoProvisioning: false }, // Controlled access  
  "Demo/POC": { allowAutoProvisioning: true },        // Easy access
  "Enterprise/Sensitive": { allowAutoProvisioning: false } // Secure access
};
```

### **Invitation System Benefits**

- ✅ **Role Control**: Assign specific roles during invitation (Admin, Contributor, Viewer)
- ✅ **Email Intelligence**: Auto-detects SSO vs password users for appropriate email templates
- ✅ **Security**: Prevents unauthorized domain users from accessing sensitive workspaces
- ✅ **Audit Trail**: Track who was invited by whom and when
- ✅ **User Experience**: Seamless SSO flow for invited users with welcome messaging

---

## Protocol Decision Matrix

Use this matrix to help clients choose the right protocol:

| Factor                    | SAML 2.0                         | OAuth 2.0 / OIDC                      |
| ------------------------- | -------------------------------- | ------------------------------------- |
| **Client Preference**     | Enterprise legacy systems        | Modern cloud applications             |
| **Identity Provider**     | ADFS, PingFederate, Okta SAML    | Azure AD, Google Workspace, Okta OIDC |
| **Security Requirements** | XML signatures, enterprise-grade | JSON Web Tokens, modern security      |
| **Technical Complexity**  | Higher (XML, certificates)       | Lower (JSON, REST APIs)               |
| **User Experience**       | Browser-based SSO                | Native app integration possible       |
| **Logout Support**        | Full Single Logout (SLO)         | Limited logout capabilities           |

### **Recommendation Guidelines:**

- **Choose SAML 2.0** for: Enterprise clients with ADFS, strict security requirements, need for Single Logout
- **Choose OAuth/OIDC** for: Modern cloud environments, mobile app requirements, simpler implementation

## SSO Flow Types Support

Our SSO implementation supports **both** initiation patterns for maximum flexibility:

### **SP-Initiated Flow (Service Provider Initiated)**
```
User Journey:
1. User visits Glacier app directly
2. Enters email → system detects SSO domain
3. Clicks "Login with SSO" button  
4. App redirects to IdP with signed state parameter
5. IdP authenticates user
6. IdP redirects back to app WITH state parameter
7. App verifies state JWT and logs user in
```

**Best For:**
- ✅ Direct app access (bookmarks, emails)
- ✅ Marketing campaigns and user invitations
- ✅ Mobile app authentication
- ✅ When users start from Glacier

### **IdP-Initiated Flow (Identity Provider Initiated)**
```
User Journey:
1. User logs into corporate IdP dashboard (Okta/Azure AD)
2. Clicks Glacier app tile from IdP portal
3. IdP redirects directly to app WITHOUT state parameter
4. App receives profile, identifies workspace by organizationId
5. App provisions user and logs them in automatically
```

**Best For:**
- ✅ Corporate dashboard workflows
- ✅ Enterprise portal integration  
- ✅ IT-managed application launchers
- ✅ WorkOS Test IdP development/testing

### **Implementation Benefits:**

| Feature | SP-Initiated | IdP-Initiated | Our Support |
|---------|-------------|---------------|-------------|
| **State Security** | JWT-signed state | No state needed | ✅ Both |
| **Workspace Discovery** | From state | From organizationId | ✅ Both |  
| **User Experience** | App-centric | IdP-centric | ✅ Both |
| **Enterprise Standard** | Common | Very common | ✅ Both |
| **Development Testing** | Manual setup | WorkOS Test IdP | ✅ Both |

**Technical Implementation:**
- ✅ **Automatic Detection**: Flow type detected by presence of state parameter
- ✅ **Zero Configuration**: No client-side setup needed
- ✅ **Backward Compatible**: Existing SP-initiated flows unchanged
- ✅ **Performance Optimized**: Fast workspace lookup for IdP flows

---

## SAML 2.0 Onboarding

### Step 1: Collect SAML-Specific Information

Send this checklist to the client's IT team:

#### **Required Information:**

Identity Provider Details:

- [ ] IdP vendor and version (e.g., "ADFS 2019", "Okta", "PingFederate 10.x")
- [ ] IdP metadata URL or XML file
- [ ] IdP Entity ID
- [ ] IdP SSO URL
- [ ] IdP SLO URL (if supported)
- [ ] Signing certificate (X.509)

User Attributes Mapping:

- [ ] Email attribute name (e.g., "mail", "emailaddress")
- [ ] First name attribute (e.g., "given_name", "firstname")
- [ ] Last name attribute (e.g., "family_name", "lastname")
- [ ] Unique ID attribute (e.g., "employeeid", "objectguid")
- [ ] Role/Group attribute (e.g., "groups", "memberof")
- [ ] Additional custom attributes (if any)

Configuration Preferences:

- [ ] SP-initiated SSO (recommended)
- [ ] IdP-initiated SSO (if required)
- [ ] NameID format preference (email, unspecified, persistent)
- [ ] Assertion encryption required? (yes/no)
- [ ] Specific allowed domains

### Step 2: Provide Glacier SAML Configuration

Send the client our Service Provider (SP) configuration:

#### **Production Configuration:**

```
Entity ID: https://app.glacier.eco/api/auth/saml/metadata
ACS URL: https://app.glacier.eco/api/auth/sso/callback
SLO URL: https://app.glacier.eco/api/auth/sso/logout
Metadata URL: https://app.glacier.eco/api/auth/saml/metadata

NameID Format: urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress
Binding: HTTP-POST
Signature Algorithm: RSA-SHA256
```

#### **Development Configuration:**

```
Entity ID: https://dev.glacier.eco/api/auth/saml/metadata
ACS URL: https://dev.glacier.eco/api/auth/sso/callback
SLO URL: https://dev.glacier.eco/api/auth/sso/logout
Metadata URL: https://dev.glacier.eco/api/auth/saml/metadata
```

### Step 3: WorkOS SAML Connection Setup

1. **Login to WorkOS Dashboard:**

   ```
   URL: https://dashboard.workos.com/
   Navigate to: Connections → New Connection → SAML
   ```

2. **Create SAML Connection:**

   ```
   Connection Name: [Client Company Name] - SAML
   Organization: [Create new organization for client]
   Domains: [client-domain.com]

   Identity Provider Configuration:
   - IdP Entity ID: [from client]
   - IdP SSO URL: [from client]
   - IdP Certificate: [upload X.509 certificate]
   ```

3. **Configure Attribute Mapping:**

   ```
   Email: mail (or client's email attribute)
   First Name: given_name
   Last Name: family_name
   Groups: groups (if applicable)
   Custom Attributes: [as needed]
   ```

4. **Save Connection ID:**
   ```
   Note down the WorkOS Connection ID (conn_xxx)
   This will be used in workspace configuration
   ```

### Step 4: Update Glacier Workspace Configuration

```sql
-- 1. Enable SSO for workspace
UPDATE workspace
SET
  sso_enabled = true,
  sso_config = jsonb_build_object(
    'provider', 'workos',
    'workosOrganizationId', 'org_[WorkOS_Org_ID]'
  )
WHERE id = '[workspace_uuid]';

-- 2. Add domain configuration for fast lookups
INSERT INTO workspace_sso (
  workspace_id,
  domain,
  workos_organization_id,
  workos_connection_id,
  protocol,
  additional_config,
  enabled
) VALUES (
  '[workspace_uuid]',
  'client-domain.com',
  'org_[WorkOS_Org_ID]',
  'conn_[WorkOS_Connection_ID]',
  'saml',
  jsonb_build_object(
    'defaultRoleId', 'contributor',
    'attributeMapping', jsonb_build_object(
      'email', 'mail',
      'first_name', 'given_name',
      'last_name', 'family_name',
      'unique_id', 'employeeid'
    )
  ),
  true
);
```

### Step 5: Testing Process

1. **Development Testing:**

   ```
   1. Client configures DEV environment in their IdP
   2. Test user logs in via: https://dev.glacier.eco/sso-login
   3. Verify user creation and attribute mapping
   4. Test logout functionality
   5. Verify error handling with invalid users
   ```

2. **Production Deployment:**
   ```
   1. Client configures PROD environment in their IdP
   2. Coordinate go-live time
   3. Test with limited user group first
   4. Full rollout after successful testing
   ```

---

## OAuth 2.0 / OIDC Onboarding

### Step 1: Collect OAuth-Specific Information

Send this checklist to the client's IT team:

#### **Required Information:**

OAuth/OIDC Provider Details:

- [ ] Provider type (Azure AD, Google Workspace, Okta, etc.)
- [ ] Authorization endpoint URL
- [ ] Token endpoint URL
- [ ] JWKS (JSON Web Key Set) URL
- [ ] Issuer URL
- [ ] Supported scopes
- [ ] User info endpoint URL

Application Registration:

- [ ] Client ID (if client registers the app)
- [ ] Client Secret (if applicable)
- [ ] Supported grant types
- [ ] Redirect URI requirements

User Claims/Scopes:

- [ ] Email claim name (usually "email")
- [ ] Name claims (given_name, family_name)
- [ ] Groups/roles claim (if applicable)
- [ ] Required scopes (openid, profile, email, etc.)

### Step 2: Provide Glacier OAuth Configuration

Send the client our OAuth application details:

#### **Production Configuration:**

```
Redirect URIs:
- https://app.glacier.eco/auth/sso/callback
- https://app.glacier.eco/dashboard

Scopes Required:
- openid (required)
- profile (for name information)
- email (for email address)
- groups (if role mapping needed)

Grant Type: Authorization Code with PKCE
Response Type: code
```

#### **Development Configuration:**

```
Redirect URIs:
- https://dev.glacier.eco/auth/sso/callback
- https://dev.glacier.eco/dashboard
```

### Step 3: WorkOS OAuth Connection Setup

1. **Create OAuth Connection in WorkOS:**

   ```
   Connection Type: OAuth 2.0
   Provider: [Azure AD/Google/Custom]
   Organization: [Client organization]
   Domains: [client-domain.com]
   ```

2. **Configure OAuth Settings:**

   ```
   Client ID: [from client's OAuth app]
   Client Secret: [from client's OAuth app]
   Authorization URL: [provider's auth endpoint]
   Token URL: [provider's token endpoint]
   Scopes: openid profile email
   ```

3. **Set up Claim Mapping:**
   ```
   Email Claim: email
   First Name Claim: given_name
   Last Name Claim: family_name
   Groups Claim: groups (if applicable)
   ```

### Step 4: Provider-Specific Configurations

#### **Azure AD (Microsoft 365)**

```
Application Registration:
- Platform: Web
- Redirect URI: https://auth.workos.com/sso/oauth/callback
- Supported account types: Single tenant
- API permissions: User.Read, profile, email, openid

Manifest Configuration:
- "accessTokenAcceptedVersion": 2
- "signInAudience": "AzureADMyOrg"
```

#### **Google Workspace**

```
OAuth 2.0 Client ID:
- Application type: Web application
- Authorized redirect URIs: https://auth.workos.com/sso/oauth/callback
- Scopes: openid, email, profile

Admin Console Configuration:
- Enable OAuth 2.0 for domain
- Configure user access policies
```

#### **Okta**

```
Application Settings:
- Application type: Web
- Grant types: Authorization Code
- Login redirect URIs: https://auth.workos.com/sso/oauth/callback
- Assignments: [User groups]
```

---

## WorkOS Configuration

### Organization Setup

1. **Create WorkOS Organization:**

   ```bash
   # Using WorkOS API
   curl -X POST https://api.workos.com/organizations \
     -H "Authorization: Bearer sk_live_xxx" \
     -H "Content-Type: application/json" \
     -d '{
       "name": "Client Company Name",
       "domains": ["client-domain.com"]
     }'
   ```

2. **Link Connection to Organization:**
   ```bash
   # Update connection with organization
   curl -X PUT https://api.workos.com/connections/{connection_id} \
     -H "Authorization: Bearer sk_live_xxx" \
     -H "Content-Type: application/json" \
     -d '{
       "organization_id": "org_xxx"
     }'
   ```

### Environment Variables

Ensure these are set in the appropriate environment:

```bash
# Production
WORKOS_API_KEY=sk_live_xxxxxxxxxxxxx
WORKOS_CLIENT_ID=client_xxxxxxxxxxxxx
WORKOS_REDIRECT_URI=https://app.glacier.eco/api/auth/sso/callback

# Development
WORKOS_API_KEY=sk_test_xxxxxxxxxxxxx
WORKOS_CLIENT_ID=client_test_xxxxxxxxxxxxx
WORKOS_REDIRECT_URI=https://dev.glacier.eco/api/auth/sso/callback
```

---

## Testing & Validation

### Pre-Launch Testing Checklist

#### **Authentication Flow Testing:**

- [ ] SP-initiated login works (from /sso-login page)
- [ ] IdP-initiated login works (if configured)
- [ ] User attributes are correctly mapped
- [ ] New user provisioning works
- [ ] Existing user linking works
- [ ] Error handling for invalid users
- [ ] Session timeout handling

#### **User Experience Testing:**

- [ ] Login redirect to correct dashboard
- [ ] Logout clears session properly
- [ ] Password users can still login
- [ ] SSO button appears for domain users
- [ ] Error messages are user-friendly

#### **Security Testing:**

- [ ] SAML assertions are properly validated
- [ ] OAuth tokens are properly verified
- [ ] State parameter prevents CSRF
- [ ] Domain restrictions work correctly
- [ ] User permissions are correctly assigned

### Testing Scripts

#### **Basic Authentication Test:**

```bash
# Test domain SSO detection
curl -X GET "https://app.glacier.eco/api/auth/sso/check-domain/client-domain.com"

# Expected response:
# {"hasSso": true, "workspaceId": "workspace-uuid"}
```

#### **Metadata Validation:**

```bash
# Test SAML metadata endpoint
curl -X GET "https://app.glacier.eco/api/auth/saml/metadata"

# Validate XML structure and endpoints
```

### Monitoring & Logging

Monitor these logs during testing:

```bash
# Backend logs to monitor
grep "SSO_EVENT" /var/log/glacier-backend.log
grep "WorkOS" /var/log/glacier-backend.log

# Key events to watch for:
# - SSO_EVENT: login, logout, provision, error
# - WorkOS webhook events
# - Authentication failures
```

---

## Common Issues & Troubleshooting

### SAML Issues

#### **Issue: "Invalid SAML Response"**

**Symptoms:** Users get error after IdP login
**Causes:**

- Clock skew between IdP and SP
- Invalid signature
- Expired assertion
- Wrong ACS URL

**Resolution:**

```bash
# Check SAML response in browser dev tools
# Validate signature and timestamps
# Verify ACS URL matches exactly
```

#### **Issue: "User attributes missing"**

**Symptoms:** User created but missing name/email
**Causes:**

- Incorrect attribute mapping
- IdP not sending required attributes
- Case sensitivity issues

**Resolution:**

```sql
-- Check user data in database
SELECT email, first_name, last_name, sso_user_id
FROM users
WHERE auth_method = 'sso'
AND workspace_id = 'workspace-uuid';

-- Update WorkOS attribute mapping
```

### OAuth Issues

#### **Issue: "Invalid redirect URI"**

**Symptoms:** OAuth flow fails at authorization step
**Causes:**

- Redirect URI mismatch
- HTTP vs HTTPS mismatch
- Port number issues in dev

**Resolution:**

```bash
# Verify exact redirect URI match
# Check OAuth app configuration
# Ensure HTTPS in production
```

#### **Issue: "Insufficient scopes"**

**Symptoms:** User info missing after OAuth login
**Causes:**

- Required scopes not requested
- User denied permissions
- Admin consent required

**Resolution:**

```javascript
// Check OAuth scopes in WorkOS connection
// Verify admin consent for enterprise apps
// Request additional scopes if needed
```

### Domain Issues

#### **Issue: "Domain not allowed"**

**Symptoms:** Users from subdomain can't login
**Causes:**

- Subdomain not in allowed list
- Wildcard domains not configured
- Case sensitivity

**Resolution:**

```sql
-- Update workspace allowed domains
UPDATE workspace
SET sso_config = jsonb_set(
  sso_config,
  '{allowed_domains}',
  '["client-domain.com", "subdomain.client-domain.com"]'::jsonb
)
WHERE id = 'workspace-uuid';
```

### Performance Issues

#### **Issue: "Slow SSO login"**

**Symptoms:** Long delays during authentication
**Causes:**

- WorkOS API latency
- Database query performance
- Network connectivity

**Resolution:**

```bash
# Monitor API response times
# Check database indexes
# Verify network connectivity to WorkOS
```

---

## Post-Deployment Support

### Monitoring Setup

#### **Set up alerts for:**

- SSO authentication failures > 5% in 5 minutes
- WorkOS webhook failures
- Database connection issues during SSO
- High latency on SSO endpoints

#### **Weekly monitoring:**

- SSO usage metrics
- Failed authentication attempts
- User provisioning errors
- Performance metrics

### User Support

#### **Common user questions:**

**Q: "I can't login with SSO"**
**A:**

1. Verify email domain is configured for SSO
2. Check if user exists in Glacier system
3. Test with /sso-login page
4. Check browser console for errors

**Q: "My role/permissions are wrong"**
**A:**

1. Check SAML/OAuth attribute mapping
2. Verify group claims from IdP
3. Update role mapping in workspace settings
4. Re-login to refresh permissions

**Q: "Can I still use my password?"**
**A:**

1. Yes, password login remains available
2. Use regular /login page
3. SSO is optional unless enforced by admin

### Maintenance Tasks

#### **Monthly:**

- [ ] Review SSO usage analytics
- [ ] Check for certificate expirations (SAML)
- [ ] Update WorkOS connection settings if needed
- [ ] Review and rotate webhook secrets

#### **Quarterly:**

- [ ] Security audit of SSO configurations
- [ ] Performance optimization review
- [ ] Client satisfaction check
- [ ] Documentation updates

#### **Annually:**

- [ ] Certificate renewal (SAML)
- [ ] OAuth client secret rotation
- [ ] Compliance review
- [ ] Capacity planning review

---

## Configuration Verification

### Verify SSO Setup

After configuring SSO for a workspace, verify the setup:

#### **Step 1: Check Database Configuration**

```sql
-- Check workspace SSO settings
SELECT domain, protocol, enabled, created_at
FROM workspace_sso
WHERE workspace_id = '[workspace_uuid]';

-- Verify domain lookup performance
EXPLAIN ANALYZE
SELECT * FROM workspace_sso
WHERE domain = 'client-domain.com' AND enabled = true;
-- Should show "Index Scan" for optimal performance
```

#### **Step 2: Test Domain Lookup**

```bash
# Test the domain check endpoint
curl -X GET "https://app.glacier.eco/api/auth/sso/check-domain/client-domain.com"

# Should return workspace info instantly
```

#### **Step 3: Validate WorkOS Connection**

```bash
# Test SSO initiation
curl -X POST "https://app.glacier.eco/api/auth/sso/init" \
  -H "Content-Type: application/json" \
  -d '{"workspaceId": "[workspace_uuid]"}'

# Should return authorization URL
```

### Troubleshooting Configuration

If SSO setup issues occur:

```sql
-- Check workspace SSO status
SELECT * FROM workspace_sso WHERE workspace_id = '[workspace_uuid]';

-- Temporarily disable for debugging
UPDATE workspace_sso SET enabled = false WHERE workspace_id = '[workspace_uuid]';

-- Re-enable after fixing issues
UPDATE workspace_sso SET enabled = true WHERE workspace_id = '[workspace_uuid]';
```

---

## Emergency Procedures

### SSO Outage Response

#### **If WorkOS is down:**

1. Enable password-only mode temporarily
2. Notify affected users via email
3. Monitor WorkOS status page
4. Document impact and resolution time

#### **If client IdP is down:**

1. Client-side issue - coordinate with client IT
2. Provide temporary password access if needed
3. Monitor for service restoration
4. Test SSO once client reports resolution

### Rollback Procedures

#### **If SSO causes issues:**

```sql
-- Temporarily disable SSO for workspace
UPDATE workspace
SET sso_enabled = false
WHERE id = 'workspace-uuid';

-- Re-enable after issue resolution
UPDATE workspace
SET sso_enabled = true
WHERE id = 'workspace-uuid';
```

---

## Appendix

### Useful Resources

- [WorkOS Documentation](https://workos.com/docs)
- [SAML 2.0 Specification](http://docs.oasis-open.org/security/saml/v2.0/)
- [OAuth 2.0 RFC](https://tools.ietf.org/html/rfc6749)
- [OpenID Connect Specification](https://openid.net/connect/)

### Contact Information

**Internal Support:**

- SSO Technical Lead: [developer-email]
- DevOps Team: [devops-email]
- Customer Success: [customer-success-email]

**External Support:**

- WorkOS Support: <EMAIL>
- Emergency Hotline: [emergency-number]

### Templates

#### **Client Email Templates:**

**Initial SSO Information Request:**

```
Subject: SSO Integration - Information Required

Dear [Client IT Team],

We're ready to begin the SSO integration for Glacier Climate Assistant.
To proceed, we'll need the following information:

[Include relevant checklist from above]

Please provide this information by [deadline] so we can meet your
go-live target of [date].

Best regards,
[Developer Name]
```

**Testing Coordination:**

```
Subject: SSO Testing Schedule - [Client Name]

Dear [Client Contact],

We've completed the SSO configuration and are ready to begin testing.

Test Environment: https://dev.glacier.eco/sso-login
Production Deployment: [Scheduled Date]

Please coordinate with your IT team for testing availability.

Best regards,
[Developer Name]
```

---

_This guide should be updated as SSO protocols evolve and new client requirements emerge._
