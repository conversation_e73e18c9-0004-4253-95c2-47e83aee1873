import {
  ConflictException,
  ForbiddenException,
  Injectable,
  UnprocessableEntityException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { UserPromptContext } from './entities/user-prompt-context.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { UserWorkspace } from './entities/user-workspace.entity';
import { Company } from '../workspace/entities/company.entity';
import { WorkspaceSSO } from '../workspace/entities/workspace-sso.entity';
import { Token, TokenType } from './entities/token.entity';
import { EmailService } from '../external/email.service';
import { CreateUserWithCompanyAndWorkspaceDto } from '../auth/auth.dto';
import { WorkspaceService } from 'src/workspace/workspace.service';
import * as bcrypt from 'bcrypt';
import { GLOBAL_AI_USER_UUID } from 'src/util/config';
import { Role } from './entities/role.entity';
import { USER_ROLES } from 'src/constants';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User) private userRepository: Repository<User>,
    @InjectRepository(Token) private tokenRepository: Repository<Token>,
    @InjectRepository(UserPromptContext)
    private userPromptContextRepository: Repository<UserPromptContext>,
    @InjectRepository(Company) private companyRepository: Repository<Company>,
    @InjectRepository(Workspace)
    private workspaceRepository: Repository<Workspace>,
    @InjectRepository(WorkspaceSSO)
    private workspaceSSORepository: Repository<WorkspaceSSO>,
    @InjectRepository(UserWorkspace)
    private userWorkspaceRepository: Repository<UserWorkspace>,
    private emailService: EmailService,
    private readonly workspaceService: WorkspaceService,
    @InjectRepository(Role)
    private roleRepository: Repository<Role>
  ) {}

  async findById(id: User['id']): Promise<User | undefined> {
    return this.userRepository.findOne({
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true,
        userWorkspaces: {
          workspaceId: true,
          joinedAt: true,
          role: {
            name: true,
          },
        },
      },
      where: { id },
      relations: ['userWorkspaces', 'userWorkspaces.role'],
    });
  }

  async findByEmailWithPassword(email: User['email']): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email: email.toLowerCase() },
      select: ['id', 'name', 'email', 'password'],
    });
  }

  async findByEmail(email: User['email']): Promise<User | null> {
    return this.userRepository.findOne({
      where: { email: email.toLowerCase() },
    });
  }

  async getUserPromptContext(userId: User['id']): Promise<string> {
    const userPromptSettings = await this.userPromptContextRepository.findOne({
      where: { userId },
    });
    return userPromptSettings?.context ?? '';
  }

  async saveUserPromptContext(
    userId: User['id'],
    context: string
  ): Promise<void> {
    await this.userPromptContextRepository.upsert({ context, userId }, [
      'userId',
    ]);
  }

  async createUser(email: string): Promise<User> {
    const existingUser = await this.findByEmail(email.toLowerCase());

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    const user = this.userRepository.create({
      email: email.toLowerCase(),
    });

    await this.userRepository.save(user);

    return user;
  }

  async createUserWithCompanyAndWorkspace(
    createUserDto: CreateUserWithCompanyAndWorkspaceDto
  ) {
    const { email, password, companyName, ssoConfig } = createUserDto;
    // Check if user already exists
    const existingUser = await this.findByEmail(email);
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Create a new workspace with SSO configuration
    const workspace = this.workspaceRepository.create({
      name: companyName,
      ssoEnabled: ssoConfig?.enabled || false,
      ssoConfig: ssoConfig
        ? {
            workosOrganizationId: ssoConfig.workosOrganizationId,
            autoProvision: true,
            requireSso: false,
          }
        : null,
    });

    await this.workspaceRepository.save(workspace);

    // Create WorkspaceSSO record if SSO is enabled
    if (ssoConfig?.enabled) {
      const contributorRole = await this.workspaceService.findRoleByName(
        USER_ROLES.Contributor
      );

      const workspaceSSO = this.workspaceSSORepository.create({
        workspaceId: workspace.id,
        domain: ssoConfig.domain,
        workosOrganizationId: ssoConfig.workosOrganizationId,
        workosConnectionId: ssoConfig.workosConnectionId,
        protocol: ssoConfig.protocol,
        enabled: true,
        additionalConfig: {
          defaultRoleId: contributorRole.id,
        },
      });
      await this.workspaceSSORepository.save(workspaceSSO);
    }

    // Create a new company with the workspace name
    const company = this.companyRepository.create({
      name: companyName,
      workspace: workspace,
    });

    await this.companyRepository.save(company);
    
    // Create a new user - handle password based on auth method
    const isSSOEnabled = ssoConfig?.enabled === true;
    let hashedPassword = null;
    
    if (!isSSOEnabled && password) {
      hashedPassword = await bcrypt.hash(password, 10);
    }
    
    const user = this.userRepository.create({
      name: companyName,
      email: email.toLowerCase(),
      password: hashedPassword,
      authMethod: isSSOEnabled ? 'sso' : 'password',
    });
    await this.userRepository.save(user);

    const contributorRole = await this.workspaceService.findRoleByName(
      USER_ROLES.Contributor
    );

    if (!contributorRole) {
      throw new Error('Contributor role not found in the database');
    }

    const userWorkspace = this.userWorkspaceRepository.create({
      user,
      workspace,
      roleId: contributorRole.id,
    });

    await this.userWorkspaceRepository.save(userWorkspace);

    return {
      user,
      company,
      workspace,
    };
  }

  async findFirstWorkspaceIdByUser(user: User): Promise<{
    workspaceId: string;
    companyId: string;
  }> {
    const userWorkspace = await this.userWorkspaceRepository.findOne({
      where: { user: user },
    });

    /**
     * TODO: Temporary process for users that don't have a workspace
     */
    if (!userWorkspace) {
      let name = user.name;
      if (user.name === null) {
        name = user.email.split('@')[0];
        await this.userRepository.update(user.id, { name: name });
      }

      // Create a new workspace
      const workspace = this.workspaceRepository.create({
        name: name,
      });

      await this.workspaceRepository.save(workspace);

      const company = this.companyRepository.create({
        name: name,
        workspace: workspace,
      });

      await this.companyRepository.save(company);

      const contributorRole = await this.workspaceService.findRoleByName(
        USER_ROLES.Contributor
      );

      if (!contributorRole) {
        throw new Error('Contributor role not found in the database');
      }

      const userWorkspace = this.userWorkspaceRepository.create({
        user,
        workspace,
        roleId: contributorRole.id,
      });

      await this.userWorkspaceRepository.save(userWorkspace);

      return {
        companyId: company.id,
        workspaceId: workspace.id,
      };
    }

    const company = await this.companyRepository.findOne({
      where: { workspace: userWorkspace.workspace },
    });

    return {
      companyId: company.id,
      workspaceId: userWorkspace.workspaceId,
    };
  }

  async sendPasswordResetEmail({
    email,
    origin,
    shouldSendEmail,
  }: {
    email: string;
    origin: string;
    shouldSendEmail: boolean;
  }): Promise<void> {
    const user = await this.userRepository.findOne({
      where: { email: email.toLowerCase() },
      relations: ['userWorkspaces'],
    });

    if (!user) {
      throw new UnprocessableEntityException('User not found');
    }
    const resetToken = crypto.randomUUID();

    //Delete all the tokens created for the current user
    await this.tokenRepository.delete({
      user: user,
      type: TokenType.PasswordReset,
    });

    const store = await this.tokenRepository.create({
      token: resetToken,
      user,
      type: TokenType.PasswordReset,
      expiresAt: new Date(Date.now() + 1000 * 60 * 60 * 24),
    });

    await this.tokenRepository.save(store);

    await this.emailService.sendPasswordReset({
      email,
      userName: user.name,
      resetToken,
      origin,
    });

    try {
      await this.workspaceService.storeActionHistory({
        event: 'password_reset_requested',
        ref: String(store.userId),
        workspaceId: user.userWorkspaces[0].workspaceId,
        versionData: {
          event: 'password_reset_requested',
          doneBy: user.id,
          data: 'password reset request initiated',
        },
      });
    } catch (err) {
      console.log(err);
    }
  }

  async validateToken(token: string): Promise<Token> {
    const userToken = await this.tokenRepository.findOne({
      where: { token },
      relations: ['user'],
    });
    if (
      !userToken?.expiresAt ||
      new Date(userToken.expiresAt).getTime() < new Date().getTime()
    ) {
      throw new ConflictException('Token Expired');
    }
    return userToken;
  }

  async resetUserPassword(
    userToken: Token,
    password: string,
    fullName?: string
  ) {
    const hashedPassword = await bcrypt.hash(password, 10);
    await this.userRepository.update(
      { id: userToken.user.id },
      { password: hashedPassword, ...(fullName ? { name: fullName } : {}) }
    );
    const user = await this.userRepository.findOne({
      where: { id: userToken.user.id },
      relations: ['userWorkspaces'],
    });
    try {
      await this.tokenRepository.delete({
        user: user,
        type: In([TokenType.PasswordReset, TokenType.WorkspaceInvite]),
      });

      await this.workspaceService.storeActionHistory({
        event: 'password_update_success',
        ref: String(userToken.userId),
        workspaceId: user.userWorkspaces[0].workspaceId,
        versionData: {
          event: 'password_update_success',
          doneBy: user.id,
          data: 'password reset successful',
        },
      });
    } catch (err) {
      console.log(err);
    }
    return user;
  }

  async findGlobalGlacierAIUser(): Promise<User> {
    return await this.userRepository.findOne({
      where: {
        id: GLOBAL_AI_USER_UUID,
      },
    });
  }

  async userHasRequiredRole(
    userOrId: string,
    requiredRoles: USER_ROLES[]
  ): Promise<boolean> {
    let user: User;
    if (typeof userOrId === 'string') {
      user = await this.findById(userOrId);
    } else {
      user = userOrId;
    }
    const userWorkspace = await this.userWorkspaceRepository.findOne({
      where: { userId: user.id },
      relations: ['role'],
    });

    if (!userWorkspace?.role) {
      return false;
    }

    return requiredRoles.includes(userWorkspace.role?.name);
  }

  async userHasRequiredRoleOrFail({
    userOrId,
    requiredRoles,
    message,
  }: {
    userOrId: string;
    requiredRoles: USER_ROLES[];
    message?: string;
  }): Promise<boolean> {
    let user: User;
    if (typeof userOrId === 'string') {
      user = await this.findById(userOrId);
    } else {
      user = userOrId;
    }
    const userWorkspace = await this.userWorkspaceRepository.findOne({
      where: { userId: user.id },
      relations: ['role'],
    });

    if (!userWorkspace?.role) {
      return false;
    }
    if (requiredRoles.includes(userWorkspace.role.name)) {
      return true;
    }

    throw new ForbiddenException(
      message || 'You do not have the right permissions'
    );
  }

  async switchWorkspace(userId: string, workspaceId: string): Promise<User> {
    await this.userWorkspaceRepository.update({ userId }, { workspaceId });

    return this.findById(userId);
  }

  // SSO-related methods
  async findBySSOId(
    ssoUserId: string,
    ssoProviderId: string
  ): Promise<User | null> {
    return await this.userRepository.findOne({
      where: { ssoUserId, ssoProviderId },
    });
  }

  async createSSOUser(userData: {
    email: string;
    name: string;
    authMethod: 'sso';
    ssoUserId: string;
    ssoProviderId: string;
    lastSsoLogin: Date;
  }): Promise<User> {
    const user = this.userRepository.create({
      ...userData,
      email: userData.email.toLowerCase(),
    });

    return await this.userRepository.save(user);
  }

  async ensureWorkspaceAccess(
    userId: string,
    workspaceId: string,
    defaultRoleId?: string
  ): Promise<void> {
    const existingAccess = await this.userWorkspaceRepository.findOne({
      where: { userId, workspaceId },
    });

    if (!existingAccess) {
      let roleId = defaultRoleId;

      if (!roleId) {
        const contributorRole = await this.workspaceService.findRoleByName(
          USER_ROLES.Contributor
        );
        roleId = contributorRole?.id;
      }

      if (!roleId) {
        throw new Error('Default role not found');
      }

      const userWorkspace = this.userWorkspaceRepository.create({
        userId,
        workspaceId,
        roleId,
        joinedAt: new Date(),
      });

      await this.userWorkspaceRepository.save(userWorkspace);
    }
  }
}
