
import axios from 'axios';
import { supabase } from '@/integrations/supabase/client';

import { API_URL } from '@/api/apiConstants';

export const login = async (credentials: {
  email: string;
  password: string;
}) => {
  try {
    const response = await axios.post(`${API_URL}/auth/login`, {
      ...credentials,
    });
    return response.data;
  } catch (error: any) {
    if (error?.response) {
      throw error.response.data.message;
    } else {
      console.error(error); // TODO: sentry
      throw error?.message ?? 'An error occurred';
    }
  }
};

export const logout = async () => {
  const response = await axios.post(`${API_URL}/auth/logout`);
  return response.data;
};

export const fetchUserProfile = async () => {
  const token = localStorage.getItem('access_token');
  const response = await axios.get(`${API_URL}/auth/profile`, {
    headers: {
      Authorization: `Bearer ${token}`,
    }
  });
  return response.data;
};

export const sendPasswordResetEmail = async (email: string) => {
  try {
    console.log("Sending password reset email to:", email);
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });

    if (error) {
      console.error("Error sending reset email:", error);
      throw error;
    }

    console.log("Reset email sent successfully:", data);
    return data;
  } catch (error) {
    console.error("Error in sendPasswordResetEmail:", error);
    throw error;
  }
};

export const resetPassword = async ({
  password,
  token,
}: {
  password: string;
  token: string;
}) => {
  try {
    const { data, error } = await supabase.auth.updateUser({
      password: password
    });

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error("Error in resetPassword:", error);
    throw error;
  }
};

export const validatePasswordResetToken = async (token: string) => {
  try {
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'recovery',
    });
    
    if (error) {
      throw error;
    }
    
    return data.user;
  } catch (error) {
    console.error("Error validating reset token:", error);
    throw error;
  }
};
