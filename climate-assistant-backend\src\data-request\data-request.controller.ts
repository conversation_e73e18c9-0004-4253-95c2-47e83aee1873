import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Req,
  Request,
  Res,
  Sse,
  SetMetadata,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DataRequestService } from './data-request.service';
import {
  GenerateDataRequestReportTextTextPayload,
  UpdateDataRequestPayload,
} from './entities/data-request.dto';
import { UseGuards } from '@nestjs/common';
import { DataRequestStatus } from './entities/data-request.entity';
import { Comment } from 'src/project/entities/comment.entity';
import { SystemPermissions } from 'src/constants';
import { Response } from 'express';
import { Observable, filter, switchMap } from 'rxjs';
import { DatapointGenerationEvent } from './constants';
import { createSseStream } from 'src/util/sse.util';
import { dataRequestGenerationStatus } from './entities/datarequest-generation.entity';
import { PermissionGuard } from 'src/auth/guard/permission.guard';
import { Permissions } from '../auth/decorators/permissions.decorator';
import { DataRequestGuard } from './data-request.guard';
import { AuthGuard } from 'src/auth/auth.guard';

@ApiTags('Data Request')
@UseGuards(AuthGuard)
@UseGuards(PermissionGuard)
@UseGuards(DataRequestGuard)
@Controller('data-request')
export class DataRequestController {
  constructor(private readonly dataRequestService: DataRequestService) {}

  @Get('/:dataRequestId')
  @ApiOperation({ summary: 'Get a specific data request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Data request retrieved successfully',
  })
  async getDataRequest(
    @Req() req,
    @Param('dataRequestId') dataRequestId: string
  ) {
    return await this.dataRequestService.findRelatedData(
      dataRequestId,
      req.user.id
    );
  }

  @Permissions(SystemPermissions.EDIT_DRS)
  @Put('/:dataRequestId')
  @ApiOperation({ summary: 'Update a specific data request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Data request updated successfully',
  })
  async updateDataRequest(
    @Request() req,
    @Param('dataRequestId') dataRequestId: string,
    @Body() updateDataRequestPayload: UpdateDataRequestPayload
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const project = await this.dataRequestService.update({
      dataRequestId,
      updateDataRequestPayload,
      userId,
      workspaceId,
    });
    return project;
  }

  @Permissions(SystemPermissions.APPROVE_DRS)
  @Put('/:dataRequestId/status')
  @ApiOperation({ summary: 'Update data request status' })
  @ApiResponse({
    status: 200,
    description: 'Data request status updated successfully',
  })
  async updateDataRequestStatus(
    @Request() req,
    @Param('dataRequestId') dataRequestId: string,
    @Body() updateDataRequestPayload: UpdateDataRequestPayload
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;

    const project = await this.dataRequestService.update({
      dataRequestId,
      updateDataRequestPayload,
      userId,
      workspaceId,
      event: 'data_request_status_updated',
    });
    return project;
  }

  @Permissions(SystemPermissions.APPROVE_DRS)
  @Put('/:dataRequestId/approve')
  @ApiOperation({ summary: 'Approve a specific data request by ID' })
  @ApiResponse({
    status: 200,
    description: 'Data request approved successfully',
  })
  async approveDataRequest(
    @Request() req,
    @Param('dataRequestId') dataRequestId: string
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    const updateDataRequestPayload: UpdateDataRequestPayload = {
      approvedBy: userId,
      approvedAt: new Date(),
      status: DataRequestStatus.CompleteData,
    };

    const project = await this.dataRequestService.update({
      dataRequestId,
      updateDataRequestPayload,
      userId,
      workspaceId,
      event: 'data_request_approved',
    });
    return project;
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DRS)
  @Post('/:dataRequestId/review-with-ai')
  @ApiOperation({ summary: 'Review data request content with AI' })
  @ApiResponse({
    status: 200,
    description: 'Data request content reviewed successfully',
  })
  async reviewContentWithAi(
    @Param('dataRequestId') dataRequestId: string,
    @Req() req
  ): Promise<Comment[]> {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    return await this.dataRequestService.reviewDataRequestContentWithAI({
      dataRequestId,
      userId,
      workspaceId,
    });
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DRS)
  @SetMetadata('customCheck', 'generateWithAI')
  @Post('/:dataRequestId/generate-with-ai')
  @ApiOperation({ summary: 'Generate data request content with AI' })
  @ApiResponse({
    status: 200,
    description: 'Data request content generated successfully',
  })
  async generateContentWithAi(
    @Param('dataRequestId') dataRequestId: string,
    @Body() data: GenerateDataRequestReportTextTextPayload,
    @Req() req
  ): Promise<{ content: string; id: string }> {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;

    return await this.dataRequestService.generateDataRequestTextContentWithAI({
      dataRequestId,
      userId,
      workspaceId,
      additionalData: data,
    });
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DRS)
  @Put('/:dataRequestId/review-bulk-datapoint')
  @ApiOperation({ summary: 'Review datapoint for data request' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint reviewed successfully',
  })
  async reviewBulkDatapointForDataRequest(
    @Param('dataRequestId') dataRequestId: string,
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    return await this.dataRequestService.reviewAllDatapointForDataRequest({
      dataRequestId,
      userId,
      workspaceId,
    });
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DRS_REVIEW)
  @Put('/:dataRequestId/generate-bulk-datapoint')
  @ApiOperation({ summary: 'Generate datapoint for data request' })
  @ApiResponse({
    status: 200,
    description: 'Datapoint generated successfully',
  })
  async generateBulkDatapointForDataRequest(
    @Param('dataRequestId') dataRequestId: string,
    @Req() req
  ) {
    const userId = req.user.id;
    const workspaceId = req.user.workspaceId;
    return await this.dataRequestService.generateAllDatapointForDataRequest({
      dataRequestId,
      userId,
      workspaceId,
    });
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DRS_REVIEW)
  @Get('/:dataRequestId/generations')
  @ApiOperation({ summary: 'Get all generations for a data request' })
  @ApiResponse({
    status: 200,
    description: 'Generations retrieved successfully',
  })
  async getGenerations(@Param('dataRequestId') dataRequestId: string) {
    return await this.dataRequestService.getGenerations(dataRequestId);
  }

  @Sse('/events/datapoint/:dataRequestId')
  subscribeToDataRequestEvents(
    @Param('dataRequestId') dataRequestId: string,
    @Res() response: Response
  ): Observable<MessageEvent> {
    const eventStream = this.dataRequestService.events$.pipe(
      filter(
        (event: DatapointGenerationEvent) =>
          event.dataRequestId === dataRequestId
      ),
      switchMap(async (event) => {
        if (event.status === 'failed') {
          //This should be passed as a callback on the queue for failure
          await this.dataRequestService.setDatapointQueueStatusToNull(
            event.datapointRequestId
          );
        }
        const datapoint = await this.dataRequestService.findDatapointById(
          event.datapointRequestId
        );

        event.datapointRequest = datapoint;
        return {
          data: JSON.stringify(event),
          type: 'message',
        } as MessageEvent;
      })
    );
    return createSseStream(response, eventStream, {
      data: { connected: true },
      type: 'connection',
    } as MessageEvent);
  }

  @Permissions(SystemPermissions.GAP_ANALYSIS_DRS_REVIEW)
  @Put('/:dataRequestId/generation-status')
  @ApiOperation({
    summary: 'Update generation status by generation ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Generation status updated successfully',
  })
  async updateDatapointGenerationStatus(
    @Req() req,
    @Body()
    payload: {
      dataRequestGenerationId: string;
      status: dataRequestGenerationStatus;
      evaluatorComment?: string;
    }
  ): Promise<{ content?: string; status: dataRequestGenerationStatus }> {
    const generation = await this.dataRequestService.updateGenerationStatus({
      dataRequestGenerationId: payload.dataRequestGenerationId,
      status: payload.status,
      userId: req.user.id,
      workspaceId: req.user.workspaceId,
      evaluatorComment: payload.evaluatorComment,
    });
    if (generation) {
      await this.dataRequestService.update({
        dataRequestId: generation.dataRequest.id,
        updateDataRequestPayload: {
          content: generation.data.content,
        },
        userId: req.user.id,
        workspaceId: req.user.workspaceId,
      });
    }
    return {
      status: payload.status,
      content: generation?.data?.content || null,
    };
  }
}
