import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';

export interface WebhookEvent {
  id: string;
  event: string;
  data: any;
  createdAt: string;
  organizationId?: string;
}

@Injectable()
export class WebhookProcessorService {
  private readonly logger = new Logger(WebhookProcessorService.name);
  private processedEvents = new Set<string>(); // In-memory idempotency for this instance
  private readonly MAX_EVENT_AGE = 5 * 60 * 1000; // 5 minutes

  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>
  ) {}

  /**
   * Process webhook event asynchronously with idempotency
   */
  async processWebhookEvent(webhookEvent: WebhookEvent): Promise<void> {
    try {
      // Idempotency check
      if (this.processedEvents.has(webhookEvent.id)) {
        this.logger.log(
          `Webhook event ${webhookEvent.id} already processed (idempotency)`
        );
        return;
      }

      // Validate event age (prevent replay attacks)
      const eventTime = new Date(webhookEvent.createdAt).getTime();
      const now = Date.now();
      if (now - eventTime > this.MAX_EVENT_AGE) {
        this.logger.warn(
          `Webhook event ${webhookEvent.id} is too old, ignoring`
        );
        return;
      }

      // Mark as processed immediately for idempotency
      this.processedEvents.add(webhookEvent.id);

      // Process event based on type
      await this.processEventByType(webhookEvent);

      this.logger.log(
        `Successfully processed webhook event: ${webhookEvent.event} (${webhookEvent.id})`
      );
    } catch (error) {
      // Remove from processed set if processing failed
      this.processedEvents.delete(webhookEvent.id);
      this.logger.error(
        `Failed to process webhook event ${webhookEvent.id}:`,
        error
      );
      throw error;
    }
  }

  private async processEventByType(webhookEvent: WebhookEvent): Promise<void> {
    switch (webhookEvent.event) {
      case 'user.deleted':
      case 'user.deactivated':
        await this.handleUserDeactivation(
          webhookEvent.data,
          webhookEvent.organizationId
        );
        break;
      case 'user.updated':
        await this.handleUserUpdate(
          webhookEvent.data,
          webhookEvent.organizationId
        );
        break;
      case 'connection.deleted':
        await this.handleConnectionDeleted(webhookEvent.data);
        break;
      case 'connection.activated':
      case 'connection.deactivated':
        await this.handleConnectionStatusChange(
          webhookEvent.data,
          webhookEvent.event
        );
        break;
      default:
        this.logger.log(`Unhandled webhook event type: ${webhookEvent.event}`);
    }
  }

  private async handleUserDeactivation(
    userData: any,
    organizationId?: string
  ): Promise<void> {
    try {
      const user = await this.userRepository.findOne({
        where: { ssoUserId: userData.id },
      });

      if (user) {
        // Log the deactivation event - actual user handling depends on business requirements
        // Options: 1) Soft delete, 2) Remove from workspaces, 3) Just log for admin review
        this.logger.warn(
          `SSO user deactivated in provider: ${user.email} (${userData.id})`
        );

        // TODO: Implement business logic for deactivated users
        // await this.handleUserDeactivationLogic(user);
      } else {
        this.logger.warn(`User not found for deactivation: ${userData.id}`);
      }
    } catch (error) {
      this.logger.error('Failed to handle user deactivation:', error);
      throw error;
    }
  }

  private async handleUserUpdate(
    userData: any,
    organizationId?: string
  ): Promise<void> {
    try {
      const user = await this.userRepository.findOne({
        where: { ssoUserId: userData.id },
      });

      if (user) {
        let updated = false;

        // Update name if provided
        if (userData.firstName || userData.lastName) {
          const newName =
            `${userData.firstName || ''} ${userData.lastName || ''}`.trim();
          if (newName && newName !== user.name) {
            user.name = newName;
            updated = true;
          }
        }

        // Update email if provided and different
        if (userData.email && userData.email.toLowerCase() !== user.email) {
          user.email = userData.email.toLowerCase();
          updated = true;
        }

        if (updated) {
          await this.userRepository.save(user);
          this.logger.log(`SSO user updated: ${user.email} (${userData.id})`);
        }
      } else {
        this.logger.warn(`User not found for update: ${userData.id}`);
      }
    } catch (error) {
      this.logger.error('Failed to handle user update:', error);
      throw error;
    }
  }

  private async handleConnectionDeleted(connectionData: any): Promise<void> {
    this.logger.log(`SSO connection deleted: ${connectionData.id}`);
    // Could update workspace configuration if needed
  }

  private async handleConnectionStatusChange(
    connectionData: any,
    event: string
  ): Promise<void> {
    this.logger.log(`SSO connection ${event}: ${connectionData.id}`);
    // Could update workspace configuration if needed
  }

  /**
   * Clean up old processed event IDs to prevent memory leaks
   */
  cleanupProcessedEvents(): void {
    // In a production system, you'd want to use Redis or database for persistence
    // For now, clear the in-memory set periodically
    this.processedEvents.clear();
    this.logger.log('Cleaned up processed webhook events cache');
  }
}
