import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../cli-runner';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../app.module';
import { Logger } from '@nestjs/common';
import { UsersService } from '../../users/users.service';
import { PostmarkService } from '../../external/postmark.service';
import { DataSource } from 'typeorm';

/**
 * Script Example with Common Patterns
 * Copy this pattern to create new scripts
 * 
 * ENVIRONMENT SETUP:
 * For scripts that need database access, set BACKEND_DB_URL:
 * export BACKEND_DB_URL="postgresql://user:password@localhost:5432/dbname"
 * 
 * Usage: ts-node -r tsconfig-paths/register src/scripts/examples/simple-script-example.ts -- --action=demo --userId=123
 */
class SimpleScriptExample extends CliRunner {
  private scriptLogger = new Logger('SimpleScriptExample');

  async run(...args: string[]): Promise<void> {
    this.scriptLogger.log('Script starting with args:', args);

    const options = this.parseArgs(args);

    // Option 1: Full NestJS context (recommended for most scripts)
    if (options.useNestJS !== 'false') {
      await this.runWithNestJS(options);
    } else {
      // Option 2: Direct database connection (faster startup for simple DB operations)
      await this.runWithDirectDB(options);
    }
  }

  private async runWithNestJS(options: any): Promise<void> {
    this.scriptLogger.log('Running with full NestJS context...');

    // Get NestJS application context to access services
    const app = await NestFactory.createApplicationContext(AppModule);

    try {
      // Access any service you need
      const usersService = app.get(UsersService);
      const emailService = app.get(PostmarkService);

      // Example operations based on arguments
      switch (options.action) {
        case 'demo':
          await this.demoOperation(usersService, options);
          break;
        case 'email':
          await this.emailOperation(emailService, options);
          break;
        default:
          this.showUsage();
      }
    } catch (error) {
      this.scriptLogger.error('Operation failed:', error.message);
      throw error;
    } finally {
      await app.close();
      this.scriptLogger.log('Script completed');
    }
  }

  private async runWithDirectDB(options: any): Promise<void> {
    this.scriptLogger.log('Running with direct database connection...');

    if (!process.env.BACKEND_DB_URL) {
      throw new Error('BACKEND_DB_URL environment variable is required for direct DB mode');
    }

    // Create direct database connection using BACKEND_DB_URL
    const dataSource = new DataSource({
      type: 'postgres',
      url: process.env.BACKEND_DB_URL,
      entities: ['src/**/*.entity{.ts,.js}'],
      synchronize: false,
    });

    try {
      await dataSource.initialize();
      this.scriptLogger.log('Direct database connection established');

      // Example direct database operations
      switch (options.action) {
        case 'count':
          await this.countUsers(dataSource);
          break;
        case 'query':
          await this.runCustomQuery(dataSource, options.sql);
          break;
        default:
          this.showUsage();
      }
    } catch (error) {
      this.scriptLogger.error('Database operation failed:', error.message);
      throw error;
    } finally {
      if (dataSource.isInitialized) {
        await dataSource.destroy();
        this.scriptLogger.log('Database connection closed');
      }
    }
  }

  private async demoOperation(usersService: UsersService, options: any): Promise<void> {
    this.scriptLogger.log('Running demo operation...');
    
    if (options.userId) {
      // Example: Get user by ID
      try {
        const user = await usersService.findById(options.userId);
        this.scriptLogger.log(`Found user: ${user?.email || 'Not found'}`);
      } catch (error) {
        this.scriptLogger.warn(`User not found: ${error.message}`);
      }
    }

    // Add your business logic here
    this.scriptLogger.log('Demo operation completed');
  }

  private async emailOperation(emailService: PostmarkService, options: any): Promise<void> {
    this.scriptLogger.log('Running email operation...');
    
    if (!options.email) {
      this.scriptLogger.warn('No email provided for email operation');
      return;
    }

    // Example email operation
    // await emailService.sendTemplatedEmail({...});
    this.scriptLogger.log(`Would send email to: ${options.email}`);
  }

  private async countUsers(dataSource: DataSource): Promise<void> {
    const result = await dataSource.query('SELECT COUNT(*) as count FROM "user"');
    this.scriptLogger.log(`Total users: ${result[0].count}`);
  }

  private async runCustomQuery(dataSource: DataSource, sql: string): Promise<void> {
    if (!sql) {
      this.scriptLogger.warn('No SQL query provided');
      return;
    }
    
    const result = await dataSource.query(sql);
    this.scriptLogger.log('Query result:', JSON.stringify(result, null, 2));
  }

  private parseArgs(args: string[]): Record<string, string | boolean> {
    const options: Record<string, string | boolean> = {};
    
    args.forEach((arg) => {
      if (arg.startsWith('--')) {
        const [key, value] = arg.substring(2).split('=');
        options[key] = value || true;
      }
    });
    
    return options;
  }

  private showUsage(): void {
    console.log(`
Usage: ts-node -r tsconfig-paths/register src/scripts/examples/simple-script-example.ts -- [options]

ENVIRONMENT:
  Set BACKEND_DB_URL for direct database access:
  export BACKEND_DB_URL="postgresql://user:password@localhost:5432/dbname"

Options:
  --action=demo         Run demo operation (requires NestJS)
  --action=email        Run email operation (requires NestJS)
  --action=count        Count users (direct DB)
  --action=query        Run custom SQL (direct DB)
  --useNestJS=false     Use direct DB connection instead of NestJS
  --userId=ID           User ID for demo
  --email=EMAIL         Email address for email operation
  --sql="query"         SQL query for query action

Examples:
  # With NestJS services (default)
  ts-node -r tsconfig-paths/register src/scripts/examples/simple-script-example.ts -- --action=demo --userId=123
  
  # Direct database access (faster for simple operations)
  BACKEND_DB_URL="postgresql://user:pass@localhost:5432/db" ts-node -r tsconfig-paths/register src/scripts/examples/simple-script-example.ts -- --action=count --useNestJS=false
    `);
  }
}

// Execute if run directly
if (require.main === module) {
  const script = new SimpleScriptExample();
  const args = process.argv.slice(2);
  script.bootstrap(args);
} 