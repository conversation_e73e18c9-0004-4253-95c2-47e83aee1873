// NOTE: This sorting object is cloned in the frontend at climate-assistant-frontend/src/lib/config.ts
export const ESRS_SORT_ORDER = {
  'ESRS 2': 0,
  E1: 1,
  E2: 2,
  E3: 3,
  E4: 4,
  E5: 5,
  S1: 6,
  S2: 7,
  S3: 8,
  S4: 9,
  G1: 10,
};

export const GLOBAL_AI_USER_UUID = '08106d33-61e4-477a-9adb-0283ecff0c54';

// WorkOS Configuration
export interface WorkOSConfig {
  apiKey: string;
  clientId: string;
  redirectUri: string;
  webhookSecret?: string;
}

export const getWorkOSConfig = (): WorkOSConfig => {
  const apiKey = process.env.WORKOS_API_KEY;
  const clientId = process.env.WORKOS_CLIENT_ID;
  const redirectUri = process.env.WORKOS_REDIRECT_URI;
  const webhookSecret = process.env.WORKOS_WEBHOOK_SECRET;

  if (!apiKey || !clientId || !redirectUri) {
    throw new Error(
      'Missing required WorkOS environment variables: WORKOS_API_KEY, WORKOS_CLIENT_ID, WORKOS_REDIRECT_URI'
    );
  }

  return {
    apiKey,
    clientId,
    redirectUri,
    webhookSecret,
  };
};
