import { Module } from '@nestjs/common';
import { LlmService } from './services/llm.service';
import { LlmRateLimiterService } from './services/llm-rate-limiter.service';
import { Workspace } from 'src/workspace/entities/workspace.entity';
import { BullModule } from '@nestjs/bull';
import { JobProcessor } from 'src/types/jobs';
import { DEFAULT_QUEUE_JOB_OPTIONS } from 'src/util/queue.config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LlmTokenUsage } from './entities/llm-token-usage.entity';
import { LlmTokenTrackingService } from './services/llm-token-tracking.service';

@Module({
  imports: [
    LlmModule,
    BullModule.registerQueue({
      name: JobProcessor.LlmRequest,
      defaultJobOptions: DEFAULT_QUEUE_JOB_OPTIONS,
    }),
    TypeOrmModule.forFeature([LlmTokenUsage, Workspace]),
  ],
  providers: [LlmService, LlmRateLimiterService, LlmTokenTrackingService],
  exports: [LlmService, LlmRateLimiterService, LlmTokenTrackingService],
  controllers: [],
})
export class LlmModule {}
