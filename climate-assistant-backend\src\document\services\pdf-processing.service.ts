import { Injectable } from '@nestjs/common';
import type { DocumentChunkGenerated } from 'src/types';
import { parseDocumentWithLlamaparseApi } from 'src/llm/services/llamaparse.service';
import { DocumentParsingUtils } from './utils';
import { HtmlProcessingService } from './html-processing.service';
import { MarkdownTableService } from './markdown-table.service';
import { ChunkProcessingService } from './chunk-processing.service';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class PdfProcessingService {
  private readonly logger = new WorkerLogger(PdfProcessingService.name);

  constructor(
    private readonly htmlProcessingService: HtmlProcessingService,
    private readonly markdownTableService: MarkdownTableService,
    private readonly chunkProcessingService: ChunkProcessingService
  ) {}

  /**
   * Process PDF document by page with overlapping content for context
   */
  async parsePageBasedPDFToMarkdown(
    filePath: string,
    premiumMode?: boolean
  ): Promise<DocumentChunkGenerated[]> {
    this.logger.log(
      `Parsing PDF document by page with overlap: ${filePath}, premium mode: ${premiumMode}`
    );

    const allDocumentMarkdown = await parseDocumentWithLlamaparseApi({
      filePath,
      premiumMode,
      pageSeparator: '\n<PAGE>=================</PAGE>\n',
    });
    this.logger.log(
      `Received parsed document from Llamaparse API, text length: ${allDocumentMarkdown.text.length}`
    );

    const sourceDocumentName = filePath.split('/').pop();

    // Split content by page separator
    const pages = allDocumentMarkdown.text.split(
      '\n<PAGE>=================</PAGE>\n'
    );
    this.logger.log(`Document split into ${pages.length} pages`);

    const chunkDataArray: DocumentChunkGenerated[] = [];
    let chunkNumber = 1;

    try {
      // Process each page with overlap
      for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
        // Current page content
        let pageContent = pages[pageIndex];
        const currentPageNumber = pageIndex + 1;

        // Extract heading hierarchy for this page
        const headingHierarchy: string[] = [];
        const mainSections: string[] = [];
        const headingRegex = /^(#{1,6})\s+(.*)$/gm;
        let match;

        while ((match = headingRegex.exec(pageContent)) !== null) {
          const headingLevel = match[1].length;
          const headingText = match[2].trim();

          // Update heading hierarchy
          while (headingHierarchy.length >= headingLevel) {
            headingHierarchy.pop();
          }
          headingHierarchy.push(headingText);

          if (headingLevel === 1 && !mainSections.includes(headingText)) {
            mainSections.push(headingText);
          }
        }

        // Add content from previous page (approximately 100 tokens)
        if (pageIndex > 0) {
          const prevPage = pages[pageIndex - 1];
          // Get approximately 100 tokens from the end of the previous page
          // ~4 chars per token, aiming for ~400 characters
          const overlapSize = 400;
          const prevPageOverlap =
            prevPage.length > overlapSize
              ? prevPage.substring(prevPage.length - overlapSize)
              : prevPage;

          // Add a separator to indicate overlapping content
          pageContent = `<!-- Overlap from previous page -->\n${prevPageOverlap}\n<!-- Current page content -->\n${pageContent}`;
        }

        // Add content from next page (approximately 100 tokens)
        if (pageIndex < pages.length - 1) {
          const nextPage = pages[pageIndex + 1];
          // Get approximately 100 tokens from the beginning of the next page
          // ~4 chars per token, aiming for ~400 characters
          const overlapSize = 400;
          const nextPageOverlap =
            nextPage.length > overlapSize
              ? nextPage.substring(0, overlapSize)
              : nextPage;

          // Add a separator to indicate overlapping content
          pageContent = `${pageContent}\n<!-- Overlap from next page -->\n${nextPageOverlap}`;
        }

        // Create page chunk
        const chunk = this.chunkProcessingService.createPageChunk({
          pageContent,
          headingHierarchy,
          sourceDocumentName,
          mainSections,
          pageNumber: currentPageNumber,
          chunkNumber,
        });

        chunkDataArray.push(chunk);
        chunkNumber++;
      }

      this.logger.log(
        `Page-based PDF parsing complete, generated ${chunkDataArray.length} chunks`
      );
      return chunkDataArray;
    } finally {
      // CLEANUP: Clear pages array
      pages.length = 0;

      // Force garbage collection hint
      if (global.gc) {
        global.gc();
      }
    }
  }

  /**
   * Parse PDF document with table detection and chunking
   */
  async parsePDFDocumentToMarkdown(
    filePath: string,
    premiumMode?: boolean
  ): Promise<DocumentChunkGenerated[]> {
    this.logger.log(
      `Parsing PDF document to markdown: ${filePath}, premium mode: ${premiumMode}`
    );

    const allDocumentMarkdown = await parseDocumentWithLlamaparseApi({
      filePath,
      premiumMode,
      pageSeparator: '\n<PAGE>=================</PAGE>\n',
    });
    this.logger.log(
      `Received parsed document from Llamaparse API, text length: ${allDocumentMarkdown.text.length}`
    );

    const sourceDocumentName = filePath.split('/').pop();

    let lines = allDocumentMarkdown.text.split('\n');
    this.logger.log(`Document split into ${lines.length} lines`);

    lines = this.htmlProcessingService.joinLinesWithHtmlTables(lines);
    this.logger.log(`After joining HTML tables: ${lines.length} lines`);

    const headingHierarchy: string[] = [];
    const mainSections: string[] = [];
    const chunkDataArray: DocumentChunkGenerated[] = [];
    let currentChunk = '';
    let currentChunkTokens = 0;
    let pageNumber = 1; // Start from page 1
    let chunkNumber = 1;
    let currentChunkPageNumbers = new Set<number>(); // Track page numbers
    let chunkHeadingHierarchy: string[] = [];
    const headingRegex = /^(#{1,6})\s+(.*)$/;
    const pageSeparatorRegex = /^\s*<PAGE>=================<\/PAGE>\s*$/;
    // Regex to detect table lines
    const tableLineRegex = /^\s*\|.*\|\s*$/;
    // Variables to handle tables
    let inTable = false;
    const tableLines: string[] = [];
    const tablePageNumbers = new Set<number>();

    try {
      // Loop over lines
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (pageSeparatorRegex.test(line)) {
          pageNumber++;
          // Add page number to current tables or chunks
          if (inTable) {
            tablePageNumbers.add(pageNumber);
          } else {
            currentChunkPageNumbers.add(pageNumber);
          }
          continue; // Skip adding the page separator to the chunk
        }

        // Handle tables
        if (inTable) {
          if (tableLineRegex.test(line)) {
            // Line is part of the table
            tableLines.push(line);
            tablePageNumbers.add(pageNumber);
            continue; // Skip further processing for this line
          } else {
            // Table has ended
            inTable = false;

            // Process the table
            const { headers, rows } =
              this.markdownTableService.parseMarkdownTable(tableLines);

            const tableMarkdown = this.markdownTableService.createMarkdownTable(
              headers,
              rows
            );
            const tableTokenCount =
              DocumentParsingUtils.countTokens(tableMarkdown);

            // Generate page number metadata for table
            const pageNumberMetadata =
              DocumentParsingUtils.generatePageNumberMetadata(tablePageNumbers);

            if (tableTokenCount > 500) {
              // Table is too big, split it
              const splitTables =
                this.markdownTableService.splitTableByTokenCount(
                  headers,
                  rows,
                  500
                );

              for (const splitTable of splitTables) {
                chunkDataArray.push({
                  text: splitTable,
                  metadata: {
                    headings: [...chunkHeadingHierarchy],
                    sourceDocumentName: sourceDocumentName,
                    mainSections: [...mainSections],
                    pageNumber: pageNumberMetadata,
                    chunkNumber: chunkNumber,
                  },
                });
                chunkNumber++; // Increment chunkNumber
              }
            } else {
              // Table is acceptable size
              chunkDataArray.push({
                text: tableMarkdown,
                metadata: {
                  headings: [...chunkHeadingHierarchy],
                  sourceDocumentName: sourceDocumentName,
                  mainSections: [...mainSections],
                  pageNumber: pageNumberMetadata,
                  chunkNumber: chunkNumber,
                },
              });
              chunkNumber++; // Increment chunkNumber
            }

            // Reset tableLines and tablePageNumbers
            tableLines.length = 0;
            tablePageNumbers.clear();

            // Since we've processed the table, continue processing the current line
          }
        }

        // Check if line is the start of a table
        if (!inTable && tableLineRegex.test(line)) {
          // Line is the start of a table
          inTable = true;
          tableLines.push(line);
          tablePageNumbers.add(pageNumber);
          // If we were building a chunk, process it before starting the table
          if (currentChunk !== '') {
            chunkNumber = await this.chunkProcessingService.processCurrentChunk(
              {
                currentChunk,
                currentChunkTokens,
                currentChunkPageNumbers,
                chunkDataArray,
                chunkHeadingHierarchy,
                sourceDocumentName,
                mainSections,
                chunkNumber,
              }
            );
            currentChunk = '';
            currentChunkTokens = 0;
            currentChunkPageNumbers.clear();
          }
          continue; // Skip further processing for this line
        }

        // Handle headings
        const headingMatch = line.match(headingRegex);

        if (currentChunk === '') {
          // Starting a new chunk
          // Capture the heading hierarchy for the chunk
          chunkHeadingHierarchy = headingHierarchy.slice();
          currentChunkPageNumbers = new Set<number>();
        }

        if (headingMatch) {
          const headingLevel = headingMatch[1].length;
          const headingText = headingMatch[2].trim();

          // Pop heading hierarchy only if new heading is at a higher or same level
          while (headingHierarchy.length >= headingLevel) {
            headingHierarchy.pop();
          }
          headingHierarchy.push(headingText);

          if (headingLevel === 1 && !mainSections.includes(headingText)) {
            mainSections.push(headingText);
          }
        }

        // Add line to currentChunk
        currentChunk += line + '\n';
        currentChunkTokens = DocumentParsingUtils.countTokens(currentChunk);
        currentChunkPageNumbers.add(pageNumber);

        if (currentChunkTokens >= 500) {
          // Process currentChunk
          chunkNumber = await this.chunkProcessingService.processCurrentChunk({
            currentChunk,
            currentChunkTokens,
            currentChunkPageNumbers,
            chunkDataArray,
            chunkHeadingHierarchy,
            sourceDocumentName,
            mainSections,
            chunkNumber,
          });
          // Reset currentChunk and tokens
          currentChunk = '';
          currentChunkTokens = 0;
          currentChunkPageNumbers.clear();
          // Update chunkHeadingHierarchy
          chunkHeadingHierarchy = headingHierarchy.slice();
        }
      }

      // Process any remaining table
      if (inTable) {
        this.logger.log(
          `Processing final table with ${tableLines.length} lines`
        );
        const { headers, rows } =
          this.markdownTableService.parseMarkdownTable(tableLines);

        const tableMarkdown = this.markdownTableService.createMarkdownTable(
          headers,
          rows
        );
        const tableTokenCount = DocumentParsingUtils.countTokens(tableMarkdown);

        // Generate page number metadata for table
        const pageNumberMetadata =
          DocumentParsingUtils.generatePageNumberMetadata(tablePageNumbers);

        if (tableTokenCount > 500) {
          // Table is too big, split it
          const splitTables = this.markdownTableService.splitTableByTokenCount(
            headers,
            rows,
            500
          );

          for (const splitTable of splitTables) {
            chunkDataArray.push({
              text: splitTable,
              metadata: {
                headings: [...chunkHeadingHierarchy],
                sourceDocumentName: sourceDocumentName,
                mainSections: [...mainSections],
                pageNumber: pageNumberMetadata,
                chunkNumber: chunkNumber,
              },
            });
            chunkNumber++; // Increment chunkNumber
          }
        } else {
          // Table is acceptable size
          chunkDataArray.push({
            text: tableMarkdown,
            metadata: {
              headings: [...chunkHeadingHierarchy],
              sourceDocumentName: sourceDocumentName,
              mainSections: [...mainSections],
              pageNumber: pageNumberMetadata,
              chunkNumber: chunkNumber,
            },
          });
          chunkNumber++; // Increment chunkNumber
        }
      }

      // Add any remaining chunk
      if (currentChunk) {
        this.logger.log(
          `Processing final text chunk of ${currentChunkTokens} tokens`
        );
        chunkNumber = await this.chunkProcessingService.processCurrentChunk({
          currentChunk,
          currentChunkTokens,
          currentChunkPageNumbers,
          chunkDataArray,
          chunkHeadingHierarchy,
          sourceDocumentName,
          mainSections,
          chunkNumber,
        });
      }

      this.logger.log(
        `PDF parsing complete, generated ${chunkDataArray.length} chunks`
      );
      return chunkDataArray;
    } finally {
      // CLEANUP: Clear large intermediate arrays
      lines.length = 0;
      tableLines.length = 0;
      headingHierarchy.length = 0;
      mainSections.length = 0;
      currentChunkPageNumbers.clear();
      tablePageNumbers.clear();
      chunkHeadingHierarchy.length = 0;

      // Force garbage collection hint
      if (global.gc) {
        global.gc();
      }
    }
  }
}
