import { MigrationInterface, QueryRunner } from "typeorm";

/**
 * Control whether SSO enabled workspaces can auto-provision users (true) or require invitation (false)
 */

export class SchemaUpdate1754289921489 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add allow_auto_provisioning column to workspace table
        await queryRunner.query(
          `ALTER TABLE "workspace" ADD "allowAutoProvisioning" boolean NOT NULL DEFAULT false`
        );

      }
    
      public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the column
        await queryRunner.query(
          `ALTER TABLE "workspace" DROP COLUMN "allowAutoProvisioning"`
        );
    }

}
