import { Navigate } from 'react-router-dom';

import { ProtectedRoute } from './ProtectedRoute';

import Login from '@/pages/login';
import { Documents } from '@/pages/documents';
import { Dashboard } from '@/pages/dashboard';
import { DataRequestDetailPage } from '@/pages/dashboard/DatarequestDetail';
import DocumentChunksPage from '@/pages/documents/DocumentChunks';
import Settings from '@/pages/dashboard/settings';
import { MaterialitySettings } from '@/pages/dashboard/MaterialitySettings';
import ResetPassword from '@/pages/reset-password';
import { AdminPrompts } from '@/pages/admin/prompts/AdminPrompts';
import { SSOCallback } from '@/pages/login/SSOCallback';
import { SSOLogin } from '@/pages/login/SSOLogin';
import { SSOOnboardingGuide } from '@/pages/sso-onboarding/SSOOnboardingGuide';
import { SSOLandingPage } from '@/pages/sso-onboarding/SSOLandingPage';

export interface RouteConfig {
  path?: string;
  index?: boolean;
  element: React.ReactNode;
  handle?: {
    breadcrumb: string;
  };
  children?: RouteConfig[];
}

export const routeConfig = [
  {
    path: '/login',
    element: <Login />,
    handle: { breadcrumb: 'Login' },
  },
  {
    path: '/sso-login',
    element: <SSOLogin />,
    handle: { breadcrumb: 'SSO Login' },
  },
  {
    path: '/auth/sso/callback',
    element: <SSOCallback />,
    handle: { breadcrumb: 'SSO Callback' },
  },
  {
    path: '/sso',
    element: <SSOLandingPage />,
    handle: { breadcrumb: 'SSO Integration' },
  },
  {
    path: '/sso-setup',
    element: <SSOOnboardingGuide />,
    handle: { breadcrumb: 'SSO Setup Guide' },
  },
  {
    path: '/reset-password',
    element: <ResetPassword />,
    handle: { breadcrumb: 'ResetPassowrd' },
  },
  {
    path: '/',
    element: <ProtectedRoute />,
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" />,
      },
      {
        path: 'admin/prompts',
        element: <AdminPrompts />,
        handle: { breadcrumb: 'Prompt Management' },
      },
      {
        path: 'documents',
        element: <Documents />,
        handle: { breadcrumb: 'Documents' },
      },
      {
        path: 'documents/:id',
        element: <DocumentChunksPage />,
        handle: { breadcrumb: 'Document Extracts' },
      },
      {
        path: 'dashboard',
        element: <Dashboard />,
        handle: { breadcrumb: 'Dashboard' },
      },
      {
        path: 'dashboard/settings',
        element: <Settings />,
        handle: { breadcrumb: 'Settings' },
      },
      {
        path: 'dashboard/materiality-settings',
        element: <MaterialitySettings />,
        handle: { breadcrumb: 'Material Topics' },
      },
      {
        path: 'dashboard/:id',
        element: <DataRequestDetailPage />,
        handle: { breadcrumb: 'Data Request' },
      },
      {
        path: '*',
        element: <Navigate to="/" />,
      },
    ],
  },
];
