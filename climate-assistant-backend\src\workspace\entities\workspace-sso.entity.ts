import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  Index,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Workspace } from './workspace.entity';

@Entity('workspace_sso')
@Index('idx_workspace_sso_domain', ['domain']) // Fast domain lookups
@Index('idx_workspace_sso_workspace_enabled', ['workspaceId', 'enabled']) // Fast workspace checks
export class WorkspaceSSO {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  @Index()
  workspaceId: string;

  @ManyToOne(() => Workspace, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;

  // Normalized domain for fast lookups (e.g., "company.com")
  @Column({ type: 'varchar', length: 255 })
  @Index() // Critical for fast domain lookups
  domain: string;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  // WorkOS Organization ID for this domain
  @Column({ type: 'varchar', length: 255 })
  workosOrganizationId: string;

  // WorkOS Connection ID
  @Column({ type: 'varchar', length: 255 })
  workosConnectionId: string;

  // SSO Protocol (saml, oauth, etc.)
  @Column({ type: 'varchar', length: 50, default: 'saml' })
  protocol: 'saml' | 'oauth' | 'oidc';

  // JSON for additional config (now just supplementary data)
  @Column({ type: 'jsonb', nullable: true })
  additionalConfig?: {
    defaultRoleId?: string;
    attributeMapping?: Record<string, string>;
    customSettings?: Record<string, any>;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
