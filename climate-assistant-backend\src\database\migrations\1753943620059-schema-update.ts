import { MigrationInterface, QueryRunner } from 'typeorm';

export class SchemaUpdate1753943620059 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create workspace_sso table for optimized domain lookups
    await queryRunner.query(`
      CREATE TABLE "workspace_sso" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "workspaceId" uuid NOT NULL,
        "domain" character varying(255) NOT NULL,
        "enabled" boolean NOT NULL DEFAULT true,
        "workosOrganizationId" character varying(255) NOT NULL,
        "workosConnectionId" character varying(255) NOT NULL,
        "protocol" character varying(50) NOT NULL DEFAULT 'saml',
        "additionalConfig" jsonb,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_workspace_sso_id" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "workspace_sso" 
      ADD CONSTRAINT "FK_workspace_sso_workspace" 
      FOREIGN KEY ("workspaceId") 
      REFERENCES "workspace"("id") 
      ON DELETE CASCADE
    `);

    // Add indexes for fast lookups
    await queryRunner.query(`
      CREATE INDEX "IDX_workspace_sso_domain" 
      ON "workspace_sso" ("domain")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_workspace_sso_workspace_enabled" 
      ON "workspace_sso" ("workspaceId", "enabled")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_workspace_sso_workspace_id" 
      ON "workspace_sso" ("workspaceId")
    `);

    // Add unique constraint on domain to prevent duplicates
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_workspace_sso_domain_unique" 
      ON "workspace_sso" ("domain")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes first
    await queryRunner.query(`DROP INDEX "IDX_workspace_sso_domain_unique"`);
    await queryRunner.query(`DROP INDEX "IDX_workspace_sso_workspace_id"`);
    await queryRunner.query(`DROP INDEX "IDX_workspace_sso_workspace_enabled"`);
    await queryRunner.query(`DROP INDEX "IDX_workspace_sso_domain"`);

    // Drop foreign key constraint
    await queryRunner.query(`
      ALTER TABLE "workspace_sso" 
      DROP CONSTRAINT "FK_workspace_sso_workspace"
    `);

    // Drop table
    await queryRunner.query(`DROP TABLE "workspace_sso"`);
  }
}
