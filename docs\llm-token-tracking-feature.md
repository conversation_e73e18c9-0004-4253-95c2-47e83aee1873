# LLM Token Tracking Service - Feature Document

## Overview

This document outlines the design and implementation of a comprehensive LLM Token Tracking Service that will monitor, record, and analyze all LLM API usage across the Climate Assistant platform. This service will enable detailed cost analysis, usage optimization, and billing transparency for each workspace and task.

## Technical Requirements

### 1. Integration Points
- **LLM Rate Limiter Service**: Primary integration point for token capture as all the LLM call is initiated from this service.

## Data Model

### Core Entity: LlmTokenUsage

The `LlmTokenUsage` entity will serve as the central record for all LLM API interactions:

```typescript
@Entity('llm_token_usage')
export class LlmTokenUsage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Request Identification
  @Column('uuid')
  workspaceId: string;
  
  @Column('uuid', { nullable: true })
  userId: string;
  
  @Column('uuid', { nullable: true })
  projectId: string;

  @Column({
    type: 'enum',
    enum: LLM_MODELS,
  })
  model: LLM_MODELS; //o1, o4, etc

  @Column({
    type: 'enum'.
    enum: LLM_PROVIDER
  })
  llmProvider: LLM_PROVIDER; //azure, openAi, etc

  @Column({ type: 'varchar', length: 50, nullable: true })
  modelVersion: string;

  @Column({type: 'varchar': length: 300, nullable: true})
  endpoint: string;

  // Token Metrics
  @Column({ type: 'integer' })
  inputTokens: number;

  @Column({ type: 'integer' })
  outputTokens: number;

  @Column({ type: 'integer' })
  totalTokens: number;

  // Task Classification
  @Column({
    type: 'string',
    length: 150
  })
  taskType: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  taskSubtype: string;

  @Column('uuid', { nullable: true })
  taskRelatedEntityId: string;

  @Column({ type: 'integer', nullable: true })
  responseTimeMs: number;

  // Status & Quality
  @Column({
    type: 'enum',
    enum: RequestStatus,
    default: RequestStatus.Success,
  })
  status: RequestStatus;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @CreateDateColumn()
  createdAt: Date;

  // Relationships
  @ManyToOne(() => Workspace, (workspace) => workspace.id)
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;
}
```

## Service Architecture

### 1. Integration with LlmRateLimiterService

```typescript
// Enhanced makeRequest method in LlmRateLimiterService
private async makeRequest({
  tokens,
  model,
  messages,
  temperature,
  json,
  context, // New parameter for tracking context
}: HandleRequestProps): Promise<any> {
  const startTime = Date.now();
  
  try {
    // ... existing request logic ...
    
    // Record successful usage
    await this.tokenTrackingService.recordTokenUsage({
      workspaceId: context.workspaceId,
      userId: context.userId,
      projectId: context.projectId,
      model,
      inputTokens: promptTokens,
      outputTokens: completionTokens,
      totalTokens: promptTokens + completionTokens,
      totalCost: calculatedCost,
      taskType: context.taskType,
      taskSubtype: context.taskSubtype,
      taskRelatedEntityId: context.entityId,
      endpoint: context.endpoint,
      responseTimeMs: Date.now() - startTime,
      status: RequestStatus.SUCCESS,
    });
    
    return result;
  } catch (error) {
    // Record failed usage
    await this.tokenTrackingService.recordTokenUsage({
      // ... same fields as success case ...
      status: this.mapErrorToStatus(error),
      errorMessage: error.message,
    });
    
    throw error;
  }
}
```


## Business Requirements

### Primary Objectives
1. **Cost Transparency**: Provide detailed cost breakdowns per workspace, user, and task
2. **Usage Analytics**: Enable data-driven decisions about LLM usage patterns
3. **Budget Management**: Support workspace-level cost controls and alerts
4. **Audit Trail**: Maintain complete records of all LLM interactions for compliance
5. **Performance Optimization**: Identify expensive operations and optimization opportunities

### Key Stakeholders
- **Workspace Administrators**: Need cost visibility and budget controls
- **Data Controllers**: Require audit trails and compliance reporting
- **Product Team**: Need usage analytics for feature optimization
- **Finance Team**: Require accurate cost allocation and billing data

## Functional Requirements

### 1. Token Usage Tracking
- **Real-time Tracking**: Capture every LLM API call with detailed metadata
- **Multi-Model Support**: Track usage across all supported LLM models (GPT-4o, o3, o4-mini, etc.)
- **Granular Recording**: Record input tokens, output tokens, and calculated costs
- **Context Preservation**: Maintain relationship between tokens and business operations

### 2. Cost Calculation & Attribution
- **Dynamic Pricing**: Support model-specific pricing that can be updated
- **Workspace Attribution**: Accurately attribute costs to specific workspaces
- **Task Classification**: Categorize usage by functional areas (e.g., datapoint generation, document processing)
- **User Attribution**: Track individual user contributions to workspace costs

## Monitoring & Alerting

### Key Metrics
- **Cost Velocity**: Rate of cost increase per workspace
- **Usage Anomalies**: Unusual spikes in token consumption
- **Error Rates**: Failed request percentages by model
- **Performance**: Response time degradation


## Analytics
- **Dashboard Views**: Real-time cost and usage dashboards
- **Historical Analysis**: Trend analysis over time periods
- **Export Capabilities**: CSV/Excel exports for external analysis
- **Alert System**: Configurable alerts for budget thresholds
