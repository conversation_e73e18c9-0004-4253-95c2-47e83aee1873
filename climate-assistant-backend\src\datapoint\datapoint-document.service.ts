import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DatapointRequest } from './entities/datapoint-request.entity';
import { Document } from 'src/document/entities/document.entity';
import { TimingTracker } from 'src/util/timing-util';
import { ChatCompletionMessageParam } from 'openai/resources';
import { LLM_MODELS } from 'src/constants';
import { LlmRateLimiterService } from 'src/llm/services/llm-rate-limiter.service';
import throat from 'throat';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class DatapointDocumentService {
  constructor(
    @InjectRepository(DatapointDocumentChunk)
    private readonly datapointDocumentChunkMapRepository: Repository<DatapointDocumentChunk>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    private readonly llmRateLimiterService: LlmRateLimiterService
  ) {}

  private readonly logger = new WorkerLogger(DatapointDocumentService.name);
  private readonly MAX_DATAPOINT_GENERATION_DOCUMENT_CHUNKS_LENGHT = 300000; // Characters. This is NOT TOKENS
  private readonly THRESHOLD_DOCUMENT_CHUNK_LENGTH_UNTIL_REDUCING = 3000; // Characters. This is NOT TOKENS
  private readonly MAX_CHATCONTENT_MESSAGE_LENGTH = 300000; // Characters. This is NOT TOKENS
  private readonly limit = throat(10);
  private readonly documentReductionLimit = throat(5); // Limit for parallel document reduction

  async loadDocumentLinks(datapointRequestId: string) {
    const documentLinks = await this.datapointDocumentChunkMapRepository.find({
      where: { datapointRequestId, active: true },
      relations: ['documentChunk', 'documentChunk.document'],
    });
    return documentLinks;
  }

  async generateDatapointGenerationContextFromLinkedDocumentChunks(
    datapointRequest: DatapointRequest,
    userId: string,
    workspaceId: string,
    timingTracker?: TimingTracker
  ): Promise<{
    context: string;
    documentChunksIndex: string[];
  }> {
    const documentTypePriority: { [key: string]: number } = {
      'Business Report': 1,
      'Sustainability Report': 2,
      'Materiality Analysis': 3,
      Policy: 4,
      Strategy: 5,
      Other: 6,
    };

    this.logger.log(
      `Number of linked DocumentChunks: ${datapointRequest.datapointDocumentChunkMap.filter((map) => map.active).length}`
    );

    //Get all linked Document Original Chunks (shortening of them happens in the call)
    const documentChunks = datapointRequest.datapointDocumentChunkMap
      .filter((map) => map.active)
      .map((datapointDocumentChunk) => {
        return {
          ...datapointDocumentChunk.documentChunk,
          key_information: datapointDocumentChunk.key_information,
          documentTitle: datapointDocumentChunk.documentChunk.document.name,
          year: datapointDocumentChunk.documentChunk.document.year,
          month: datapointDocumentChunk.documentChunk.document.month,
          day: datapointDocumentChunk.documentChunk.document.day,
          documentType:
            datapointDocumentChunk.documentChunk.document.documentType,
          remarks: datapointDocumentChunk.documentChunk.document.remarks,
        };
      })
      .sort((a, b) => {
        // Compare by date (most recent first)
        const dateA = new Date(a.year || 0, (a.month || 1) - 1, a.day || 1);
        const dateB = new Date(b.year || 0, (b.month || 1) - 1, b.day || 1);

        if (dateB.getTime() !== dateA.getTime()) {
          return dateB.getTime() - dateA.getTime(); // Most recent first
        }

        // Compare by document type priority
        const typePriorityA = documentTypePriority[a.documentType] || 999;
        const typePriorityB = documentTypePriority[b.documentType] || 999;

        if (typePriorityA !== typePriorityB) {
          return typePriorityA - typePriorityB; // Lower priority value comes first
        }

        // Check for user remarks
        const hasRemarkA = a.remarks ? 1 : 0;
        const hasRemarkB = b.remarks ? 1 : 0;

        return hasRemarkB - hasRemarkA; // Chunks with remarks come first
      });

    const totalDocumentChunkLength = documentChunks.reduce(
      (acc, documentChunk) => acc + documentChunk.content.length,
      0
    );
    const numberOfLinkedDocumentChunks = documentChunks.length;

    this.logger.log(
      `${datapointRequest.id}: Total DocumentChunkLength ${totalDocumentChunkLength}, Number of LinkedChunks: ${numberOfLinkedDocumentChunks}`
    );

    const generateMetadataSection = async (
      documentChunk: DocumentChunk
    ): Promise<string> => {
      let metadata = '';

      if (documentChunk.metadataJson) {
        metadata += `<Metadata>\nPart 1: ${documentChunk.metadataJson} \nPart 2:`;
      } else {
        metadata += `<Metadata>\nNo Base Metadata`;
      }

      // Fetch the document using documentId
      if (documentChunk.documentId) {
        const document = await this.documentRepository.findOne({
          where: { id: documentChunk.documentId },
        });

        if (document) {
          const documentMetadata = [
            document.documentType && `Document Type: ${document.documentType}`,
            document.esrsCategory &&
              `ESRS Category: ${document.esrsCategory.join(', ')}`,
            document.year && `Year: ${document.year}`,
            document.month && `Month: ${document.month}`,
            document.day && `Day: ${document.day}`,
            document.remarks && `Remarks: ${document.remarks}`,
          ].filter(Boolean);

          if (documentMetadata.length > 0) {
            metadata += `\n\n${documentMetadata.join('\n')}`;
          }
        }
      }
      metadata += '\n</Metadata>\n\n';
      return metadata;
    };

    const documentChunkSplitter = '\n\n';
    let generatedContext = '<Context>\n\n';

    if (
      totalDocumentChunkLength >
      this.MAX_DATAPOINT_GENERATION_DOCUMENT_CHUNKS_LENGHT
    ) {
      if (timingTracker) {
        timingTracker.start('Document Chunk Reduction');
      }

      // Group chunks by document to reduce API calls
      const chunksNeedingReduction = documentChunks.filter(
        (dc) =>
          (!dc.key_information || dc.key_information === 'false') &&
          dc.content.length >
            this.THRESHOLD_DOCUMENT_CHUNK_LENGTH_UNTIL_REDUCING
      );

      this.logger.log(
        `${datapointRequest.id}: Found ${chunksNeedingReduction.length} chunks needing reduction out of ${documentChunks.length} total chunks`
      );

      // Process chunks that need reduction in groups by document
      if (chunksNeedingReduction.length > 0) {
        await this.reduceDocumentChunksInGroups(
          datapointRequest,
          chunksNeedingReduction,
          userId,
          workspaceId,
          timingTracker
        );
      }

      const processedChunks = await Promise.all(
        documentChunks.map(async (documentChunk, index) =>
          this.limit(async () => {
            const metadata = await generateMetadataSection(documentChunk);
            const chunkContent = `Document Chunk ID: chunk-${index + 1}
            Document: ${documentChunk.documentTitle} (${documentChunk.documentType}) - ${documentChunk.year}-${documentChunk.month}-${documentChunk.day}
            Page: ${documentChunk.page}:
            Metadata: ${metadata}
            --------------------------------------`;

            if (
              documentChunk.key_information &&
              documentChunk.key_information !== 'false'
            ) {
              return chunkContent + documentChunk.key_information;
            } else {
              return chunkContent + documentChunk.content;
            }
          })
        )
      );

      if (timingTracker) {
        timingTracker.end('Document Chunk Reduction');
      }

      generatedContext += processedChunks.join(documentChunkSplitter);
    } else {
      generatedContext += await Promise.all(
        documentChunks.map(async (documentChunk, index) => {
          const metadata = await generateMetadataSection(documentChunk);
          return `
          Document Chunk ID: chunk-${index + 1}
          Document: ${documentChunk.documentTitle} (${documentChunk.documentType}) - ${documentChunk.year}-${documentChunk.month}-${documentChunk.day}
          Page: ${documentChunk.page}:
          Metadata: ${metadata}
          --------------------------------------
          ${documentChunk.content}
          --------------------------------------`;
        })
      ).then((chunks) => chunks.join(documentChunkSplitter));
    }

    if (generatedContext.length > this.MAX_CHATCONTENT_MESSAGE_LENGTH) {
      this.logger.log(
        `${datapointRequest.id}: Generated context exceeds maximum chat content message length. Shortening context.`
      );
      generatedContext = generatedContext.slice(
        0,
        this.MAX_CHATCONTENT_MESSAGE_LENGTH
      );
    }

    generatedContext + '\n</Context>';

    return {
      context: generatedContext,
      documentChunksIndex: documentChunks.map((dc) => dc.id),
    };
  }

  /**
   * The method processes chunks that need reduction and updates their key_information
   * in the database for use in the subsequent processing pipeline.
   */
  async reduceDocumentChunksInGroups(
    datapointRequest: DatapointRequest,
    documentChunks: any[], // Enhanced DocumentChunk with additional properties
    userId: string,
    workspaceId: string,
    timingTracker?: TimingTracker
  ) {
    // Group chunks by document to reduce the number of API calls
    // Instead of calling the LLM for each chunk individually, we group chunks
    // from the same document and process them together
    const chunksByDocument = new Map<string, any[]>();

    for (const chunk of documentChunks) {
      const documentKey = `${chunk.documentId}-${chunk.documentTitle}`;
      if (!chunksByDocument.has(documentKey)) {
        chunksByDocument.set(documentKey, []);
      }
      chunksByDocument.get(documentKey)!.push(chunk);
    }

    this.logger.log(
      `${datapointRequest.id}: Processing ${chunksByDocument.size} documents with chunks needing reduction. Total chunks: ${documentChunks.length}`
    );

    // Process each document group in parallel (5 at a time)
    const documentGroups = Array.from(chunksByDocument.entries());
    const results = await Promise.allSettled(
      documentGroups.map(([documentKey, chunks]) =>
        this.documentReductionLimit(async () => {
          try {
            await this.reduceDocumentChunksForSingleDocument(
              datapointRequest,
              chunks,
              userId,
              workspaceId,
              timingTracker
            );
          } catch (error) {
            this.logger.error(
              `${datapointRequest.id}: Failed to reduce chunks for document ${documentKey}:`,
              error
            );
            throw error;
          }
        })
      )
    );

    // Log any failures
    const failures = results.filter((result) => result.status === 'rejected');
    if (failures.length > 0) {
      this.logger.warn(
        `${datapointRequest.id}: ${failures.length} document groups failed to process`
      );
    }
  }

  async reduceDocumentChunksForSingleDocument(
    datapointRequest: DatapointRequest,
    documentChunks: any[], // Enhanced DocumentChunk with additional properties
    userId: string,
    workspaceId: string,
    timingTracker?: TimingTracker
  ) {
    this.logger.log(
      `${datapointRequest.id}: Reducing ${documentChunks.length} chunks from document: ${documentChunks[0]?.documentTitle}`
    );

    const chunksContent = documentChunks.map((chunk) => ({
      chunkId: chunk.id,
      page: chunk.page,
      content: chunk.content,
    }));

    // Create a prompt for multiple chunks from the same document
    const promptContent = `You are analyzing document chunks for ESRS datapoint extraction. Your task is to reduce lengthy chunks to only relevant information.

DATAPOINT REQUEST:
${datapointRequest.esrsDatapoint?.name || datapointRequest.esrsDatapointId}

DATAPOINT REQUIREMENT:
${datapointRequest.esrsDatapoint.lawText} 
${datapointRequest.esrsDatapoint.lawTextAR}

DOCUMENT INFORMATION:
- Document: ${documentChunks[0].documentTitle} (${documentChunks[0].documentType})
- Date: ${documentChunks[0].year}-${documentChunks[0].month}-${documentChunks[0].day}

ANALYZE THE FOLLOWING CHUNKS:
${chunksContent
  .map(
    (chunk, index) => `
--- CHUNK ${index + 1} ---
CHUNK_ID: ${chunk.chunkId}
PAGE: ${chunk.page}
CONTENT:
${chunk.content}
--- END CHUNK ${index + 1} ---
`
  )
  .join('\n')}

INSTRUCTIONS:
For each chunk, determine if it contains information relevant to the datapoint requirement. If relevant, extract only the key information. If not relevant, mark it for exclusion.

REQUIRED OUTPUT FORMAT (JSON only, no additional text):
{
  "chunk_reductions": [
    {
      "chunkId": "exact_chunk_id_from_above",
      "keep_chunk_as_is": "true/false",
      "key_information": "extracted key information if keep_chunk_as_is is false, otherwise full content"
    }
  ]
}

IMPORTANT: 
- Use the exact CHUNK_ID values provided above
- Do not modify or truncate the chunk IDs
- Include ALL chunks in your response
- If keep_chunk_as_is is "true", copy the key_information from the original content
- If keep_chunk_as_is is "false", provide a concise summary of relevant information only
`;

    const extractedChunkInformation: ChatCompletionMessageParam[] = [
      {
        role: 'user',
        content: promptContent,
      },
    ];

    type GroupReductionResponse = {
      chunk_reductions: Array<{
        chunkId: string;
        keep_chunk_as_is: string;
        key_information: string;
      }>;
    };

    const reductionResponse = await this.llmRateLimiterService.handleRequest({
      model: LLM_MODELS['o4-mini'],
      messages: extractedChunkInformation,
      json: true,
      temperature: 0.2,
      userId,
      workspaceId,
      taskType: 'reduceDocumentChunksForSingleDocument',
      taskRelatedEntityId: datapointRequest.id,
    });

    if (reductionResponse.status === 400) {
      throw new Error(reductionResponse.response);
    }

    const result = reductionResponse.response as GroupReductionResponse;

    // Validate response structure
    if (!result.chunk_reductions || !Array.isArray(result.chunk_reductions)) {
      throw new Error(
        'Invalid response format: missing chunk_reductions array'
      );
    }

    // Create a map of expected chunk IDs for validation
    const expectedChunkIds = new Set(
      chunksContent.map((chunk) => chunk.chunkId)
    );

    this.logger.log(
      `${datapointRequest.id}: Expected chunk IDs: ${Array.from(expectedChunkIds).join(', ')}`
    );
    this.logger.log(
      `${datapointRequest.id}: Received chunk IDs: ${result.chunk_reductions.map((r) => r.chunkId).join(', ')}`
    );

    // Update each chunk with its reduction result
    for (const reduction of result.chunk_reductions) {
      try {
        // Clean up the chunk ID in case of corruption
        let cleanChunkId = reduction.chunkId;

        // Remove common corruptions
        cleanChunkId = cleanChunkId.replace(/-generation$/, '');
        cleanChunkId = cleanChunkId.replace(/^chunk-\d+-/, '');

        // If the cleaned ID is not in expected IDs, try to find a match
        if (!expectedChunkIds.has(cleanChunkId)) {
          const possibleMatch = Array.from(expectedChunkIds).find(
            (id) => id.includes(cleanChunkId) || cleanChunkId.includes(id)
          );
          if (possibleMatch) {
            this.logger.warn(
              `${datapointRequest.id}: Corrected corrupted chunk ID ${reduction.chunkId} to ${possibleMatch}`
            );
            cleanChunkId = possibleMatch;
          } else {
            this.logger.error(
              `${datapointRequest.id}: Could not match chunk ID ${reduction.chunkId} to any expected ID`
            );
            continue;
          }
        }

        await this.datapointDocumentChunkMapRepository.update(
          {
            documentChunkId: cleanChunkId,
            datapointRequestId: datapointRequest.id,
          },
          {
            key_information:
              reduction.keep_chunk_as_is === 'false'
                ? 'false'
                : reduction.key_information,
          }
        );
      } catch (error) {
        this.logger.error(
          `${datapointRequest.id}: Failed to update chunk ${reduction.chunkId}:`,
          error
        );
      }
    }
  }
}
