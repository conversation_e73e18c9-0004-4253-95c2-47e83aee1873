import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';

import {
  fetchProjectById,
  fetchProjects,
} from '@/api/project-settings/project-settings.api';
import { Project } from '@/types/project';
import { ESRS_SORT_ORDER } from '@/lib/config';
import { DataRequestStatus } from '@/types';

export interface DashboardListing {
  id: string;
  drId: string;
  esrs: string;
  name: string;
  status: string;
  dueDate: Date | null;
  responsiblePerson: string | null;
}

export function useDashboard() {
  const [project, setProject] = useState<Project | null>(null);
  const [dataRequests, setDataRequests] = useState<DashboardListing[]>([]);
  const [progress, setProgress] = useState(0);
  const [totalDRs, setTotalDRs] = useState(0);

  const { data: projects } = useQuery<Project[]>({
    queryKey: ['projects'],
    queryFn: fetchProjects,
  });

  const { data: projectMain, isLoading: loading } = useQuery({
    enabled: !!projects,
    queryKey: ['project', projects],
    queryFn: () => fetchProjectById(projects![0].id),
  });

  useEffect(() => {
    if (!projectMain) return;
    setProject({
      id: projectMain.id,
      workspaceId: projectMain.workspaceId,
      name: projectMain.name,
      primaryContentLanguage: projectMain.primaryContentLanguage,
      reportTextGenerationRules: projectMain.reportTextGenerationRules,
      reportingYear: projectMain.reportingYear,
      createdBy: projectMain.createdBy,
      createdAt: projectMain.createdAt,
    });

    if (projectMain.dataRequests && projectMain.dataRequests.length > 0) {
      const dataRequests = projectMain.dataRequests
        .sort((a, b) => {
          // First sort by ESRS category using the predefined mapping
          const esrsSortOrderA =
            ESRS_SORT_ORDER[a.disclosureRequirement.esrs] || 999; // Default to a high number if not found
          const esrsSortOrderB =
            ESRS_SORT_ORDER[b.disclosureRequirement.esrs] || 999;

          if (esrsSortOrderA !== esrsSortOrderB) {
            return esrsSortOrderA - esrsSortOrderB;
          }

          // If ESRS categories are the same, sort by disclosure requirement sort order
          return a.disclosureRequirement.sort - b.disclosureRequirement.sort;
        })
        .map((dataRequest) => {
          const progressPercentage =
            (dataRequest as any).datapointStats?.progressPercentage || 0;
          return {
            id: dataRequest.id,
            drId: dataRequest.disclosureRequirement.dr,
            esrs: dataRequest.disclosureRequirement.esrs,
            name: dataRequest.disclosureRequirement.name,
            status: dataRequest.status,
            dueDate: dataRequest.dueDate,
            responsiblePerson:
              dataRequest.responsiblePerson?.name ||
              dataRequest.responsiblePerson?.email ||
              null,
            progress: progressPercentage,
          };
        });
      setDataRequests(dataRequests);
      const reportableDRs = dataRequests.filter(
        (dr) => dr.status !== DataRequestStatus.NotReported
      );
      const completedDRs = reportableDRs.filter(
        (dr) => dr.status === DataRequestStatus.Complete
      ).length;
      const totalReportableDRs = reportableDRs.length;
      setTotalDRs(totalReportableDRs);
      setProgress(completedDRs);
    }
  }, [projectMain]);

  return { dataRequests, project, loading, progress, totalDRs };
}
