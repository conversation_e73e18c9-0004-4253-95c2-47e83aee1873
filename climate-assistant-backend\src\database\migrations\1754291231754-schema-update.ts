import { MigrationInterface, QueryRunner } from 'typeorm';
// Migration to create llm_token_usage table with necessary fields and indexes
// This will be used to track token usage and cost incurred by LLM API calls
export class SchemaUpdate1754291231754 implements MigrationInterface {
  name = 'SchemaUpdate1754291231754';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."llm_token_usage_model_enum" AS ENUM('gpt-4o', 'gpt-4o-mini', 'o3-mini', 'deepseek-r1', 'o3', 'o4-mini')`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."llm_token_usage_llmprovider_enum" AS ENUM('OPENAI', 'AZURE')`
    );
    await queryRunner.query(
      `CREATE TYPE "public"."llm_token_usage_status_enum" AS ENUM('SUCCESS', 'PENDING', 'FAILED')`
    );
    await queryRunner.query(
      `CREATE TABLE "llm_token_usage" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "workspaceId" uuid NOT NULL, "userId" uuid, "projectId" uuid, "model" "public"."llm_token_usage_model_enum" NOT NULL, "llmProvider" "public"."llm_token_usage_llmprovider_enum" NOT NULL, "modelVersion" character varying(50), "endpoint" character varying(300), "inputTokens" integer NOT NULL, "outputTokens" integer NOT NULL, "totalTokens" integer NOT NULL, "taskType" character varying(150) NOT NULL, "taskSubtype" character varying(100), "taskRelatedEntityId" uuid, "responseTimeMs" integer, "status" "public"."llm_token_usage_status_enum" NOT NULL DEFAULT 'SUCCESS', "errorMessage" text, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_75394635c6a59aa0776eb89193c" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b0c5b7eb7ae9964bbe9e94e7b5" ON "llm_token_usage" ("model", "createdAt") `
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_21b0ab7e01912d8d960ca2e4fa" ON "llm_token_usage" ("workspaceId", "taskType") `
    );

    await queryRunner.query(
      `ALTER TABLE "llm_token_usage" ADD CONSTRAINT "FK_7afdde9bdf4923f68308ac5d392" FOREIGN KEY ("workspaceId") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "llm_token_usage" DROP CONSTRAINT "FK_7afdde9bdf4923f68308ac5d392"`
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_21b0ab7e01912d8d960ca2e4fa"`
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b0c5b7eb7ae9964bbe9e94e7b5"`
    );
    await queryRunner.query(`DROP TABLE "llm_token_usage"`);
    await queryRunner.query(`DROP TYPE "public"."llm_token_usage_status_enum"`);
    await queryRunner.query(
      `DROP TYPE "public"."llm_token_usage_llmprovider_enum"`
    );
    await queryRunner.query(`DROP TYPE "public"."llm_token_usage_model_enum"`);
  }
}
