import { Workspace } from '../../workspace/entities/workspace.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { LLM_PROVIDER, RequestStatus, LLM_MODELS } from '../enums';

@Entity('llm_token_usage')
@Index(['workspaceId', 'taskType'])
@Index(['model', 'createdAt'])
export class LlmTokenUsage {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  // Request Identification
  @Column('uuid')
  workspaceId: string;

  @Column('uuid', { nullable: true })
  userId: string;

  @Column('uuid', { nullable: true })
  projectId: string;

  @Column({
    type: 'enum',
    enum: LLM_MODELS,
  })
  model: LLM_MODELS;

  @Column({
    type: 'enum',
    enum: LLM_PROVIDER,
  })
  llmProvider: LLM_PROVIDER;

  @Column({ type: 'varchar', length: 50, nullable: true })
  modelVersion: string;

  @Column({ type: 'varchar', length: 300, nullable: true })
  endpoint: string;

  // Token Metrics
  @Column({ type: 'integer' })
  inputTokens: number;

  @Column({ type: 'integer' })
  outputTokens: number;

  @Column({ type: 'integer' })
  totalTokens: number;

  // Task Classification
  @Column({
    type: 'varchar',
    length: 150,
  })
  taskType: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  taskSubtype: string;

  @Column('uuid', { nullable: true })
  taskRelatedEntityId: string;

  @Column({ type: 'integer', nullable: true })
  responseTimeMs: number;

  // Status & Quality
  @Column({
    type: 'enum',
    enum: RequestStatus,
    default: RequestStatus.Success,
  })
  status: RequestStatus;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @CreateDateColumn()
  createdAt: Date;

  // Relationships
  @ManyToOne(() => Workspace, (workspace) => workspace.id)
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;
}
