/**
 * AI Chunk-Linking Miss Analysis Script
 *
 * This script analyzes why the AI failed to automatically link certain document chunks
 * to ESRS datapoints by examining manually-created links (user corrections).
 *
 * How it works:
 * 1. Queries the database for chunks linked manually (not by AI user ID)
 * 2. For each missed chunk, runs the exact same AI classification logic 3 times
 * 3. If the AI still fails to match after 3 rounds, performs detailed miss analysis
 * 4. Outputs comprehensive JSON report with statistics and recommendations
 *
 * The goal is to identify patterns in AI failures and improve the classification prompts
 * or preprocessing logic to reduce manual correction workload.
 *
 * Usage: ts-node -r tsconfig-paths/register src/scripts/analysis/analyze-ai-misses.ts <workspaceId>
 */

import { CliRunner } from '../cli-runner';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../app.module';
import { DataSource } from 'typeorm';
import { LlmRateLimiterService } from '../../llm/services/llm-rate-limiter.service';
import { PromptService } from '../../prompts/prompts.service';
import { LLM_MODELS } from '../../constants';
import { GLOBAL_AI_USER_UUID } from '../../util/config';
import { ChatCompletionMessageParam } from 'openai/resources/chat';
import { WorkerLogger } from '../../shared/logger.service';
import * as fs from 'fs';
import * as path from 'path';

interface MissedChunkData {
  chunkId: string;
  chunkContent: string;
  chunkPage: number;
  datapointId: string;
  datapointName: string;
  dataType: string;
  lawText: string;
  lawTextAR: string;
  documentName: string;
  workspaceId: string;
  workspaceName: string;
  createdBy: string;
  disclosureRequirement: string;
}

interface ValidationResult {
  chunkId: string;
  datapointId: string;
  round1_matched: boolean;
  round2_matched: boolean;
  round3_matched: boolean;
  final_missed: boolean;
  miss_analysis?: {
    reasons: string[];
    complexity_score: number;
    content_clarity: string;
    prompt_adequacy: string;
    recommendations: string[];
  };
}

interface AnalysisOutput {
  timestamp: string;
  workspaceId: string;
  totalMissedChunks: number;
  processedChunks: number;
  finalMisses: number;
  revalidatedCorrectly: number;
  results: ValidationResult[];
}

/**
 * AI Miss Analysis Script
 *
 * Analyzes why AI failed to link certain chunks to datapoints by:
 * 1. Running 3 validation rounds with the original prompts
 * 2. For persistent misses, analyzing the reasons for failure
 *
 * Usage: ts-node -r tsconfig-paths/register src/scripts/analysis/analyze-ai-misses.ts <workspaceId>
 */
class AnalyzeAIMissesScript extends CliRunner {
  private outputFile: string;
  private readonly scriptLogger = new WorkerLogger('AnalyzeAIMissesScript');

  async run(...args: string[]): Promise<void> {
    const workspaceId = args[0];
    if (!workspaceId) {
      throw new Error(
        'workspaceId is required. Usage: ts-node -r tsconfig-paths/register src/scripts/analysis/analyze-ai-misses.ts <workspaceId>'
      );
    }

    this.scriptLogger.log(
      `Starting AI miss analysis for workspace: ${workspaceId}`
    );

    // Setup output file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputDir = path.join(
      process.cwd(),
      'src',
      'scripts',
      'analysis',
      'output'
    );

    // Ensure output directory exists
    await fs.promises.mkdir(outputDir, { recursive: true });

    this.outputFile = path.join(
      outputDir,
      `ai-miss-analysis-${workspaceId}-${timestamp}.json`
    );

    // Get NestJS application context to access services
    const app = await NestFactory.createApplicationContext(AppModule);

    try {
      const dataSource = app.get(DataSource);
      const llmRateLimiterService = app.get(LlmRateLimiterService);
      const promptService = app.get(PromptService);

      // Get missed chunks data
      const missedChunks = await this.getMissedChunks(workspaceId, dataSource);
      this.scriptLogger.log(
        `Found ${missedChunks.length} missed chunks to analyze`
      );

      if (missedChunks.length === 0) {
        this.scriptLogger.log('No missed chunks found for this workspace');
        return;
      }

      // Initialize output
      const analysisOutput: AnalysisOutput = {
        timestamp: new Date().toISOString(),
        workspaceId,
        totalMissedChunks: missedChunks.length,
        processedChunks: 0,
        finalMisses: 0,
        revalidatedCorrectly: 0,
        results: [],
      };

      // Write initial output
      await this.writeOutput(analysisOutput);

      // Process each missed chunk
      for (let i = 0; i < missedChunks.length; i++) {
        const chunk = missedChunks[i];
        this.scriptLogger.log(
          `Processing chunk ${i + 1}/${missedChunks.length}: ${chunk.chunkId}`
        );

        try {
          const result = await this.analyzeChunk(
            chunk,
            dataSource,
            llmRateLimiterService,
            promptService
          );
          analysisOutput.results.push(result);
          analysisOutput.processedChunks++;

          if (result.final_missed) {
            analysisOutput.finalMisses++;
          } else {
            analysisOutput.revalidatedCorrectly++;
          }

          // Write updated output after each chunk
          await this.writeOutput(analysisOutput);

          // Small delay to avoid overwhelming the API
          await new Promise((resolve) => setTimeout(resolve, 1000));
        } catch (error) {
          this.scriptLogger.error(
            `Error processing chunk ${chunk.chunkId}:`,
            error
          );
          // Continue with next chunk
        }
      }

      this.scriptLogger.log(
        `Analysis complete. Results written to: ${this.outputFile}`
      );
      this.scriptLogger.log(
        `Summary: ${analysisOutput.revalidatedCorrectly} revalidated correctly, ${analysisOutput.finalMisses} final misses`
      );
    } catch (error) {
      this.scriptLogger.error('Error in AI miss analysis:', error);
      throw error;
    } finally {
      await app.close();
    }
  }

  private async getMissedChunks(
    workspaceId: string,
    dataSource: DataSource
  ): Promise<MissedChunkData[]> {
    const query = `
      SELECT 
        ddc."documentChunkId" as chunk_id,
        dc.content as chunk_content,
        dc.page as chunk_page,
        edp."datapointId" as datapoint_id,
        edp.name as datapoint_name,
        edp."dataType" as data_type,
        edp."lawText" as law_text,
        edp."lawTextAR" as law_text_ar,
        d.name as document_name,
        w.id as workspace_id,
        w.name as workspace_name,
        ddc."createdBy" as created_by,
        edr.dr as disclosure_requirement
      FROM 
        datapoint_document_chunk ddc
        INNER JOIN document_chunk dc ON ddc."documentChunkId" = dc.id
        INNER JOIN document d ON dc."documentId" = d.id
        INNER JOIN datapoint_request dp ON ddc."datapointRequestId" = dp.id
        INNER JOIN esrs_datapoint edp ON dp."esrsDatapointId" = edp.id
        INNER JOIN data_request dr ON dp."dataRequestId" = dr.id
        INNER JOIN project p ON dr."projectId" = p.id
        INNER JOIN workspace w ON p."workspaceId" = w.id
        LEFT JOIN esrs_disclosure_requirement edr ON edp."esrsDisclosureRequirementId" = edr.id
      WHERE 
        w.id = $1
        AND ddc.active = true
        AND ddc."createdBy" != $2  -- Not created by AI
      ORDER BY 
        d.name, dc.page, edp."datapointId"
    `;

    const results = await dataSource.query(query, [
      workspaceId,
      GLOBAL_AI_USER_UUID,
    ]);

    return results.map((row) => ({
      chunkId: row.chunk_id,
      chunkContent: row.chunk_content,
      chunkPage: row.chunk_page,
      datapointId: row.datapoint_id,
      datapointName: row.datapoint_name,
      dataType: row.data_type,
      lawText: row.law_text,
      lawTextAR: row.law_text_ar,
      documentName: row.document_name,
      workspaceId: row.workspace_id,
      workspaceName: row.workspace_name,
      createdBy: row.created_by,
      disclosureRequirement: row.disclosure_requirement,
    }));
  }

  private async analyzeChunk(
    chunkData: MissedChunkData,
    dataSource: DataSource,
    llmRateLimiterService: LlmRateLimiterService,
    promptService: PromptService
  ): Promise<ValidationResult> {
    const result: ValidationResult = {
      chunkId: chunkData.chunkId,
      datapointId: chunkData.datapointId,
      round1_matched: false,
      round2_matched: false,
      round3_matched: false,
      final_missed: false,
    };

    // Get ESRS disclosure requirements for this chunk's DR
    const esrsDisclosureRequirements = await dataSource.query(
      `
      SELECT edr.*, 
        CASE 
          WHEN COUNT(edp.id) = 0 THEN '[]'::json
          ELSE array_to_json(array_agg(
            json_build_object(
              'id', edp.id,
              'datapointId', edp."datapointId",
              'name', edp.name,
              'dataType', edp."dataType",
              'lawText', edp."lawText",
              'lawTextAR', edp."lawTextAR"
            ) ORDER BY edp."datapointId"
          ))
        END as esrs_datapoints
      FROM esrs_disclosure_requirement edr
      LEFT JOIN esrs_datapoint edp ON edr.id = edp."esrsDisclosureRequirementId"
      WHERE edr.dr = $1
      GROUP BY edr.id, edr.dr, edr.esrs, edr.name
    `,
      [chunkData.disclosureRequirement]
    );

    if (esrsDisclosureRequirements.length === 0) {
      this.scriptLogger.warn(
        `No disclosure requirements found for DR: ${chunkData.disclosureRequirement}`
      );
      result.final_missed = true;
      return result;
    }

    const disclosureRequirement = esrsDisclosureRequirements[0];
    const datapoints = Array.isArray(disclosureRequirement.esrs_datapoints)
      ? disclosureRequirement.esrs_datapoints.filter(
          (dp) => dp && dp.id !== null
        )
      : [];

    this.scriptLogger.debug(
      `Found ${datapoints.length} datapoints for DR: ${chunkData.disclosureRequirement}`
    );

    if (datapoints.length === 0) {
      this.scriptLogger.warn(
        `No datapoints found for DR: ${chunkData.disclosureRequirement}`
      );
      result.final_missed = true;
      return result;
    }

    // Run 3 validation rounds
    for (let round = 1; round <= 3; round++) {
      this.scriptLogger.debug(
        `Running validation round ${round} for chunk ${chunkData.chunkId}`
      );

      const matched = await this.validateChunkDatapointMatch(
        chunkData,
        datapoints,
        disclosureRequirement,
        llmRateLimiterService,
        promptService
      );

      if (round === 1) result.round1_matched = matched;
      else if (round === 2) result.round2_matched = matched;
      else if (round === 3) result.round3_matched = matched;

      if (matched) {
        this.scriptLogger.debug(
          `Chunk ${chunkData.chunkId} matched in round ${round}`
        );
        break; // Stop if we get a match
      }
    }

    // If still no match after 3 rounds, analyze why it was missed
    if (
      !result.round1_matched &&
      !result.round2_matched &&
      !result.round3_matched
    ) {
      result.final_missed = true;
      this.scriptLogger.debug(
        `Analyzing miss reasons for chunk ${chunkData.chunkId}`
      );
      result.miss_analysis = await this.analyzeMissReasons(
        chunkData,
        datapoints,
        disclosureRequirement,
        llmRateLimiterService,
        promptService
      );
    }

    return result;
  }

  private async validateChunkDatapointMatch(
    chunkData: MissedChunkData,
    datapoints: any[],
    disclosureRequirement: any,
    llmRateLimiterService: LlmRateLimiterService,
    promptService: PromptService
  ): Promise<boolean> {
    try {
      // Use the same prompt as the original datapoint classification
      const datapointClassificationPrompt: ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: promptService.generateDatapointClassificationReasoningPrompt(
            {
              chunkContent: chunkData.chunkContent,
              esrsDatapoints: datapoints,
              disclosureRequirement: disclosureRequirement,
            }
          ),
        },
      ];

      const response = await llmRateLimiterService.handleRequest({
        model: LLM_MODELS['o4-mini'],
        messages: datapointClassificationPrompt,
        json: true,
        temperature: 0,
        taskType: 'scripted_chunk_datapoint_validation',
        taskRelatedEntityId: chunkData.chunkId,
        workspaceId: chunkData.workspaceId,
        userId: GLOBAL_AI_USER_UUID,
      });

      if (response.status === 400) {
        this.scriptLogger.warn(
          `LLM request failed with status 400 for chunk ${chunkData.chunkId}`
        );
        return false;
      }

      if (!response.response || !response.response.matchedDatapoints) {
        return false;
      }

      const matchedDatapoints = response.response.matchedDatapoints || [];

      // Check if our target datapoint was matched
      const targetMatched = matchedDatapoints.some(
        (match) => match.matchedId === chunkData.datapointId
      );

      return targetMatched;
    } catch (error) {
      this.scriptLogger.error(
        `Error in validation for chunk ${chunkData.chunkId}:`,
        error
      );
      return false;
    }
  }

  private async analyzeMissReasons(
    chunkData: MissedChunkData,
    datapoints: any[],
    disclosureRequirement: any,
    llmRateLimiterService: LlmRateLimiterService,
    promptService: PromptService
  ): Promise<any> {
    try {
      const originalPrompt =
        promptService.generateDatapointClassificationReasoningPrompt({
          chunkContent: chunkData.chunkContent,
          esrsDatapoints: datapoints,
          disclosureRequirement: disclosureRequirement,
        });

      const analysisPrompt: ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: `You are an AI analysis expert tasked with understanding why an AI system failed to correctly link a document chunk to a specific ESRS datapoint.

CONTEXT:
The AI was supposed to link the following chunk to datapoint "${chunkData.datapointId}" (${chunkData.datapointName}) but failed to do so even after 3 attempts.

TARGET DATAPOINT:
- ID: ${chunkData.datapointId}
- Name: ${chunkData.datapointName}
- Data Type: ${chunkData.dataType}
- Law Text: ${chunkData.lawText}
- Law Text AR: ${chunkData.lawTextAR}

DOCUMENT CHUNK:
${chunkData.chunkContent}

ORIGINAL CLASSIFICATION PROMPT USED:
${originalPrompt}

TASK:
Analyze why the AI failed to make this connection and provide insights.

REQUIRED OUTPUT FORMAT (JSON only):
{
  "reasons": [
    "List of specific reasons why the AI might have missed this connection"
  ],
  "complexity_score": 1-10,
  "content_clarity": "clear|ambiguous|complex|unclear",
  "prompt_adequacy": "adequate|insufficient|overly_complex|needs_improvement",
  "recommendations": [
    "List of specific recommendations to improve AI performance for similar cases"
  ],
  "chunk_datapoint_relationship": "direct|indirect|implicit|requires_inference|weak_connection",
  "missing_context": "What additional context might be needed",
  "language_issues": "Any language or terminology barriers identified"
}`,
        },
      ];

      const response = await llmRateLimiterService.handleRequest({
        model: LLM_MODELS['o4-mini'],
        messages: analysisPrompt,
        json: true,
        temperature: 0.3,
        taskType: 'scripted_miss_analysis',
        taskRelatedEntityId: chunkData.chunkId,
        workspaceId: chunkData.workspaceId,
        userId: GLOBAL_AI_USER_UUID,
      });

      if (response.status === 400 || !response.response) {
        this.scriptLogger.warn(
          `Miss analysis failed for chunk ${chunkData.chunkId}`
        );
        return {
          reasons: ['Analysis failed due to API error'],
          complexity_score: 5,
          content_clarity: 'unknown',
          prompt_adequacy: 'unknown',
          recommendations: ['Retry analysis'],
        };
      }

      return response.response;
    } catch (error) {
      this.scriptLogger.error(
        `Error in miss analysis for chunk ${chunkData.chunkId}:`,
        error
      );
      return {
        reasons: [`Analysis error: ${error.message}`],
        complexity_score: 5,
        content_clarity: 'unknown',
        prompt_adequacy: 'unknown',
        recommendations: ['Fix analysis error'],
      };
    }
  }

  private async writeOutput(data: AnalysisOutput): Promise<void> {
    try {
      await fs.promises.writeFile(
        this.outputFile,
        JSON.stringify(data, null, 2),
        'utf8'
      );
    } catch (error) {
      this.scriptLogger.error(`Error writing to output file: ${error}`);
    }
  }
}

// Script runner
if (require.main === module) {
  const script = new AnalyzeAIMissesScript();
  script.bootstrap(process.argv.slice(2)).catch(console.error);
}
