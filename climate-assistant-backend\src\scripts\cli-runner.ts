import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { Logger } from '@nestjs/common';

/**
 * CLI Runner - Base class for command-line scripts
 * This bootstraps the NestJS application context so you can access all services
 */
export abstract class CliRunner {
  private logger = new Logger('CliRunner');

  abstract run(...args: string[]): Promise<void>;

  async bootstrap(args: string[] = []): Promise<void> {
    try {
      this.logger.log('Bootstrapping NestJS application...');

      // Create NestJS application context (without HTTP server)
      const app = await NestFactory.createApplicationContext(AppModule);

      // Run the script
      await this.run(...args);

      // Close the application
      await app.close();
      this.logger.log('<PERSON><PERSON><PERSON> completed successfully');
      process.exit(0);
    } catch (error) {
      this.logger.error('<PERSON><PERSON><PERSON> failed:', error);
      process.exit(1);
    }
  }
}
