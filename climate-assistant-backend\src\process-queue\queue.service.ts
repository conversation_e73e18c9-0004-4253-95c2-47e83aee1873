import { Injectable } from '@nestjs/common';
import { Processor, Process, InjectQueue } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { JobProcessor, JobQueue } from 'src/types/jobs';
import { DocumentService } from 'src/document/document.service';
import { DatapointDocumentChunkService } from 'src/datapoint-document-chunk/datapoint-document-chunk.service';
import { UsersService } from 'src/users/users.service';
import { DocumentStatus } from 'src/document/entities/document.entity';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { DataRequestService } from 'src/data-request/data-request.service';
import { BULK_DATAPOINT_OPERATIONS } from 'src/data-request/constants';
import { DEFAULT_JOB_CONFIG } from 'src/util/queue.config';
import { DatapointGenerationService } from 'src/datapoint/datapoint-generation.service';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
@Processor(JobProcessor.DatapointGeneration)
export class DatapointGenerationProcessor {
  private readonly logger = new WorkerLogger(DatapointGenerationProcessor.name);

  constructor(
    private readonly datapointRequestService: DatapointRequestService,
    private readonly datapointGenerationService: DatapointGenerationService,
    private readonly dataRequestService: DataRequestService
    // Add other necessary services for datapoint generation
  ) {}

  @Process({ name: JobQueue.DatapointGenerate, concurrency: 3 }) // Reduced for 3 PM2 workers (3×3=9 total)
  async generateDatapoint(job: Job) {
    this.logger.log(
      `Processing datapoint generation: ${job.data.datapointRequestId}`
    );
    const payload = job?.data;
    try {
      await this.datapointGenerationService.generateDatapointContentWithAI({
        datapointRequestId: payload.datapointRequestId,
        userId: payload.userId,
        workspaceId: payload.workspaceId,
        useExistingReportTextForReference:
          payload.useExistingReportTextForReference,
      });
      await this.dataRequestService.emitSseEvents({
        dataRequestId: payload.dataRequestId,
        datapointRequestId: payload.datapointRequestId,
        status: 'success',
        operation: BULK_DATAPOINT_OPERATIONS.GENERATE,
      });
    } catch (error) {
      this.logger.error(
        `Error generating datapoint: ${job.data.datapointId}`,
        error
      );
      await this.dataRequestService.emitSseEvents({
        dataRequestId: payload.dataRequestId,
        datapointRequestId: payload.datapointRequestId,
        status: 'failed',
        operation: BULK_DATAPOINT_OPERATIONS.GENERATE,
      });
    }
  }
}

@Injectable()
@Processor(JobProcessor.DatapointReview)
export class DatapointReviewProcessor {
  private readonly logger = new WorkerLogger(DatapointReviewProcessor.name);

  constructor(
    private readonly datapointRequestService: DatapointRequestService,
    private readonly datapointGenerationService: DatapointGenerationService,
    private readonly dataRequestService: DataRequestService

    // Add other necessary services for datapoint generation
  ) {}

  @Process({ name: JobQueue.DatapointReview, concurrency: 3 }) // Reduced for 3 PM2 workers (3×3=9 total)
  async reviewDatapoint(job: Job) {
    this.logger.log(
      `Processing datapoint reviewing: ${job.data.datapointRequestId}`
    );
    const payload = job?.data;
    try {
      await this.datapointGenerationService.reviewDatapointContentWithAI({
        datapointRequestId: payload.datapointRequestId,
        userId: payload.userId,
        workspaceId: payload.workspaceId,
      });
      await this.dataRequestService.emitSseEvents({
        dataRequestId: payload.dataRequestId,
        datapointRequestId: payload.datapointRequestId,
        status: 'success',
        operation: BULK_DATAPOINT_OPERATIONS.REVIEW,
      });
    } catch (error) {
      this.logger.error(
        `Error reviewing datapoint: ${payload.datapointRequestId}: ${error.message}`
      );
      await this.dataRequestService.emitSseEvents({
        dataRequestId: payload.dataRequestId,
        datapointRequestId: payload.datapointRequestId,
        status: 'failed',
        operation: BULK_DATAPOINT_OPERATIONS.REVIEW,
      });
    }
  }
}

@Injectable()
@Processor(JobProcessor.ChunkExtraction)
export class ChunkExtractionProcessor {
  private readonly logger = new WorkerLogger(ChunkExtractionProcessor.name);

  constructor(
    private readonly documentService: DocumentService,
    @InjectQueue(JobProcessor.ChunkDpLinking)
    private readonly chunkLinkingQueue: Queue
  ) {}

  @Process({ name: JobQueue.ChunkExtract, concurrency: 2 }) // Reduced for 3 PM2 workers (2×3=6 total)
  async handleChunkExtraction(job: Job) {
    const { documentId, userId } = job.data;
    this.logger.log(`Bull Processing extraction for document: ${documentId}`);

    try {
      // call the extraction logic from DocumentService
      await this.documentService.extractDocumentChunks(documentId, userId);

      // add next step to the queue (linking) once extraction is done
      await this.chunkLinkingQueue.add(
        JobQueue.ChunkDpLink,
        {
          documentId,
        },
        {
          ...DEFAULT_JOB_CONFIG,
          jobId: `chunkLinking-${documentId}`,
        }
      );

      // update the document status
      await this.documentService.updateDocumentStatus(documentId, {
        status: DocumentStatus.QueuedForLinking,
      });
    } catch (error) {
      this.logger.error(`Error processing document: ${documentId}`, error);
      await this.documentService.updateDocumentStatus(documentId, {
        status: DocumentStatus.FailedExtraction,
      });
      throw error;
    }
  }
}

@Injectable()
@Processor(JobProcessor.ChunkDpLinking)
export class ChunkLinkingProcessor {
  private readonly logger = new WorkerLogger(ChunkLinkingProcessor.name);

  constructor(
    private readonly documentService: DocumentService,
    private readonly datapointDocumentChunkService: DatapointDocumentChunkService,
    private readonly userService: UsersService
  ) {}

  @Process({ name: JobQueue.ChunkDpLink, concurrency: 2 })
  async handleChunkLinking(job: Job) {
    const { documentId } = job.data;
    this.logger.log(`Bull Processing linking for document: ${documentId}`);

    try {
      const globalAIUser = await this.userService.findGlobalGlacierAIUser();
      await this.datapointDocumentChunkService.linkDocumentChunksToDatapoints(
        documentId,
        globalAIUser.id
      );
    } catch (error) {
      this.logger.error(`Error linking document: ${documentId}`, error);
      await this.documentService.updateDocumentStatus(documentId, {
        status: DocumentStatus.FailedLinking,
      });
      throw error;
    }
  }
}
