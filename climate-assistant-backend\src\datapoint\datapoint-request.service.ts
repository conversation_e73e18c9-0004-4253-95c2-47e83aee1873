import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository, IsNull } from 'typeorm';
import {
  DatapointRequestData,
  DatapointRequestWithDocumentCount,
} from './entities/datapoint-request.dto';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { UsersService } from 'src/users/users.service';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { USER_ROLES } from 'src/constants';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class DatapointRequestService {
  constructor(
    @InjectRepository(DatapointRequest)
    private readonly datapointRequestRepository: Repository<DatapointRequest>,
    @InjectRepository(DatapointDocumentChunk)
    private readonly datapointDocumentChunkMapRepository: Repository<DatapointDocumentChunk>,
    private readonly userService: UsersService,
    private readonly workspaceService: WorkspaceService
  ) {}

  private readonly logger = new WorkerLogger(DatapointRequestService.name);

  async findById(datapointRequestId: string) {
    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations: {
        esrsDatapoint: true,
        responsiblePerson: true,
      },
    });
    if (!datapointRequest) {
      throw new NotFoundException(
        `Datapoint with ID ${datapointRequest} not found`
      );
    }

    return datapointRequest;
  }

  async findDatapointWithGenerationsById(datapointRequestId: string) {
    const relations = {
      esrsDatapoint: true,
      datapointGenerations: true,
      commentGenerations: true,
      comments: {
        user: true,
      },
      datapointDocumentChunkMap: {
        documentChunk: {
          document: true,
        },
      },
    };
    return await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations,
      order: {
        esrsDatapointId: 'ASC',
        comments: {
          createdAt: 'ASC',
        },
      },
    });
  }

  async findAllDataPointRequests(
    dataRequestId: string,
    userId: string
  ): Promise<DatapointRequestWithDocumentCount[]> {
    const relations = {
      esrsDatapoint: true,
      responsiblePerson: true,
      comments: {
        user: true,
      },
    };
    const isSuperAdmin = await this.userService.userHasRequiredRole(userId, [
      USER_ROLES.SuperAdmin,
    ]);
    const isAiContributor = await this.userService.userHasRequiredRole(userId, [
      USER_ROLES.AiContributor,
    ]);
    if (isSuperAdmin) {
      relations['datapointGenerations'] = {
        evaluator: true,
      };
    }
    if (isSuperAdmin || isAiContributor) {
      relations['commentGenerations'] = true;
    }
    const datapoints = await this.datapointRequestRepository.find({
      where: { dataRequestId },
      relations,
      order: {
        esrsDatapointId: 'ASC',
        comments: {
          createdAt: 'ASC',
        },
      },
    });

    const datapointsWithDocumentCount: DatapointRequestWithDocumentCount[] = [];
    for (const datapoint of datapoints) {
      try {
        const count = await this.datapointDocumentChunkMapRepository.count({
          where: {
            datapointRequestId: datapoint.id,
            active: true,
            documentChunk: {
              document: {
                id: Not(IsNull()),
              },
            },
          },
          relations: ['documentChunk.document'],
        });

        datapointsWithDocumentCount.push({
          ...datapoint,
          documentChunkCount: count,
        });
      } catch (e) {
        this.logger.error('Error while counting document chunks', e);
      }
    }
    return datapointsWithDocumentCount;
  }

  async findData(datapointRequestId: string): Promise<DatapointRequestData> {
    const datapointRequest = await this.datapointRequestRepository.findOne({
      where: { id: datapointRequestId },
      relations: [
        'esrsDatapoint',
        'dataRequest',
        'comments',
        'datapointDocumentChunkMap.documentChunk.document',
      ],
    });

    if (!datapointRequest) {
      throw new NotFoundException(
        `Datapoint with ID ${datapointRequest} not found`
      );
    }

    return datapointRequest;
  }

  // async findDatapointWithRelations(
  //   where,
  //   relations: string[]
  // ): Promise<DatapointRequestData> {
  //   const datapointRequest = await this.datapointRequestRepository.findOne({
  //     where,
  //     relations: relations,
  //   });

  //   if (!datapointRequest) {
  //     throw new NotFoundException(`Datapoint with ID ${datpointId} not found`);
  //   }

  //   return datapointRequest;
  // }

  async update({
    datapointRequestId,
    updateDatapointRequestPayload,
    userId,
    workspaceId,
    event = 'datapoint_request_updated',
    skipHistory = false,
  }: {
    datapointRequestId: string;
    updateDatapointRequestPayload: Partial<DatapointRequest>;
    userId?: string;
    workspaceId?: string;
    event?: string;
    skipHistory?: boolean;
  }): Promise<DatapointRequest> {
    const datapointRequest = await this.findData(datapointRequestId);
    if (!datapointRequest) {
      throw new NotFoundException(`Datapoint Request not found`);
    }

    const definedPayload = Object.fromEntries(
      Object.entries(updateDatapointRequestPayload).filter(
        ([, value]) => value !== undefined
      )
    );

    await this.datapointRequestRepository.update(
      datapointRequestId,
      definedPayload
    );

    const datapoint = await this.findById(datapointRequestId);

    if (userId && workspaceId && !skipHistory) {
      const versionHistoryId = await this.workspaceService.storeActionHistory({
        event: event,
        ref: datapointRequestId,
        workspaceId: workspaceId,
        versionData: {
          event: event,
          doneBy: userId,
          data: datapoint,
        },
      });

      await this.datapointRequestRepository.update(datapointRequestId, {
        content_version: versionHistoryId,
      });
    }
    return datapoint;
  }

  async updateContentWithMDRText(
    datapointRequestId: string,
    mdrText: string
  ): Promise<void> {
    const datapointRequest = await this.findById(datapointRequestId);
    if (!datapointRequest) {
      throw new NotFoundException(`Datapoint Request not found`);
    }

    datapointRequest.content += `\n\n${mdrText}`;
    await this.datapointRequestRepository.save(datapointRequest);
  }

  async findAllDatapointsForWorkspace(workspaceId: string) {
    // Find all datapoint requests for this workspace through joins
    const datapointRequests = await this.datapointRequestRepository
      .createQueryBuilder('datapointRequest')
      .innerJoinAndSelect('datapointRequest.esrsDatapoint', 'esrsDatapoint')
      .innerJoin('datapointRequest.dataRequest', 'dataRequest')
      .innerJoin('dataRequest.project', 'project')
      .select([
        'datapointRequest.id',
        'datapointRequest.status',
        'esrsDatapoint.datapointId',
        'esrsDatapoint.name',
      ])
      .where('project.workspaceId = :workspaceId', { workspaceId })
      .orderBy('datapointRequest.esrsDatapointId', 'ASC')
      .getMany();

    return datapointRequests;
  }
}
