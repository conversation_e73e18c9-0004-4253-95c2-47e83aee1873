import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';

import {
  DatapointRequestData,
  DataRequestData,
  GenerateDataRequestReportTextTextPayload,
  UpdateDataRequestPayload,
} from './entities/data-request.dto';

import { DataRequest, DataRequestStatus } from './entities/data-request.entity';
import { DatapointRequestStatus } from 'src/datapoint/entities/datapoint-request.entity';
import { PromptService } from 'src/prompts/prompts.service';
import { ChatCompletionMessageParam } from 'openai/resources';
import { ProjectService } from 'src/project/project.service';
import { Comment, CommentType } from 'src/project/entities/comment.entity';
import { trimHtmlPreAndPostfix } from 'src/util/llm-response-util';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { DatapointRequestService } from 'src/datapoint/datapoint-request.service';
import { USER_ROLES } from 'src/constants';
import { LLM_MODELS } from 'src/constants';
import { Observable } from 'rxjs';

import { DatapointGenerationEvent } from './constants';
import {
  DataRequestGeneration,
  dataRequestGenerationStatus,
} from './entities/datarequest-generation.entity';
import { DatapointDataRequestSharedService } from 'src/shared/shared-datapoint-datarequest.service';
import { LlmRateLimiterService } from 'src/llm/services/llm-rate-limiter.service';
import { DatapointRequestWithDocumentCount } from 'src/datapoint/entities/datapoint-request.dto';
import { RedisEventService } from 'src/events/redis-event.service';
import { DatapointStatusService } from 'src/datapoint/datapoint-status.service';
import { WorkerLogger } from 'src/shared/logger.service';
import { ESRSDatapoint } from 'src/datapoint/entities/esrs-datapoint.entity';

@Injectable()
export class DataRequestService {
  private readonly CHANNEL = 'datapoint-generation-events';

  constructor(
    @InjectRepository(DataRequest)
    private readonly dataRequestRepository: Repository<DataRequest>,
    @InjectRepository(ESRSDatapoint)
    private readonly esrsDatapointRepository: Repository<ESRSDatapoint>,
    private readonly promptService: PromptService,
    private readonly projectService: ProjectService,
    private readonly userService: UsersService,
    private readonly workspaceService: WorkspaceService,
    private readonly datapointRequestService: DatapointRequestService,
    private readonly datapointStatusService: DatapointStatusService,
    private readonly llmRateLimitService: LlmRateLimiterService,
    private readonly datapointDataRequestSharedService: DatapointDataRequestSharedService,
    @InjectRepository(DataRequestGeneration)
    private readonly dataRequestGenerationRepository: Repository<DataRequestGeneration>,
    private readonly redisEventService: RedisEventService
  ) {
    // Subscribe to Redis channel for cross-worker event propagation
    this.events$ = this.redisEventService
      .subscribe<DatapointGenerationEvent>(this.CHANNEL)
      .asObservable();
  }

  public readonly events$: Observable<DatapointGenerationEvent>;

  private readonly logger = new WorkerLogger(DataRequestService.name);

  /**
   * Processes citations in the content and replaces them with indexed numbers
   * @param content The content containing citations in format ["E1-1_04"] or ["E1-1_04", "E1-1_05"]
   * @returns Object containing processed content and citation data
   */
  private async processCitations(content: string): Promise<{
    processedContent: string;
    citations: { datapointId: string; name: string; paragraph: string }[];
  }> {
    // Regex to find citations in format ["E1-1_04"] or ["E1-1_04", "E1-1_05"]
    // This captures the entire array content between square brackets
    const citationArrayRegex = /\[([^\]]+)\]/g;
    const citationMatches: string[] = [];
    const citationMap = new Map<string, number>();
    let match;

    // Extract all citations from arrays
    while ((match = citationArrayRegex.exec(content)) !== null) {
      const arrayContent = match[1];

      // Extract individual citation IDs from the array content
      // Handle both quoted and unquoted formats: "E1-1_04" or E1-1_04
      const individualCitationRegex = /"([^"]+)"|([^",\s]+)/g;
      let citationMatch;

      while (
        (citationMatch = individualCitationRegex.exec(arrayContent)) !== null
      ) {
        // Use either quoted or unquoted capture group
        const citationId = citationMatch[1] || citationMatch[2];
        if (citationId && !citationMap.has(citationId)) {
          citationMap.set(citationId, citationMap.size + 1);
          citationMatches.push(citationId);
        }
      }
    }

    // Get datapoint details from database
    const citationData: {
      datapointId: string;
      name: string;
      paragraph: string;
    }[] = [];

    for (const datapointId of citationMatches) {
      try {
        const datapoint = await this.esrsDatapointRepository.findOne({
          where: { datapointId },
        });

        if (datapoint) {
          citationData.push({
            datapointId: datapoint.datapointId,
            name: datapoint.name,
            paragraph: datapoint.paragraph || 'N/A',
          });
        } else {
          // Fallback if datapoint not found
          citationData.push({
            datapointId,
            name: 'Unknown datapoint',
            paragraph: 'N/A',
          });
        }
      } catch (error) {
        this.logger.error(`Error fetching datapoint ${datapointId}:`, error);
        // Fallback
        citationData.push({
          datapointId,
          name: 'Unknown datapoint',
          paragraph: 'N/A',
        });
      }
    }

    // Replace citation arrays with indexed numbers
    // Handle citation arrays like ["E1-1_04"] or ["E1-1_04", "E1-1_05"]
    // Replace with corresponding superscript numbers like <sub>[1]</sub> or <sub>[1]</sub><sub>[2]</sub>
    let processedContent = content;

    // Find all citation arrays in the content for replacement
    const replacementArrayRegex = /\[([^\]]+)\]/g;
    processedContent = processedContent.replace(
      replacementArrayRegex,
      (fullMatch, arrayContent) => {
        // Extract individual citations from this array
        const individualCitationRegex = /"([^"]+)"|([^",\s]+)/g;
        const citationsInArray: string[] = [];
        let citationMatch;

        while (
          (citationMatch = individualCitationRegex.exec(arrayContent)) !== null
        ) {
          const citationId = citationMatch[1] || citationMatch[2];
          if (citationId) {
            citationsInArray.push(citationId);
          }
        }

        // Convert each citation to its corresponding superscript number
        const superscriptNumbers = citationsInArray
          .map((citationId) => {
            const citationNumber = citationMap.get(citationId);
            return citationNumber ? `<sub>[${citationNumber}]</sub>` : '';
          })
          .filter(Boolean) // Remove empty strings
          .join('');

        return superscriptNumbers;
      }
    );

    // Handle special case: citations that appear after </p> tags
    // Move superscript numbers inside the paragraph before the closing tag
    // Pattern: </p><sub>[1]</sub><sub>[2]</sub> -> <sub>[1]</sub><sub>[2]</sub></p>
    processedContent = processedContent.replace(
      /(<\/p>)(\s*)(<sub>\[[^\]]+\]<\/sub>(?:<sub>\[[^\]]+\]<\/sub>)*)/g,
      '$3$1'
    );

    return {
      processedContent,
      citations: citationData,
    };
  }

  /**
   * Generates footnotes section for citations
   * @param citations Array of citation data
   * @returns Formatted footnotes string in HTML format
   */
  private generateFootnotes(
    citations: { datapointId: string; name: string; paragraph: string }[]
  ): string {
    if (citations.length === 0) {
      return '';
    }

    let footnotes = '<hr>';
    citations.forEach((citation, index) => {
      const citationNumber = index + 1;
      footnotes += `<p>[${citationNumber}] <strong>${citation.datapointId}</strong> / ${citation.paragraph} - ${citation.name}</p>`;
    });

    return footnotes;
  }

  async emitSseEvents(event: DatapointGenerationEvent) {
    const eventPayload = {
      ...event,
      timestamp: new Date(),
    };

    // Publish to Redis for cross-worker event propagation
    await this.redisEventService.publish(this.CHANNEL, eventPayload);

    this.logger.debug(
      `Worker ${process.env.INSTANCE_ID || process.pid} published event for datapoint ${event.datapointRequestId}`
    );
  }

  closeSseEvents() {
    // With Redis pub/sub, we don't need to complete the subject
    // as each worker manages its own subscription
    this.logger.log('SSE events closed');
  }

  async findAll(projectId: string): Promise<DataRequest[]> {
    return this.dataRequestRepository.find({
      where: { projectId },
    });
  }

  async findProject(dataRequestId: string): Promise<DataRequest> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId },
      relations: {
        project: true,
        disclosureRequirement: true,
      },
    });
    return dataRequest;
  }

  async findById(dataRequestId: string): Promise<DataRequest> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId },
    });
    return dataRequest;
  }

  async findRelatedData(
    dataRequestId: string,
    userId?: string
  ): Promise<DataRequestData> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId },
      relations: {
        responsiblePerson: true,
        approver: true,
        disclosureRequirement: true,
        dataRequestGenerations: true,
        comments: {
          user: true,
        },
        commentGenerations: true,
      },
      order: {
        comments: {
          createdAt: 'ASC',
        },
      },
    });
    if (!dataRequest) {
      throw new NotFoundException(
        `DataRequest with ID ${dataRequest} not found`
      );
    }

    const datapointRequests: DatapointRequestWithDocumentCount[] =
      await this.datapointRequestService.findAllDataPointRequests(
        dataRequest.id,
        userId
      );

    dataRequest.datapointRequests = datapointRequests;

    return dataRequest;
  }

  async update({
    dataRequestId,
    updateDataRequestPayload,
    userId,
    workspaceId,
    event = 'data_request_updated',
  }: {
    dataRequestId: string;
    updateDataRequestPayload: UpdateDataRequestPayload;
    userId?: string;
    workspaceId?: string;
    event?: string;
  }): Promise<DataRequest> {
    const dataRequest = await this.findById(dataRequestId);
    if (!dataRequest) {
      throw new NotFoundException(`Data Request not found`);
    }

    const definedPayload = Object.fromEntries(
      Object.entries(updateDataRequestPayload).filter(
        ([, value]) => value !== undefined
      )
    );

    await this.dataRequestRepository.update(dataRequestId, definedPayload);

    const updated = await this.findById(dataRequestId);

    if (userId && workspaceId) {
      const versionId = await this.workspaceService.storeActionHistory({
        event: event,
        ref: dataRequestId,
        workspaceId: workspaceId,
        versionData: {
          event: event,
          doneBy: userId,
          data: updated,
        },
      });
      await this.dataRequestRepository.update(dataRequestId, {
        content_version: versionId,
      });
    }

    return updated;
  }

  async reviewDataRequestContentWithAI({
    dataRequestId,
    userId,
    workspaceId,
  }: {
    dataRequestId: string;
    userId: string;
    workspaceId: string;
  }): Promise<Comment[]> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId, status: Not(DataRequestStatus.NotReported) },
      relations: [
        'project',
        'comments',
        'disclosureRequirement',
        'datapointRequests.esrsDatapoint',
      ],
    });

    const isAiEvaluator = await this.userService.userHasRequiredRole(userId, [
      USER_ROLES.SuperAdmin,
    ]);

    // Build prompt
    this.logger.log(`Start Gap Analysis for dataRequest ${dataRequest.id}`);
    const dataRequestGapAnalysisChatCompletion: ChatCompletionMessageParam[] = [
      {
        role: 'system',
        content: this.promptService.generateDataRequestGapAnalysisSystemPrompt({
          esrsDisclosureRequirement: dataRequest.disclosureRequirement,
          generationLanguage: dataRequest.project.primaryContentLanguage,
        }),
      },
      {
        role: 'system',
        content:
          this.promptService.generateDataRequestFullLawTextContextForReportedDatapoints(
            dataRequest
          ),
      },
      {
        role: 'user',
        content:
          this.promptService.generateDataRequestGapAnalysisContentContext(
            dataRequest
          ),
      },
    ];

    const reviewedGapAnalysisCompletionResponse =
      await this.llmRateLimitService.handleRequest({
        model: LLM_MODELS['o4-mini'],
        messages: dataRequestGapAnalysisChatCompletion,
        json: true,
        temperature: 0.3,
        userId,
        workspaceId,
        taskType: 'reviewDataRequestContentWithAI',
        taskRelatedEntityId: dataRequestId,
      });

    if (reviewedGapAnalysisCompletionResponse.status === 400) {
      throw new Error(reviewedGapAnalysisCompletionResponse.response);
    }

    const reviewedGapAnalysis: {
      gapIdentified: boolean;
      datapointGaps: {
        datapoint: string;
        gap: string;
        actions: string[];
        exampleText: string;
      }[];
    } = reviewedGapAnalysisCompletionResponse.response;

    // if (reviewedGapAnalysis) {
    //   reviewedGapAnalysis = trimHtmlPreAndPostfix(reviewedGapAnalysis);
    // }

    this.logger.log(
      `Gap Analysis Tokens: ${JSON.stringify(reviewedGapAnalysisCompletionResponse.token)}`
    );

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    const comments: Comment[] = [];

    if (reviewedGapAnalysis.gapIdentified) {
      for (const gapJson of reviewedGapAnalysis.datapointGaps) {
        // prettier-ignore
        const commentHtml =
          "<h2>" + gapJson.datapoint + "</h2>" +
          "<p><strong>Gap:</strong> " + gapJson.gap + "</p>" +
          "<p><strong>Recommended Actions:</strong></p>" +
          "<ul>" +
          gapJson.actions.map(function (action) {
            return "<li>" + action + "</li>";
          }).join('') +
          "</ul>" +
          "<p><strong>Example Text:</strong>" + gapJson.exampleText + "</p>";

        const comment = await this.projectService.addComment({
          commentableId: dataRequest.id,
          commentableType: CommentType.DataRequest,
          userId: globalAIUser.id,
          comment: commentHtml,
          workspaceId,
          evaluationLot: isAiEvaluator,
        });

        comments.push(comment);
      }
    }

    return comments;
  }

  async validateDataRequestGenerationRightsOrFail({
    dataRequest,
    userId,
  }: {
    dataRequest: DataRequest;
    userId: string;
  }): Promise<boolean> {
    if (!dataRequest.disclosureRequirement.publicAccess) {
      await this.userService.userHasRequiredRoleOrFail({
        userOrId: userId,
        requiredRoles: [USER_ROLES.SuperAdmin, USER_ROLES.AiContributor],
        message: 'Data request generation is not enabled for this request.',
      });
    }

    return true;
  }

  async generateDataRequestTextContentWithAI({
    dataRequestId,
    userId,
    workspaceId,
    additionalData,
  }: {
    dataRequestId: string;
    userId: string;
    workspaceId: string;
    additionalData: GenerateDataRequestReportTextTextPayload;
  }): Promise<{ content: string; id: string }> {
    const dataRequest = await this.dataRequestRepository.findOne({
      where: { id: dataRequestId },
      relations: [
        'project',
        'disclosureRequirement.topicRelations.topic.disclosureRequirementRelations.disclosureRequirement',
        'datapointRequests.esrsDatapoint',
      ],
    });

    const cleanCustomUserRemark =
      additionalData?.additionalReportTextGenerationRules.trim() !== ''
        ? additionalData.additionalReportTextGenerationRules
        : null;

    if (cleanCustomUserRemark) {
      await this.dataRequestRepository.update(
        { id: dataRequestId },
        {
          customUserRemark: cleanCustomUserRemark,
        }
      );
    }

    this.logger.log(
      `Start Generating dataRequest ${dataRequest.id} - ${dataRequest.disclosureRequirement.dr} with AI`
    );

    // Generate Prompt based on Requirements
    // @S: This can become extremely long. If quality isn't good, consider splitting to 2-3 sub-DRs?
    // @S: Add a "general information about company style" to the prompt
    //- shorter system prompt
    //- law texts (exploit 'primacy bias')
    //- datapoint contents
    //- example (exploit 'recency bias')
    //- detailed instructions system prompt

    const existingReportText = additionalData.useExistingReportText
      ? `In the output field, there is already an existing report text snippet. You are asked to regenerate and improve upon it. There may be a comment from the user what has to change. Old text: ${dataRequest.content}`
      : ' ';

    // Get additional data needed for the prompt
    const { generalCompanyProfile } =
      await this.workspaceService.getCompanyDetailFromWorkspaceId(workspaceId);

    const dataRequestGenerationChatCompletion: ChatCompletionMessageParam[] = [
      {
        role: 'system',
        content:
          this.promptService.generateDataRequestContentGenerationSystemPrompt({
            dataRequest: dataRequest,
            customUserRemark: cleanCustomUserRemark,
            existingReportText: existingReportText,
            generalCompanyProfile: generalCompanyProfile || '',
          }),
      },
    ];

    if (additionalData.useExistingReportText) {
      dataRequestGenerationChatCompletion.push({
        role: 'user',
        content: `Following is the existing report text that was previously generated: ${dataRequest.content}`,
      });
    }
    const dataRequestGenerationChatCompletionResponse =
      await this.llmRateLimitService.handleRequest({
        model: LLM_MODELS['o3'],
        messages: dataRequestGenerationChatCompletion,
        json: true,
        temperature: 0, //in reasoning models not an option anyways
        userId,
        workspaceId,
        taskType: 'generateDataRequestTextContentWithAI',
        taskRelatedEntityId: dataRequestId,
      });

    if (dataRequestGenerationChatCompletionResponse.status === 400) {
      throw new Error(dataRequestGenerationChatCompletionResponse.response);
    }

    const isAIEvaluator = await this.userService.userHasRequiredRole(userId, [
      USER_ROLES.SuperAdmin,
    ]);

    let generatedContent =
      dataRequestGenerationChatCompletionResponse.response['dr'];
    if (!generatedContent) {
      throw new Error(
        'No "dr" field found in the AI response. Response structure may have changed.'
      );
    }

    generatedContent = trimHtmlPreAndPostfix(generatedContent);

    // Process citations and add footnotes
    const { processedContent, citations } =
      await this.processCitations(generatedContent);
    const finalContent = processedContent + this.generateFootnotes(citations);

    const dataGenerationResponse = {
      id: dataRequest.id,
      content: finalContent,
    };

    if (isAIEvaluator) {
      const dataGeneration = await this.dataRequestGenerationRepository.create({
        data: { content: finalContent },
        dataRequest,
        evaluatorId: userId,
      });
      await this.dataRequestGenerationRepository.save(dataGeneration);
      dataGenerationResponse.id = dataGeneration.id;
    } else {
      dataRequest.content = finalContent;
      await this.dataRequestRepository.save(dataRequest);
    }

    this.logger.log(
      `Finished Generating dataRequest ${dataRequest.id} - ${dataRequest.disclosureRequirement.dr} with AI`
    );

    const globalAIUser: User = await this.userService.findGlobalGlacierAIUser();

    await this.workspaceService.storeActionHistory({
      event: 'data_request_ai_generated',
      ref: dataRequestId,
      workspaceId: workspaceId,
      versionData: {
        event: 'data_request_ai_generated',
        doneBy: globalAIUser.id,
        issuedBy: userId,
        data: dataRequest,
      },
    });

    return dataGenerationResponse;
  }

  async findDatapointById(
    datapointRequestId: string
  ): Promise<DatapointRequestData> {
    return await this.datapointRequestService.findDatapointWithGenerationsById(
      datapointRequestId
    );
  }

  async generateAllDatapointForDataRequest({
    dataRequestId,
    userId,
    workspaceId,
  }: {
    dataRequestId: string;
    userId: string;
    workspaceId: string;
  }): Promise<void> {
    let datapointRequestToGenerate =
      await this.datapointStatusService.findDatapointRequestsByStatus({
        dataRequestId,
        status: [DatapointRequestStatus.IncompleteData],
      });
    datapointRequestToGenerate =
      datapointRequestToGenerate.filter(
        (datapointRequest) =>
          datapointRequest.datapointDocumentChunkMap.filter((mp) => mp.active)
            .length > 0
      ) || [];

    for (const datapointRequest of datapointRequestToGenerate) {
      await this.datapointDataRequestSharedService.addDatapointToGenerationQueue(
        {
          datapointRequest,
          userId,
          workspaceId,
          useExistingReportTextForReference: false,
        }
      );
    }
  }

  async reviewAllDatapointForDataRequest({
    dataRequestId,
    userId,
    workspaceId,
  }: {
    dataRequestId: string;
    userId: string;
    workspaceId: string;
  }): Promise<void> {
    const datapointRequestToReview =
      await this.datapointStatusService.findDatapointRequestsExcludingStatus({
        dataRequestId,
        status: [DatapointRequestStatus.NotResponded],
      });

    for (const datapointRequest of datapointRequestToReview) {
      await this.datapointDataRequestSharedService.addDatapointToReviewQueue({
        datapointRequest,
        userId,
        workspaceId,
      });
    }
  }

  async setDatapointQueueStatusToNull(id: string) {
    await this.datapointStatusService.updateQueueStatus({
      datapointRequestId: id,
      queueStatus: null,
    });
  }

  async getGenerations(
    dataRequestId: string
  ): Promise<DataRequestGeneration[]> {
    return this.dataRequestGenerationRepository.find({
      where: { dataRequest: { id: dataRequestId } },
      relations: ['evaluator'],
    });
  }

  async updateGenerationStatus({
    dataRequestGenerationId,
    status,
    userId,
    workspaceId,
    evaluatorComment,
  }: {
    dataRequestGenerationId: string;
    status: dataRequestGenerationStatus;
    userId: string;
    workspaceId: string;
    evaluatorComment?: string;
  }) {
    try {
      await this.dataRequestGenerationRepository.update(
        {
          id: dataRequestGenerationId,
        },
        {
          status,
          evaluatorId: userId,
          evaluatedAt: new Date(),
          evaluatorComment: evaluatorComment || null,
        }
      );
      if (status === dataRequestGenerationStatus.Approved) {
        return await this.dataRequestGenerationRepository.findOne({
          where: { id: dataRequestGenerationId },
          relations: ['dataRequest'],
        });
      }
      return null;
    } catch (err) {
      console.log(err);
      throw new Error(`Error while updating generation status: ${err.message}`);
    }
  }
}
