import {
  Body,
  Controller,
  Get,
  Post,
  Request,
  Res,
  UseGuards,
  Query,
  Param,
  Headers,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { AuthGuard } from './auth.guard';
import { AuthService } from './auth.service';
import { JWT_COOKIE_KEY, Public } from './helpers';
import { Response } from 'express';
import { UsersService } from '../users/users.service';
import {
  LoginDto,
  LoginResponseSuccess,
  LogoutResponseSuccess,
  RegisterWithCompanyDto,
  InitiateSSOLoginDto,
  InitiateSSOResponseDto,
  CheckDomainSSOResponseDto,
  LinkAccountToSSODto,
  GetSSOProvidersResponseDto,
} from './auth.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SystemPermissions } from 'src/constants';
import { isProduction } from 'src/env-helper';
import { PermissionService } from './services/permission.service';
import { PermissionGuard } from './guard/permission.guard';
import { Permissions } from './decorators/permissions.decorator';
import { SsoService } from './sso.service';
import { ThrottlerGuard, Throttle } from '@nestjs/throttler';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private authService: AuthService,
    private readonly userService: UsersService,
    private readonly permissionService: PermissionService,
    private readonly ssoService: SsoService
  ) {}

  /**
   * Validate redirect URLs to prevent open redirect attacks
   * Allows only allowlisted internal paths and same-origin URLs
   */
  private validateRedirectUrl(url: string | undefined): string | null {
    if (!url) return null;

    // Allowlist of safe internal paths
    const allowedPaths = [
      '/login',
      '/dashboard',
      '/projects',
      '/documents',
      '/settings',
      '/profile',
    ];

    // Check if it's a relative path
    if (url.startsWith('/')) {
      // Extract just the path (ignore query parameters for validation)
      const pathOnly = url.split('?')[0];

      // Allow exact matches or subpaths of allowed paths
      const isAllowed = allowedPaths.some(
        (allowed) => pathOnly === allowed || pathOnly.startsWith(allowed + '/')
      );

      return isAllowed ? url : null;
    }

    // For absolute URLs, only allow same origin
    try {
      const urlObj = new URL(url);
      const allowedOrigins = [
        'https://app.glacier.eco',
        'https://dev.glacier.eco',
      ];

      if (process.env.NODE_ENV !== 'production') {
        allowedOrigins.push('http://localhost:5173', 'http://localhost:8080');
      }

      return allowedOrigins.includes(urlObj.origin) ? url : null;
    } catch {
      // Invalid URL format
      return null;
    }
  }

  @Public()
  @Post('login')
  @ApiOperation({ summary: 'User login' })
  @ApiResponse({ status: 200, description: 'Login successful' })
  async login(
    @Body() loginDto: LoginDto,
    @Res() res: Response
  ): Promise<Response<LoginResponseSuccess>> {
    const token = await this.authService.login(
      loginDto.email,
      loginDto.password
    );

    res.cookie(JWT_COOKIE_KEY, token, {
      httpOnly: true,
      secure: true,
      sameSite: 'none',
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
    });

    return res.send({ message: 'Login successful' });
  }

  @Public()
  @Post('register')
  @ApiOperation({ summary: 'User registration' })
  @ApiResponse({ status: 200, description: 'Registration successful' })
  async register(
    @Body() registerDto: RegisterWithCompanyDto,
    @Res() res: Response
  ): Promise<Response<LoginResponseSuccess>> {
    const token = await this.authService.registerWithCompany(registerDto);

    res.cookie(JWT_COOKIE_KEY, token, {
      httpOnly: true,
      secure: true,
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
    });

    return res.send({ message: 'Login successful' });
  }

  @Public()
  @Post('request-password-reset')
  @ApiOperation({ summary: 'Password reset request' })
  @ApiResponse({
    status: 200,
    description: 'Password reset email sent successfully',
  })
  async passwordResetEmail(
    @Res() res: Response,
    @Request() req,
    @Body() body: { email: string }
  ): Promise<Response> {
    await this.authService.sendPasswordResetEmail(
      body.email,
      req.headers.origin
    );

    return res.send({ message: 'Password reset email sent successfully' });
  }

  @Public()
  @Post('validate-password-reset-token')
  @ApiOperation({ summary: 'Password reset token validatiaon' })
  @ApiResponse({
    status: 200,
    description: 'Valid token',
  })
  async validatePasswordResetToken(
    @Request() req,
    @Body() body: { token: string }
  ) {
    const token = await this.authService.validateToken(body.token);
    return token.user;
  }

  @Public()
  @Post('password-reset-submit')
  @ApiOperation({ summary: 'Password reset submit' })
  @ApiResponse({
    status: 200,
    description: 'Password submitted successfully',
  })
  async passwordSubmit(
    @Request() req,
    @Res() res: Response,
    @Body() body: { password: string; fullName?: string; token: string }
  ) {
    const userToken = await this.authService.resetPassword(
      body.password,
      body.token,
      body.fullName
    );
    res.cookie(JWT_COOKIE_KEY, userToken, {
      httpOnly: true,
      secure: true,
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
    });

    return res.send({ message: 'Password reset successful' });
  }

  @UseGuards(AuthGuard)
  @Get('profile')
  @ApiOperation({ summary: 'Get user profile' })
  @ApiResponse({
    status: 200,
    description: 'User profile retrieved successfully',
  })
  getProfile(@Request() req) {
    return this.userService.findById(req.user.id);
  }

  @UseGuards(PermissionGuard)
  @Post('switch-workspace')
  @Permissions(SystemPermissions.SWITCH_WORKSPACE)
  async switchWorkspace(
    @Request() req,
    @Body() body: { workspaceId: string },
    @Res() res: Response
  ) {
    const userId = req.user.id;
    const { workspaceId } = body;

    const token = await this.authService.switchUserWorkspace(
      userId,
      workspaceId
    );

    res.cookie(JWT_COOKIE_KEY, token, {
      httpOnly: true,
      secure: isProduction,
      expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 1),
    });

    return res.send({ message: 'Switch successful' });
  }

  // SSO Endpoints
  @Public()
  @Post('sso/init')
  @UseGuards(ThrottlerGuard)
  @Throttle({ default: { limit: 5, ttl: 60000 } }) // Rate limiting: 5 requests per minute per IP
  @ApiOperation({ summary: 'Initiate SSO login' })
  @ApiResponse({
    status: 200,
    description: 'SSO authorization URL generated',
    type: InitiateSSOResponseDto,
  })
  @ApiResponse({ status: 400, description: 'SSO not enabled for workspace' })
  async initiateSSOLogin(
    @Body() body: InitiateSSOLoginDto
  ): Promise<InitiateSSOResponseDto> {
    const authUrl = await this.ssoService.initiateSSOLogin(
      body.workspaceId,
      body.redirectUrl
    );

    return { authorizationUrl: authUrl };
  }

  @Public()
  @Get('sso/callback')
  @ApiOperation({ summary: 'Handle SSO callback' })
  @ApiResponse({ status: 302, description: 'Redirect to application' })
  @ApiResponse({ status: 401, description: 'SSO authentication failed' })
  async handleSSOCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response
  ) {
    try {
      console.log('SSO Callback Debug:', {
        hasState: !!state,
        flowType: state ? 'SP-initiated' : 'IdP-initiated',
      });

      if (!code) {
        console.error('SSO Callback Error: code parameter is missing');
        throw new Error('Code parameter is required');
      }

      const token = await this.ssoService.handleSSOCallback(code, state);

      res.cookie(JWT_COOKIE_KEY, token, {
        httpOnly: true,
        secure: isProduction,
        sameSite: 'none',
        expires: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // 7 days
      });

      // Decode state to get redirect URL (only for SP-initiated flows)
      let redirectUrl = '/dashboard';
      if (state) {
        try {
          const decodedState = JSON.parse(
            Buffer.from(state.split('.')[1], 'base64').toString()
          );
          // Validate redirect URL even though it came from signed JWT state
          const validatedUrl =
            this.validateRedirectUrl(decodedState.redirectUrl) || '/dashboard';

          // Normalize to path only (handle both relative and absolute URLs)
          if (validatedUrl.startsWith('http')) {
            // It's an absolute URL, extract just the pathname
            try {
              const urlObj = new URL(validatedUrl);
              redirectUrl = urlObj.pathname + urlObj.search; // Include query params
            } catch {
              redirectUrl = '/dashboard'; // Fallback if URL parsing fails
            }
          } else {
            // It's already a relative path
            redirectUrl = validatedUrl;
          }
        } catch {
          // Use default if state parsing fails
        }
      } else {
        // IdP-initiated: Always redirect to dashboard
        console.log('IdP-initiated flow: using default redirect /dashboard');
      }

      const finalRedirectUrl = `${process.env.FRONTEND_URL}${redirectUrl}`;

      return res.redirect(finalRedirectUrl);
    } catch (error) {
      // Redirect to login with error
      return res.redirect(
        `${process.env.FRONTEND_URL}/sso-login?error=sso_failed`
      );
    }
  }

  @Public()
  @Get('sso/check-domain/:domain')
  @UseGuards(ThrottlerGuard)
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // Rate limiting: 10 requests per minute per IP
  @ApiOperation({ summary: 'Check if domain has SSO configured' })
  @ApiResponse({
    status: 200,
    description: 'Domain SSO status',
    type: CheckDomainSSOResponseDto,
  })
  async checkDomainSSO(
    @Param('domain') domain: string
  ): Promise<CheckDomainSSOResponseDto> {
    return await this.ssoService.checkDomainSSO(domain);
  }

  @Post('sso/link')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Link existing account to SSO' })
  @ApiResponse({ status: 200, description: 'Account linked successfully' })
  @ApiResponse({ status: 400, description: 'User not found' })
  async linkAccountToSSO(@Request() req, @Body() body: LinkAccountToSSODto) {
    return await this.ssoService.linkExistingUserToSSO(
      req.user.id,
      body.ssoUserId,
      body.ssoProviderId
    );
  }

  @Get('sso/providers/:workspaceId')
  @UseGuards(AuthGuard)
  @ApiOperation({ summary: 'Get available SSO providers for workspace' })
  @ApiResponse({
    status: 200,
    description: 'SSO providers retrieved',
    type: GetSSOProvidersResponseDto,
  })
  async getSSOProviders(
    @Param('workspaceId') workspaceId: string
  ): Promise<GetSSOProvidersResponseDto> {
    return await this.ssoService.getWorkspaceProviders(workspaceId);
  }

  @Public()
  @Get('saml/metadata')
  @ApiOperation({ summary: 'Get SAML metadata for service provider' })
  @ApiResponse({
    status: 200,
    description: 'SAML metadata XML',
    content: { 'application/xml': {} },
  })
  async getSAMLMetadata(@Res() res: Response) {
    try {
      const metadata = await this.ssoService.getSAMLMetadata();
      res.set('Content-Type', 'application/xml');
      return res.send(metadata);
    } catch (error) {
      throw new BadRequestException('Failed to generate SAML metadata');
    }
  }

  @Public()
  @Get('sso/logout')
  @ApiOperation({ summary: 'SSO logout endpoint' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  async ssoLogout(
    @Request() req,
    @Query('SAMLRequest') samlRequest?: string,
    @Query('RelayState') relayState?: string,
    @Res() res?: Response
  ) {
    // Handle both authenticated and unauthenticated logout requests
    if (req.user) {
      const user = await this.userService.findById(req.user.id);

      if (user && user.authMethod === 'sso' && user.ssoUserId) {
        // Log SSO logout event
        await this.ssoService.logSSOEvent({
          type: 'logout',
          userId: user.id,
          workspaceId: req.user.workspaceId,
          email: user.email,
          ssoProvider: user.ssoProviderId,
        });
      }

      res.clearCookie(JWT_COOKIE_KEY);
    }

    // Handle SAML SLO if SAMLRequest is present
    if (samlRequest) {
      try {
        const sloResponse = await this.ssoService.handleSAMLLogout(samlRequest);
        res.set('Content-Type', 'application/xml');
        return res.send(sloResponse);
      } catch (error) {
        this.logger.error('SAML SLO failed:', error);
        return res.status(400).send('Invalid SAML logout request');
      }
    }

    // For regular logout or after SAML processing
    if (req.headers.accept?.includes('application/json')) {
      return res.json({ message: 'Logout successful' });
    }

    // Redirect to login with logout confirmation - validate RelayState for security
    const redirectUrl =
      this.validateRedirectUrl(relayState) || '/login?message=logged_out';
    return res.redirect(redirectUrl);
  }

  @Public()
  @Post('webhooks/workos')
  @ApiOperation({ summary: 'Handle WorkOS webhooks' })
  @ApiResponse({ status: 200, description: 'Webhook processed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid webhook signature' })
  async handleWorkOSWebhook(
    @Request() req,
    @Headers('workos-signature') signature: string
  ) {
    // Use raw body for webhook signature verification
    const rawBody = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;
    return await this.ssoService.handleWebhook(rawBody, signature, clientIP);
  }

  @UseGuards(AuthGuard)
  @Post('logout')
  @ApiOperation({ summary: 'User logout' })
  @ApiResponse({ status: 200, description: 'Logout successful' })
  async logout(
    @Request() req,
    @Res() res: Response
  ): Promise<Response<LogoutResponseSuccess>> {
    // Note: SSO logout logging is handled internally by SsoService
    res.clearCookie(JWT_COOKIE_KEY);
    return res.send({ message: 'Logout successful' });
  }

  @UseGuards(AuthGuard)
  @Get('permissions')
  @ApiOperation({ summary: 'Get user permissions' })
  @ApiResponse({
    status: 200,
    description: 'User permissions retrieved successfully',
  })
  getUserPermissions(@Request() req) {
    return this.permissionService.getUserPermissions(
      req.user.id,
      req.user.workspaceId
    );
  }
}
