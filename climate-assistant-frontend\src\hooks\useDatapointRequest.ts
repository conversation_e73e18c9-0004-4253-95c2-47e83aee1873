import { useEffect, useState } from 'react';

import {
  DatapointRequestData,
  generationStatus,
  QUEUE_STATUS,
} from '@/types/project';
import { DatapointRequestStatus } from '@/types';
import {
  updateDatapointGenerationStatus,
  generateDatapointWithAi,
  reviewDatapointContentWithAI,
  updateDatapointRequestContent,
  updateDatapointRequestStatus,
  updateDatapointResponsiblePerson,
} from '@/api/datapoint/datapoint-request.api';
import { permitOverride } from '@/lib/utils';
import { toast } from '@/components/ui/use-toast';
import { GenerateDatapointWithAIFormData } from '@/components/dashboard/AiGenerateDpConfirmModal';
import { fireConfetti } from '@/lib/confetti';
import { useDataRequestContext } from '@/context/dataRequestContext';
import { usePermissions } from '@/context/permissionsContext';

export function useDatapointRequest({
  datapointRequest,
}: {
  datapointRequest: DatapointRequestData;
}) {
  const [content, setContent] = useState(datapointRequest.content);
  const [isLoadingReviewWithAi, setIsLoadingReviewWithAi] = useState(false);
  const [isLoadingGenerateWithAi, setIsLoadingGenerateWithAi] = useState(false);
  const [confirmAiDialogOpen, setConfirmAiDialogOpen] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const { userPermissions } = usePermissions();

  const { setupEventSource, closeEventSource, refetchDataRequest } =
    useDataRequestContext();

  useEffect(() => {
    setIsLoadingGenerateWithAi(
      datapointRequest.queueStatus === QUEUE_STATUS.QueuedForGeneration
    );
    setIsLoadingReviewWithAi(
      datapointRequest.queueStatus === QUEUE_STATUS.QueuedForReview
    );

    // Only update content from backend if it's different from current content
    // This prevents overwriting user edits
    if (datapointRequest.content !== content) {
      setContent(datapointRequest.content);
      setIsDirty(false);
    }
  }, [datapointRequest]);

  const handleInputChange = (newContent: string) => {
    setContent(newContent);
    if (!isDirty) setIsDirty(true);
  };

  async function handleReportDatapointStatusChange(
    status: DatapointRequestStatus
  ) {
    await updateDatapointRequestStatus({
      datapointRequestId: datapointRequest.id,
      status,
    });
    toast({
      title: `Status Updated`,
      variant: 'success',
    });
    refetchDataRequest();
  }

  async function handleSave(datapointRequestId: string) {
    try {
      console.log('Handle Save for Datapoint ' + datapointRequestId);
      const newContent: string = content;
      await updateDatapointRequestContent({
        datapointRequestId: datapointRequestId,
        content: newContent,
      });
      refetchDataRequest();
      setIsDirty(false);
      toast({
        title: `${datapointRequest.esrsDatapoint.datapointId} Saved`,
        variant: 'success',
      });
    } catch (error) {
      toast({
        title: `Error saving ${datapointRequest.esrsDatapoint.datapointId}`,
        variant: 'destructive',
      });
    }
  }

  function canReviewWithAi(): {
    allow: boolean;
    tooltip: string;
  } {
    switch (true) {
      case isLoadingReviewWithAi:
        return {
          allow: false,
          tooltip: 'Review in progress',
        };
      case permitOverride():
        return { allow: true, tooltip: 'Review datapoint text using AI' };

      case !datapointRequest.esrsDatapoint.publicAccess &&
        !userPermissions.canPerformAiGenerateOrReviewOnDp:
        return {
          allow: false,
          tooltip:
            'This gaps are only shown after quality assurance by Glacier',
        };

      case ![
        DatapointRequestStatus.IncompleteData,
        DatapointRequestStatus.CompleteData,
      ].includes(datapointRequest.status) || content.trim() === '':
        return {
          allow: false,
          tooltip:
            'The datapoint text must be completed before it can be reviewed with AI',
        };

      case isDirty:
        return {
          allow: false,
          tooltip: 'Cannot review because there are unsaved changes.',
        };

      case !userPermissions.canPerformAiGenerateOrReviewOnDp:
        return {
          allow: false,
          tooltip:
            'You do not have the right permissions to the datapoint text with AI',
        };

      default:
        return { allow: true, tooltip: 'Review datapoint text using AI' };
    }
  }

  async function handleReviewWithAi() {
    setIsLoadingReviewWithAi(true);
    try {
      const eventSource = await setupEventSource(
        datapointRequest.dataRequestId
      );
      await reviewDatapointContentWithAI({
        datapointRequestId: datapointRequest.id,
      });

      if (!userPermissions.canUpdateAiGenerationStatusOnDp) {
        const messageHandler = (event: MessageEvent) => {
          const eventData = JSON.parse(event.data);
          if (
            eventData.datapointRequestId === datapointRequest.id &&
            eventData.status === 'success' &&
            eventData.operation === 'review'
          ) {
            updateDatapointRequestStatus({
              datapointRequestId: datapointRequest.id,
              status: DatapointRequestStatus.IncompleteData,
            }).then(() => {
              refetchDataRequest();
            });

            eventSource.removeEventListener('message', messageHandler);
          }
        };

        eventSource.addEventListener('message', messageHandler);
      }

      toast({
        title: 'Datapoint queued for review',
        description: `AI Review for ${datapointRequest.esrsDatapoint.datapointId} started.`,
        variant: 'default',
      });
    } catch (e) {
      toast({
        title: `Error reviewing ${datapointRequest.esrsDatapoint.datapointId} with AI`,
        variant: 'destructive',
      });
      closeEventSource();
    }
  }

  function canGenerateWithAi(): {
    allow: boolean;
    tooltip: string;
  } {
    switch (true) {
      case isLoadingGenerateWithAi:
        return {
          allow: false,
          tooltip: 'Generation in progress',
        };

      case permitOverride():
        return { allow: true, tooltip: 'Generate datapoint text using AI' };

      case status === DatapointRequestStatus.NotReported:
        return {
          allow: false,
          tooltip: 'Cannot generate because the datapoint is not reported.',
        };

      case !datapointRequest.esrsDatapoint.publicAccess &&
        !userPermissions.canPerformAiGenerateOrReviewOnDp:
        return {
          allow: false,
          tooltip:
            'This datapoint is only shown after quality assurance by Glacier',
        };

      case datapointRequest.documentChunkCount === 0:
        return {
          allow: false,
          tooltip:
            'Cannot generate because there are no documents linked to this datapoint.',
        };

      case isDirty:
        return {
          allow: false,
          tooltip: 'Cannot generate because there are unsaved changes.',
        };

      case !userPermissions.canPerformAiGenerateOrReviewOnDp:
        return {
          allow: false,
          tooltip:
            'You do not have the right permissions to Generate datapoint text with AI',
        };

      default:
        return { allow: true, tooltip: 'Generate datapoint text using AI' };
    }
  }

  async function handleGenerateWithAi(data: GenerateDatapointWithAIFormData) {
    setIsLoadingGenerateWithAi(true);
    try {
      await setupEventSource(datapointRequest.dataRequestId);
      await generateDatapointWithAi(datapointRequest.id, data);
      toast({
        title: 'Datapoint queued for generation',
        description: `AI Generation for ${datapointRequest.esrsDatapoint.datapointId} started.`,
        variant: 'default',
      });
    } catch (e) {
      toast({
        title: `Error generating ${datapointRequest.esrsDatapoint.datapointId} with AI`,
        variant: 'destructive',
      });
      closeEventSource();
    }
  }

  async function handleUpdateDatapointGenerationStatus(
    id: string,
    updatedStatus: generationStatus,
    evaluatorComment?: string
  ): Promise<void> {
    if (userPermissions.canUpdateAiGenerationStatusOnDp) {
      try {
        await updateDatapointGenerationStatus({
          datapointRequestId: datapointRequest.id,
          datapointGenerationId: id,
          status: updatedStatus,
          evaluatorComment,
        });
        if (updatedStatus === generationStatus.approved) {
          fireConfetti();
        }
        toast({
          title: `Datapoint Generation ${updatedStatus}`,
          variant: 'success',
        });
      } catch (error) {
        toast({
          title: 'Error updating datapoint generation status',
          variant: 'destructive',
        });
        console.error('Error updating datapoint generation status:', error);
      } finally {
        refetchDataRequest();
      }
    }
  }

  async function updateResponsiblePerson(responsiblePersonId: string) {
    try {
      await updateDatapointResponsiblePerson({
        datapointRequestId: datapointRequest.id,
        responsiblePersonId,
      });
      refetchDataRequest();
    } catch (error) {
      toast({
        title: 'Error updating responsible person',
        variant: 'destructive',
      });
      console.error('Error updating responsible person:', error);
    }
  }

  return {
    content,
    status: datapointRequest.status,
    datapointGenerations: datapointRequest.datapointGenerations,
    esrsDatapoint: datapointRequest.esrsDatapoint,
    isLoadingReviewWithAi,
    isLoadingGenerateWithAi,
    handleInputChange,
    handleReportDatapointStatusChange,
    handleSave,
    canReviewWithAi,
    handleReviewWithAi,
    canGenerateWithAi,
    handleGenerateWithAi,
    confirmAiDialogOpen,
    setConfirmAiDialogOpen,
    handleUpdateDatapointGenerationStatus,
    updateResponsiblePerson,
    isDirty,
    refetchDataRequest,
    contentVersion: datapointRequest.content_version,
  };
}
