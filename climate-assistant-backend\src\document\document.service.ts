import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import * as fs from 'fs';
import type { DocumentChunkGenerated } from 'src/types';
import { DocumentChunk } from 'src/document/entities/document-chunk.entity';
import {
  Document,
  DocumentStatus,
} from 'src/document/entities/document.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { DocumentParserService } from './document-parser.service';
import { DatapointDocumentChunk } from 'src/datapoint-document-chunk/entities/datapoint-document-chunk.entity';
import { DatapointRequest } from 'src/datapoint/entities/datapoint-request.entity';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { User } from 'src/users/entities/user.entity';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { JobProcessor, JobQueue } from 'src/types/jobs';
import { BullBoardInstance, InjectBullBoard } from '@bull-board/nestjs';
import { DEFAULT_JOB_CONFIG } from 'src/util/queue.config';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class DocumentService {
  constructor(
    @InjectRepository(DocumentChunk)
    private readonly documentChunkRepository: Repository<DocumentChunk>,
    @InjectRepository(DatapointDocumentChunk)
    private readonly datapointDocumentChunkRepository: Repository<DatapointDocumentChunk>,
    @InjectRepository(DatapointRequest)
    private readonly datapointRepository: Repository<DatapointRequest>,
    @InjectRepository(Document)
    private readonly documentRepository: Repository<Document>,
    private readonly documentParserService: DocumentParserService,
    private readonly workspaceService: WorkspaceService,
    @InjectQueue(JobProcessor.ChunkExtraction)
    private readonly chunkExtractionQueue: Queue,
    @InjectBullBoard() private readonly boardInstance: BullBoardInstance
  ) {}

  private readonly logger = new WorkerLogger(DocumentService.name);

  async getWorkspaceDocumentUploads(workspaceId: Workspace['id']) {
    // First, get documents without chunks (lightweight query)
    const documents = await this.documentRepository
      .createQueryBuilder('document')
      .leftJoinAndSelect('document.creator', 'creator')
      .select([
        'document.id',
        'document.name',
        'document.createdAt',
        'document.status',
        'document.documentType',
        'document.esrsCategory',
        'document.year',
        'document.month',
        'document.day',
        'document.remarks',
        'document.createdBy',
        'creator.id',
        'creator.name',
      ])
      .where('document.workspaceId = :workspaceId', { workspaceId })
      .orderBy('document.createdAt', 'DESC')
      .getMany();

    if (documents.length === 0) return [];

    // Process counts in parallel batches
    const BATCH_SIZE = 3; // Process 3 documents at a time
    const counts = new Map<string, number>();

    // Split documents into batches
    for (let i = 0; i < documents.length; i += BATCH_SIZE) {
      const batch = documents.slice(i, i + BATCH_SIZE);

      // Process each batch in parallel
      const batchCounts = await Promise.all(
        batch.map((doc) => this.getDocumentDatapointCount(doc.id))
      );

      // Store results
      batch.forEach((doc, index) => {
        counts.set(doc.id, batchCounts[index]);
      });

      // Optional: Add small delay between batches to prevent CPU spikes
      if (i + BATCH_SIZE < documents.length) {
        await new Promise((resolve) => setTimeout(resolve, 10));
      }
    }

    return documents.map((document) => ({
      ...document,
      creator: document.creator
        ? {
            id: document.creator.id,
            name: document.creator.name,
          }
        : null,
      datapointsCount: counts.get(document.id) || 0,
    }));
  }

  private async getDocumentDatapointCount(documentId: string): Promise<number> {
    const result = await this.documentRepository.query(
      `
    SELECT COUNT(DISTINCT ddcm."datapointRequestId") as count
    FROM document_chunk dc
    INNER JOIN datapoint_document_chunk ddcm 
      ON ddcm."documentChunkId" = dc.id
    WHERE dc."documentId" = $1 
      AND ddcm.active = true
  `,
      [documentId]
    );

    return parseInt(result[0]?.count || '0');
  }

  getUniqueDatapointCount(documentchunks: DocumentChunk[]) {
    const uniqueIds = new Set();
    // count of  datapointDocumentChunkMap
    return documentchunks
      .map((chunk) => {
        return chunk.datapointDocumentChunkMap.filter((map) => {
          if (map.active && !uniqueIds.has(map.datapointRequestId)) {
            uniqueIds.add(map.datapointRequestId);
            return true;
          }
          return false;
        }).length;
      })
      .reduce((acc, val) => acc + val, 0);
  }

  async findDocumentById(id: Document['id']) {
    const document = await this.documentRepository.findOne({
      where: { id },
    });

    return document;
  }

  async getDocumentData(id: Document['id']) {
    const documentData = await this.documentRepository.findOne({
      where: { id },
      relations: [
        'creator',
        'chunks.datapointDocumentChunkMap.datapointRequest.dataRequest',
      ],
      select: {
        creator: {
          id: true,
          name: true,
        },
      },
    });

    if (!documentData) {
      throw new NotFoundException(`Document not found`);
    }

    const datapointsCount = this.getUniqueDatapointCount(documentData.chunks);
    return {
      ...documentData,
      creator: {
        id: documentData.creator?.id,
        name: documentData.creator?.name,
      },
      datapointsCount: datapointsCount,
    };
  }

  async deleteDocument(
    id: Document['id'],
    workspaceId: Workspace['id'],
    userId: User['id']
  ): Promise<void> {
    const hasPermission = await this.documentRepository.exists({
      where: { id, workspaceId },
    });
    if (!hasPermission) {
      throw new UnauthorizedException(
        'You are not allowed to delete this Document'
      );
    }
    const document = await this.documentRepository.findOneOrFail({
      where: { id, workspaceId },
    });
    const documentChunks = await this.documentChunkRepository.find({
      where: { documentId: id },
      select: ['id'],
    });
    const documentChunkIds = documentChunks.map((chunk) => chunk.id);

    const relatedLinksCount = await this.datapointDocumentChunkRepository.count(
      {
        where: { documentChunkId: In(documentChunkIds) },
      }
    );

    await this.datapointDocumentChunkRepository.delete({
      documentChunkId: In(documentChunkIds),
    });
    await this.documentChunkRepository.delete({ documentId: id });
    await this.documentRepository.delete({ id, workspaceId });

    await this.workspaceService.storeActionHistory({
      event: 'document_deleted',
      ref: id,
      workspaceId: workspaceId,
      versionData: {
        event: 'document_deleted',
        doneBy: userId,
        data: {
          name: document.name,
          chunksCount: documentChunks.length,
          relatedLinksCount,
          status: document.status,
          createdAt: document.createdAt,
          deletedBy: userId,
        },
      },
    });

    if (fs.existsSync(document.path)) {
      fs.unlinkSync(document.path);
    }
  }

  async saveDocument({
    originalname,
    path,
    workspaceId,
    userId,
    documentType,
    esrsCategory,
    year,
    month,
    day,
    remarks,
  }: {
    originalname: string;
    path: string;
    workspaceId: Workspace['id'];
    userId: User['id'];
    documentType: string;
    esrsCategory: string[];
    year: number;
    month: number | null;
    day: number | null;
    remarks: string;
  }) {
    const documentExists = await this.documentRepository.exists({
      where: { workspaceId, name: originalname },
    });

    if (documentExists) {
      throw new BadRequestException('Document with name already exists');
    }

    const upload = await this.documentRepository.save({
      workspaceId,
      createdBy: userId,
      path,
      name: originalname,
      documentType,
      esrsCategory,
      year,
      month,
      day,
      remarks,
    });

    await this.workspaceService.storeActionHistory({
      event: 'new_document_uploaded',
      ref: upload.id,
      workspaceId: workspaceId,
      versionData: {
        event: 'new_document_uploaded',
        doneBy: userId,
        data: upload,
      },
    });

    try {
      await this.addChunkExtractionToQueue(upload.id, userId);
      this.documentRepository.update(upload.id, {
        status: DocumentStatus.QueuedForExtraction,
      });
    } catch (error) {
      this.logger.error(`Error adding job to queue:`, error);
    }
    return upload;

    //This is not awaited and done in the background
    //this.extractDocumentChunks(document.id, true);
  }

  async updateDocumentSettings({
    id,
    workspaceId,
    userId,
    documentType,
    esrsCategory,
    year,
    month,
    day,
    remarks,
  }: {
    id: Document['id'];
    workspaceId: Workspace['id'];
    userId: User['id'];
    documentType: string;
    esrsCategory: string[];
    year: number;
    month: number | null;
    day: number | null;
    remarks: string;
  }) {
    const document = await this.documentRepository.findOneOrFail({
      where: { id },
    });

    document.documentType = documentType;
    document.esrsCategory = esrsCategory;
    document.year = year;
    document.month = month;
    document.day = day;
    document.remarks = remarks;

    const update = await this.documentRepository.save(document);

    await this.workspaceService.storeActionHistory({
      event: 'document_settings_updated',
      ref: update.id,
      workspaceId: workspaceId,
      versionData: {
        event: 'document_settings_updated',
        doneBy: userId,
        data: update,
      },
    });
  }

  async extractDocumentChunks(
    id: Document['id'],
    userId: string,
    premiumMode?: boolean
  ) {
    this.logger.log(`Extracting document chunks for document: ${id}`);
    const document: Document = await this.documentRepository.findOne({
      where: { id: id },
      relations: ['workspace'],
    });

    if (!document) {
      this.logger.error(`Document not found: ${id}`);
      return;
    }

    document.status = DocumentStatus.ExtractingData;
    this.documentRepository.save(document);
    const chunks: DocumentChunkGenerated[] =
      await this.documentParserService.parseDocumentToMarkdown({
        path: document.path,
        userId: userId,
        workspaceId: document.workspace.id,
        premiumMode: premiumMode,
      });

    for (const chunkContent of chunks) {
      const cleanChunk = chunkContent.text.replace(/\n/g, '').trim();
      if (cleanChunk !== '') {
        await this.documentChunkRepository.save({
          documentId: id,
          page: chunkContent.metadata.pageNumber,
          content: chunkContent.text,
          metadataJson: JSON.stringify(chunkContent.metadata),
        });
        // console.log(savedChunk);
      }
    }
    document.status = DocumentStatus.DataExtractionFinished;
    this.documentRepository.save(document);
  }

  async findDocumentChunkById(id: DocumentChunk['id']) {
    const documentChunk = await this.documentChunkRepository.findOne({
      where: { id },
      relations: ['document'],
    });

    return documentChunk;
  }

  async deleteDocumentChunk({
    id,
    workspaceId,
    userId,
  }: {
    id: DocumentChunk['id'];
    workspaceId: Workspace['id'];
    userId: string;
  }) {
    const documentChunk = await this.documentChunkRepository.findOne({
      where: { id },
    });

    await this.workspaceService.storeActionHistory({
      event: 'document_chunk_deleted',
      ref: documentChunk.documentId,
      workspaceId: workspaceId,
      versionData: {
        event: 'document_chunk_deleted',
        doneBy: userId,
        data: documentChunk,
      },
    });

    await this.documentChunkRepository.delete({ id });
  }

  async getDocumentChunk(id: DocumentChunk['id']) {
    const documentChunk = await this.documentChunkRepository.findOne({
      where: { id },
      relations: ['datapointDocumentChunkMap.datapointRequest'],
    });

    if (!documentChunk) {
      throw new NotFoundException(`Document Chunk not found`);
    }

    return documentChunk;
  }

  async createDocumentChunkMap({
    documentChunkId,
    datapointRequestId,
    userId,
  }: {
    documentChunkId: DocumentChunk['id'];
    datapointRequestId: DatapointRequest['id'];
    userId: string;
  }) {
    const documentChunk = await this.documentChunkRepository.findOne({
      where: { id: documentChunkId },
    });

    const datapointRequest = await this.datapointRepository.findOne({
      where: { id: datapointRequestId },
    });

    if (!datapointRequest || !documentChunk) {
      throw new NotFoundException(`Linking components not found`);
    }

    await this.datapointDocumentChunkRepository.save({
      documentChunk,
      datapointRequest,
      createdBy: userId,
    });
  }

  async updateDocumentChunkMap({
    documentChunkId,
    datapointRequestId,
    userId,
    status,
  }: {
    documentChunkId: DocumentChunk['id'];
    datapointRequestId: DatapointRequest['id'];
    userId: string;
    status: boolean;
  }) {
    const documentChunk = await this.datapointDocumentChunkRepository.findOne({
      where: {
        documentChunkId,
        datapointRequestId,
      },
    });

    if (!documentChunk) {
      if (status === false) {
        throw new NotFoundException(`Linking components not found`);
      } else {
        await this.createDocumentChunkMap({
          documentChunkId,
          datapointRequestId,
          userId,
        });
      }
    } else {
      await this.datapointDocumentChunkRepository.update(
        {
          id: documentChunk.id,
        },
        {
          active: status,
          modifiedBy: userId,
        }
      );
    }
  }

  async bulkUpdateDocumentChunkMap({
    documentChunkId,
    userId,
    data,
  }: {
    documentChunkId: DocumentChunk['id'];
    userId: string;
    data: {
      datapointRequestId: DatapointRequest['id'];
      linked: boolean;
    }[];
  }) {
    for (const { datapointRequestId, linked } of data) {
      try {
        await this.updateDocumentChunkMap({
          documentChunkId,
          datapointRequestId,
          userId,
          status: linked,
        });
      } catch (error) {
        console.log(error);
      }
    }
  }

  async updateDocumentStatus(
    documentId: Document['id'],
    { status }: { status: DocumentStatus }
  ) {
    await this.documentRepository.update(documentId, { status });
  }

  async addChunkExtractionToQueue(
    documentId: string,
    userId: string
  ): Promise<void> {
    try {
      await this.chunkExtractionQueue.add(
        JobQueue.ChunkExtract,
        {
          documentId,
          userId,
        },
        {
          ...DEFAULT_JOB_CONFIG,
          jobId: `chunkExtraction-${documentId}`,
        }
      );
    } catch (error) {
      this.logger.error(`Error adding job to queue:`, error);
    }
  }
}
