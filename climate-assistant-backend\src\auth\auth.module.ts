import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Auth<PERSON>ontroller } from './auth.controller';
import { AuthService } from './auth.service';
import { AuthGuard } from './auth.guard';
import { PermissionGuard } from './guard/permission.guard';
import { PermissionService } from './services/permission.service';
import { Permission } from './entities/permission.entity';
import { RolePermission } from './entities/role-permission.entity';
import { UsersModule } from '../users/users.module';
import { WorkspaceModule } from '../workspace/workspace.module';
import { SsoService } from './sso.service';
import { WebhookProcessorService } from './webhook-processor.service';
import { User } from '../users/entities/user.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { UserWorkspace } from '../users/entities/user-workspace.entity';
import { WorkspaceSSO } from '../workspace/entities/workspace-sso.entity';
import { Token } from '../users/entities/token.entity';

@Module({
  imports: [
    UsersModule,
    WorkspaceModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      global: true,
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '7 days' },
      }),
      inject: [ConfigService],
    }),
    TypeOrmModule.forFeature([
      Permission,
      RolePermission,
      User,
      Workspace,
      UserWorkspace,
      WorkspaceSSO,
      Token,
    ]),
  ],
  providers: [
    AuthService,
    AuthGuard,
    PermissionService,
    PermissionGuard,
    SsoService,
    WebhookProcessorService,
  ],
  controllers: [AuthController],
  exports: [
    JwtModule,
    AuthGuard,
    PermissionService,
    PermissionGuard,
    SsoService,
  ],
})
export class AuthModule {}
