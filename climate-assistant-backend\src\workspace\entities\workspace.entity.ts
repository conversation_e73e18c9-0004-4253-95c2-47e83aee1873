// entities/workspace.entity.ts
import { UserWorkspace } from '../../users/entities/user-workspace.entity';
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { Document } from '../../document/entities/document.entity';
import { VersionHistory } from './version-history.entity';
import { Company } from './company.entity';
import { Project } from '../../project/entities/project.entity';

@Entity()
export class Workspace {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'boolean', default: false })
  ssoEnabled: boolean;

  @Column({ type: 'boolean', default: false })
  allowAutoProvisioning: boolean;

  @Column({ type: 'json', nullable: true })
  ssoConfig: {
    allowedDomains?: string[];
    defaultRoleId?: string;
    autoProvision?: boolean;
    workosOrganizationId?: string;
    requireSso?: boolean; // Force SSO for this workspace
  };

  @OneToMany(() => UserWorkspace, (userWorkspace) => userWorkspace.workspace)
  userWorkspaces: UserWorkspace[];

  @OneToMany(() => Company, (company) => company.workspace)
  companies: Company[];

  @OneToMany(() => Document, (document) => document.workspace)
  documents: Document[];

  @OneToMany(() => Project, (project) => project.workspace)
  projects: Project[];

  @OneToMany(() => VersionHistory, (versionHistory) => versionHistory.workspace)
  versionHistories: VersionHistory[];
}
