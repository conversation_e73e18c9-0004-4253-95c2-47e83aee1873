# SSO with WorkOS - Technical Documentation

## Overview

This document provides comprehensive technical documentation for the Single Sign-On (SSO) implementation using WorkOS in the Glacier Climate Assistant application. The implementation supports enterprise-grade SSO authentication alongside existing password-based JWT authentication with full backward compatibility.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Backend Implementation](#backend-implementation)
3. [Frontend Implementation](#frontend-implementation)
4. [Database Schema](#database-schema)
5. [Security Considerations](#security-considerations)
6. [API Reference](#api-reference)
7. [Configuration Guide](#configuration-guide)
8. [Deployment Guide](#deployment-guide)
9. [Troubleshooting](#troubleshooting)
10. [Extension Guide](#extension-guide)

## Architecture Overview

### System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │    WorkOS       │
│   (React)       │    │   (NestJS)      │    │   (Identity)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │   1. Check Domain     │                       │
         ├──────────────────────►│                       │
         │   2. Initiate SSO     │                       │
         ├──────────────────────►│   3. Get Auth URL     │
         │                       ├──────────────────────►│
         │   4. Redirect to SSO  │   5. Return Auth URL  │
         │◄──────────────────────┤◄──────────────────────┤
         │                       │                       │
         │   6. User Auth        │                       │
         ├───────────────────────┼──────────────────────►│
         │                       │                       │
         │   7. Callback         │   8. Exchange Code    │
         │◄──────────────────────┤◄──────────────────────┤
         │                       │   9. Get Profile      │
         │                       ├──────────────────────►│
         │                       │   10. User Data       │
         │                       │◄──────────────────────┤
         │   11. Set JWT Cookie  │                       │
         │◄──────────────────────┤                       │
         │   12. Redirect        │                       │
         │◄──────────────────────┤                       │
```

### Key Components

- **WorkOS Integration**: Handles OAuth 2.0 flow and user profile management
- **JWT Authentication**: Maintains session management with secure HTTP-only cookies
- **Domain-based SSO Discovery**: Automatic SSO detection based on email domains
- **Multi-workspace Support**: Users can belong to multiple workspaces with different authentication methods
- **Role-based Access Control**: Maintains existing permission system for SSO users

## Backend Implementation

### Core Services

#### SsoService

**Location**: `climate-assistant-backend/src/auth/sso.service.ts`

Primary service handling all SSO operations with support for **SP-initiated** and **IdP-initiated** flows:

```typescript
@Injectable()
export class SsoService {
  private workos: WorkOS;
  
  Key methods:
  - initiateSSOLogin(): Generates WorkOS authorization URL (SP-initiated)
  - handleSSOCallback(): Processes OAuth callback for both flow types
  - provisionSSOUser(): Creates or updates users from SSO profile
  - checkDomainSSO(): Fast indexed domain lookup
  - handleWebhook(): Secure webhook processing with HMAC verification
  - getSAMLMetadata(): Generates SAML Service Provider metadata
  - handleSAMLLogout(): Processes SAML Single Logout requests
}
```

**Key Features**:
- ✅ **Dual Flow Support**: SP-initiated (with state) + IdP-initiated (without state)
- ✅ **Secure State Management**: JWT-signed parameters with timing-safe validation
- ✅ **Smart Workspace Discovery**: Organization ID → Workspace mapping for IdP flows
- ✅ **Optimized Performance**: O(log n) domain lookups with database indexing
- ✅ **Enterprise Security**: HMAC webhook verification, redirect URL validation
- ✅ **Comprehensive Logging**: Detailed audit trails for both flow types

#### SSO Flow Type Handling

**Dual Flow Support Implementation:**

```typescript
async handleSSOCallback(code: string, state: string): Promise<string> {
  // Get user profile from WorkOS
  const { profile } = await this.workos.sso.getProfileAndToken({ code, clientId });
  
  let workspaceId: string;
  let redirectUrl: string = '/dashboard';

  // Check flow type based on state parameter presence
  if (!state) {
    // IdP-initiated: Find workspace by organizationId
    console.log('Handling IdP-initiated SSO flow');
    
    const ssoConfig = await this.workspaceSSORepository.findOne({
      where: { workosOrganizationId: profile.organizationId, enabled: true }
    });
    
    if (ssoConfig) {
      workspaceId = ssoConfig.workspaceId;
    } else {
      // Fallback to legacy workspace.ssoConfig
      const workspace = await this.findWorkspaceByOrgId(profile.organizationId);
      workspaceId = workspace.id;
    }
    
  } else {
    // SP-initiated: Verify JWT state and extract workspace
    const decodedState = this.jwtService.verify(state);
    workspaceId = decodedState.workspaceId;
    redirectUrl = decodedState.redirectUrl || '/dashboard';
  }
  
  // Continue with user provisioning...
}
```

**Flow Detection Benefits:**
- ✅ **Automatic Detection**: No configuration needed
- ✅ **Backward Compatible**: Existing SP flows unchanged  
- ✅ **Enterprise UX**: Natural IdP dashboard workflow
- ✅ **Development Friendly**: WorkOS Test IdP works seamlessly

#### Updated AuthService

**Location**: `climate-assistant-backend/src/auth/auth.service.ts`

Extended to support SSO users:

```typescript
// New SSO-specific methods:
async findBySSOId(ssoUserId: string, ssoProviderId: string): Promise<User | null>
async createSSOUser(userData: CreateSSOUserDto): Promise<User>
```

#### Enhanced UsersService

**Location**: `climate-assistant-backend/src/users/users.service.ts`

Extended with SSO support methods:

```typescript
// SSO user management:
async findBySSOId(ssoUserId: string, ssoProviderId: string): Promise<User | null>
async createSSOUser(userData: SSOUserData): Promise<User>
async ensureWorkspaceAccess(userId: string, workspaceId: string, defaultRoleId?: string): Promise<void>
```

### API Endpoints

#### SSO Authentication Endpoints

| Method | Endpoint | Description | Rate Limit |
|--------|----------|-------------|------------|
| POST | `/auth/sso/init` | Initiate SSO login | 5/min |
| GET | `/auth/sso/callback` | Handle SSO callback | None |
| GET | `/auth/sso/check-domain/:domain` | Check domain SSO | 10/min |
| POST | `/auth/sso/link` | Link account to SSO | None (auth required) |
| GET | `/auth/sso/providers/:workspaceId` | List SSO providers | None (auth required) |
| GET | `/auth/saml/metadata` | Get SAML SP metadata | None |
| GET | `/auth/sso/logout` | SSO logout endpoint | None |
| POST | `/auth/webhooks/workos` | Handle WorkOS webhooks | 100/min (global) |

#### Request/Response Examples

**Initiate SSO Login**:
```typescript
// Request
POST /auth/sso/init
{
  "workspaceId": "uuid-workspace-id",
  "redirectUrl": "https://app.glacier.eco/dashboard"
}

// Response
{
  "authorizationUrl": "https://auth.workos.com/sso/authorize?..."
}
```

**Check Domain SSO**:
```typescript
// Request
GET /auth/sso/check-domain/company.com

// Response
{
  "hasSso": true,
  "workspaceId": "uuid-workspace-id"
}
```

**Get SAML Metadata**:
```typescript
// Request
GET /auth/saml/metadata

// Response (Content-Type: application/xml)
<?xml version="1.0" encoding="UTF-8"?>
<md:EntityDescriptor xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata"
                     entityID="https://app.glacier.eco/api/auth/saml/metadata">
  <md:SPSSODescriptor AuthnRequestsSigned="false" 
                      WantAssertionsSigned="true" 
                      protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
    <!-- Service Provider metadata for SAML identity providers -->
  </md:SPSSODescriptor>
</md:EntityDescriptor>
```

**SSO Logout**:
```typescript
// For SAML SLO (Single Logout)
GET /auth/sso/logout?SAMLRequest=<encoded_request>&RelayState=<state>

// For regular logout with redirect
GET /auth/sso/logout?RelayState=/login

// Response: 
// - SAML LogoutResponse XML (if SAMLRequest present)
// - JSON response (if Accept: application/json)
// - HTTP redirect (default behavior)
```

### Configuration Management

**Location**: `climate-assistant-backend/src/util/config.ts`

```typescript
export interface WorkOSConfig {
  apiKey: string;
  clientId: string;
  redirectUri: string;
  webhookSecret?: string;
}

export const getWorkOSConfig = (): WorkOSConfig => {
  // Validates and returns WorkOS configuration
  // Throws error if required variables are missing
}
```

### Rate Limiting & Security

**Implementation**: Uses `@nestjs/throttler` for rate limiting

```typescript
// Global configuration
ThrottlerModule.forRoot([{
  ttl: 60000, // 1 minute
  limit: 100, // 100 requests per minute per IP
}])

// Endpoint-specific limits
@Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 requests per minute
```

## Frontend Implementation

### API Client

#### SSO Authentication API
**Location**: `climate-assistant-frontend/src/api/authentication/authentication.api.ts`

```typescript
// Core SSO functions
export const initiateSSOLogin = async (workspaceId: string, redirectUrl?: string)
export const checkDomainSSO = async (domain: string)
export const linkAccountToSSO = async (ssoData: LinkAccountData)
export const getSSOProviders = async (workspaceId: string)
```

#### Workspace Creation API
**Location**: `climate-assistant-frontend/src/api/workspace-settings/workspace-settings.api.ts`

```typescript
// Enhanced workspace creation with SSO
export interface CreateWorkspaceRequest {
  name: string;
  email: string;
  password: string;
  ssoConfig?: {
    enabled: boolean;
    domain: string;
    protocol: 'saml' | 'oauth' | 'oidc';
    workosOrganizationId: string;
    workosConnectionId: string;
  };
}

export const createWorkspace = async (workspaceDetail: CreateWorkspaceRequest)
```

### React Components

#### SSOLoginButton Component

**Location**: `climate-assistant-frontend/src/components/login/SSOLoginButton.tsx`

```typescript
interface SSOLoginButtonProps {
  workspaceId: string;
  onSSOLogin: (workspaceId: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}
```

**Features**:
- Loading states with spinner animation
- Disabled state handling
- Customizable styling
- TypeScript interfaces for type safety

#### SSOCallback Component

**Location**: `climate-assistant-frontend/src/pages/login/SSOCallback.tsx`

Handles SSO authentication completion:

```typescript
// Key functionality:
// - Processes URL parameters for errors
// - Invalidates React Query cache
// - Redirects to dashboard on success
// - Shows toast notifications for errors
```

**Error Handling**:
- `sso_failed`: General authentication failure
- `sso_not_enabled`: SSO not configured for workspace
- `domain_not_allowed`: Email domain not authorized
- `sso_provider_error`: Identity provider error

#### SSO Login Page with Invitation Support

**Location**: `climate-assistant-frontend/src/pages/login/SSOLogin.tsx`

Enhanced dedicated SSO login page with invitation handling:

```typescript
// Key features:
interface InvitationSupport {
  urlParameters: {
    email?: string;           // Pre-filled from invitation
    invited?: 'true';         // Marks as invitation flow
  };
  
  userExperience: {
    welcomeMessage: string;   // "Welcome! Complete Your Invitation"
    autoSSOCheck: boolean;    // Automatically checks SSO for invited users
    invitationAlert: boolean; // Shows workspace invitation notice
  };
}

// URL handling:
/sso-login?email=<EMAIL>&invited=true
```

**Features**:
- ✅ **URL Parameter Handling**: Auto-fills email from invitation links
- ✅ **Invitation Detection**: Shows special UI for invited users
- ✅ **Auto-SSO Check**: Automatically validates SSO for invitation emails
- ✅ **Enhanced UX**: Welcome messages and invitation alerts
- ✅ **Navigation**: Links back to regular login and SSO info pages

**User Experience Flow**:
```typescript
// Invitation email → SSO login page
1. User clicks invitation email link
2. Page loads with email pre-filled and invitation UI
3. System auto-checks SSO availability for domain
4. User sees "Welcome! Complete Your Invitation" message
5. One-click SSO login → Workspace access with assigned role
```

#### SuperAdminSettings Component

**Location**: `climate-assistant-frontend/src/pages/dashboard/settings/SuperAdminSettings.tsx`

Enhanced workspace creation form with SSO configuration:

```typescript
// Key features:
interface SSOConfiguration {
  enabled: boolean;
  domain: string;                    // Company domain (e.g., "acme.com")
  protocol: 'saml' | 'oauth' | 'oidc'; // SSO protocol selection
  workosOrganizationId: string;      // WorkOS org ID (org_xxx)
  workosConnectionId: string;        // WorkOS connection ID (conn_xxx)
}

// Form validation:
- Domain format validation (regex)
- WorkOS ID format validation (org_, conn_ prefixes)
- Required field validation when SSO enabled
- Real-time visual feedback with red borders
```

**UI Components**:
- ✅ **Toggle Switch**: Enable/disable SSO with visual feedback
- ✅ **Protocol Selector**: Dropdown for SAML 2.0, OAuth 2.0, OpenID Connect
- ✅ **Form Validation**: Real-time validation with helpful error messages  
- ✅ **WorkOS Integration**: Automatic WorkspaceSSO record creation
- ✅ **Success Feedback**: Enhanced success message with SSO status

**User Experience**:
```typescript
// Workflow: Super Admin creates workspace
1. Fill basic details (name, email, password)
2. Toggle SSO switch → SSO fields appear
3. Enter domain, select protocol, add WorkOS IDs
4. Real-time validation provides immediate feedback
5. Create workspace → Automatically configures SSO
6. Users from domain.com immediately get SSO login
```
- `user_deactivated`: SSO account deactivated
- `invalid_state`: Invalid or expired state parameter

#### Enhanced LoginForm

**Location**: `climate-assistant-frontend/src/pages/login/LoginForm.tsx`

Enhanced with dynamic SSO detection:

```typescript
// Key features:
// - Email domain checking on blur
// - Dynamic SSO option display
// - Elegant UI separation
// - Error handling with toast notifications
// - Loading states during SSO initiation
```

### Routing Configuration

**Location**: `climate-assistant-frontend/src/components/router/Routes.tsx`

```typescript
// SSO-related routes
{
  path: '/sso-login',
  element: <SSOLogin />,
  handle: { breadcrumb: 'SSO Login' },
},
{
  path: '/auth/sso/callback',
  element: <SSOCallback />,
  handle: { breadcrumb: 'SSO Callback' },
},
{
  path: '/sso',
  element: <SSOLandingPage />,
  handle: { breadcrumb: 'SSO Integration' },
},
{
  path: '/sso-setup',
  element: <SSOOnboardingGuide />,
  handle: { breadcrumb: 'SSO Setup Guide' },
}
```

**Route Functions**:
- `/sso-login`: Dedicated SSO login with invitation support
- `/auth/sso/callback`: Handles authentication completion
- `/sso`: Public SSO information and onboarding
- `/sso-setup`: Technical setup guide for clients

## Database Schema

### User Provisioning Models

The SSO system supports two user access models controlled by the `allowAutoProvisioning` flag:

#### **1. Auto-Provisioning Mode (allowAutoProvisioning = true)**
- **Default behavior**: Users from allowed SSO domains can self-provision
- **Use case**: Open collaboration, internal teams
- **Flow**: User logs in → Domain validated → User auto-created → Assigned default role → Access granted

#### **2. Invitation-Only Mode (allowAutoProvisioning = false)**  
- **Controlled access**: Users must be explicitly invited before accessing
- **Use case**: Client workspaces, sensitive projects, controlled environments
- **Flow**: Admin invites user → Invitation email sent → User accepts via SSO → Access granted with assigned role

### User Entity Updates

**Location**: `climate-assistant-backend/src/users/entities/user.entity.ts`

```typescript
@Entity()
export class User {
  // Existing fields...
  
  @Column({ type: 'varchar', nullable: true })
  authMethod: 'password' | 'sso';
  
  @Column({ type: 'varchar', nullable: true })
  ssoProviderId: string; // WorkOS connection ID
  
  @Column({ type: 'varchar', nullable: true })
  ssoUserId: string; // External user ID from SSO provider
  
  @Column({ type: 'timestamp', nullable: true })
  lastSsoLogin: Date;
}
```

### Workspace Entity Updates

**Location**: `climate-assistant-backend/src/workspace/entities/workspace.entity.ts`

```typescript
@Entity()
export class Workspace {
  // Existing fields...
  
  @Column({ type: 'boolean', default: false })
  ssoEnabled: boolean;
  
  @Column({ type: 'boolean', default: false })
  allowAutoProvisioning: boolean;
  
  @Column({ type: 'json', nullable: true })
  ssoConfig: {
    defaultRoleId?: string;
    autoProvision?: boolean;
    workosOrganizationId?: string;
    requireSso?: boolean;
  };
}
```

**Key Fields:**
- `ssoEnabled`: Whether SSO is activated for this workspace
- `allowAutoProvisioning`: Controls user access model:
  - `true`: Any user from allowed SSO domains can auto-provision themselves
  - `false`: Users must be explicitly invited before accessing the workspace
- `ssoConfig`: Basic SSO configuration (detailed config now in WorkspaceSSO table)

### WorkspaceSSO Entity

**Location**: `climate-assistant-backend/src/workspace/entities/workspace-sso.entity.ts`

```typescript
@Entity('workspace_sso')
@Index('idx_workspace_sso_domain', ['domain']) // Fast domain lookups
@Index('idx_workspace_sso_workspace_enabled', ['workspaceId', 'enabled'])
export class WorkspaceSSO {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  @Index()
  workspaceId: string;

  @ManyToOne(() => Workspace, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'workspaceId' })
  workspace: Workspace;

  // Normalized domain for fast lookups (e.g., "company.com")
  @Column({ type: 'varchar', length: 255 })
  @Index() // Critical for fast domain lookups
  domain: string;

  @Column({ type: 'boolean', default: true })
  enabled: boolean;

  // WorkOS Organization ID for this domain
  @Column({ type: 'varchar', length: 255 })
  workosOrganizationId: string;

  // WorkOS Connection ID 
  @Column({ type: 'varchar', length: 255 })
  workosConnectionId: string;

  // SSO Protocol (saml, oauth, etc.)
  @Column({ type: 'varchar', length: 50, default: 'saml' })
  protocol: 'saml' | 'oauth' | 'oidc';

  // JSON for additional config (now just supplementary data)
  @Column({ type: 'jsonb', nullable: true })
  additionalConfig?: {
    defaultRoleId?: string;
    attributeMapping?: Record<string, string>;
    customSettings?: Record<string, any>;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
```

### Performance Benefits

| Operation | Implementation | Performance | Benefits |
|-----------|----------------|-------------|----------|
| **Domain Lookup** | Indexed database lookup | O(log n) | **Lightning fast** |
| **Multi-domain Support** | Separate table rows | Scalable | **Enterprise ready** |
| **Domain Management** | Standard CRUD operations | Maintainable | **Developer friendly** |
| **Concurrent Access** | Row-level locking | Efficient | **High throughput** |

### Migration Script

**Location**: `climate-assistant-backend/src/database/migrations/1754000000000-add-sso-support.ts`

```sql
-- User table updates
ALTER TABLE "user" ADD COLUMN "authMethod" character varying;
ALTER TABLE "user" ADD COLUMN "ssoProviderId" character varying;
ALTER TABLE "user" ADD COLUMN "ssoUserId" character varying;
ALTER TABLE "user" ADD COLUMN "lastSsoLogin" TIMESTAMP;

-- Workspace table updates
ALTER TABLE "workspace" ADD COLUMN "ssoEnabled" boolean NOT NULL DEFAULT false;
ALTER TABLE "workspace" ADD COLUMN "ssoConfig" json;

-- Indexes for performance
CREATE INDEX "IDX_user_sso_provider_user" ON "user" ("ssoProviderId", "ssoUserId") 
WHERE "ssoProviderId" IS NOT NULL AND "ssoUserId" IS NOT NULL;
```

## Security Considerations

### Authentication Flow Security

1. **State Parameter Validation**:
   - JWT-signed state parameters with 10-minute expiry
   - Prevents CSRF attacks and replay attacks
   - Includes workspace ID and redirect URL validation

2. **Webhook Security**:
   - HMAC-SHA256 signature verification with timing-safe comparison
   - Timestamp validation with 5-minute tolerance (replay attack prevention)
   - Asynchronous processing to prevent timeout vulnerabilities
   - IP allowlisting for WorkOS webhook endpoints
   - Idempotency handling for duplicate events
   - Raw body parsing for proper signature verification

3. **Domain Restrictions**:
   - Configurable allowed email domains per workspace
   - Prevents unauthorized access from external domains
   - Real-time domain validation during authentication

4. **Session Management**:
   - HTTP-only, secure cookies with SameSite protection
   - 7-day JWT token expiry
   - Proper session invalidation on logout

5. **Redirect URL Security**:
   - Allowlist-based validation for RelayState parameters
   - Path-based validation for internal redirects
   - Same-origin validation for absolute URLs
   - Protection against open redirect vulnerabilities

### Webhook Security Implementation

Our webhook implementation follows enterprise security best practices:

```typescript
// Secure webhook processing flow
async handleWebhook(rawBody: Buffer, signature: string, userIP?: string) {
  // 1. IP Validation
  if (userIP && !this.isWorkOSIP(userIP)) {
    throw new UnauthorizedException('Invalid source IP');
  }

  // 2. Timestamp Validation (5-minute tolerance)
  const timestamp = this.extractTimestampFromSignature(signature);
  const now = Math.floor(Date.now() / 1000);
  if (Math.abs(now - timestamp) > 300) {
    throw new UnauthorizedException('Request timestamp outside tolerance');
  }

  // 3. Timing-Safe Signature Verification
  const expectedSignature = this.computeWebhookSignature(
    rawBody.toString(), timestamp, webhookSecret
  );
  if (!crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
    throw new UnauthorizedException('Invalid webhook signature');
  }

  // 4. Immediate Response + Async Processing
  setImmediate(() => this.processWebhookAsync(payload));
  return { received: true };
}
```

**Key Security Features**:
- **Timing Attack Prevention**: Uses `crypto.timingSafeEqual()` for signature comparison
- **Replay Attack Prevention**: 5-minute timestamp tolerance window
- **Timeout Prevention**: Immediate 200 response with async processing
- **Idempotency**: Prevents duplicate event processing
- **IP Filtering**: Validates requests from WorkOS IP ranges
- **Open Redirect Prevention**: Allowlist-based validation for RelayState and redirect URLs
- **Organization Validation**: Mandatory organization ID verification on SSO callbacks

### Permission System Integration

```typescript
// Enhanced cache key for permission isolation
const cacheKey = `permissions:${userId}:${workspaceId}:${authMethod || 'password'}`;
```

Benefits:
- Separate permission caches for SSO and password users
- Consistent permission checking across authentication methods
- No changes required to existing permission logic

## API Reference

### SSO User Invitation System

The system provides hybrid user access control through the existing invitation endpoints with SSO enhancements.

#### **Enhanced User Invitation Endpoint**

**Endpoint**: `POST /api/workspace/inviteUsers`

**Behavior**:
- **Auto-detects SSO**: Checks if invited user's domain has SSO configured
- **Smart Email Routing**: 
  - SSO domains → Sends SSO invitation with link to `/sso-login?email=<EMAIL>&invited=true`
  - Non-SSO domains → Sends traditional password setup invitation
- **Role Assignment**: Respects the role specified in invitation (not default role)

**Example**:
```typescript
// Admin invites SSO user
POST /api/workspace/inviteUsers
{
  emails: ["<EMAIL>"],
  role: "Admin",
  workspaceId: "workspace-uuid"
}

// System behavior:
// 1. Detects acme.com has SSO enabled
// 2. Creates user with specified role
// 3. Sends SSO invitation email
// 4. User clicks link → SSO login → Immediate access
```

#### **Auto-Provisioning Control**

**Database Control**:
```sql
-- Disable auto-provisioning (require invitations)
UPDATE workspace SET "allowAutoProvisioning" = false WHERE id = 'workspace-id';

-- Enable auto-provisioning (open access)
UPDATE workspace SET "allowAutoProvisioning" = true WHERE id = 'workspace-id';
```

**Effect on SSO Login**:
- `allowAutoProvisioning = true`: Any domain user can login and get default role
- `allowAutoProvisioning = false`: Only invited users can access, get assigned role

### Workspace Creation with SSO

#### Enhanced Workspace Creation Endpoint

**Endpoint**: `POST /api/users/create-workspace`

**Request Body**:
```typescript
{
  name: string;
  email: string;
  password: string;
  ssoConfig?: {
    enabled: boolean;
    domain: string;
    protocol: 'saml' | 'oauth' | 'oidc';
    workosOrganizationId: string;
    workosConnectionId: string;
  };
}
```

**Response**:
```typescript
{
  user: User;
  workspace: Workspace;
  company: Company;
}
```

**Features**:
- ✅ **Atomic Operation**: Creates workspace + SSO config in single transaction
- ✅ **Automatic WorkspaceSSO**: Creates indexed domain record when SSO enabled
- ✅ **Validation**: Frontend and backend validation for WorkOS IDs
- ✅ **Backward Compatibility**: SSO config is optional

**Example Usage**:
```typescript
// Frontend: SuperAdminSettings.tsx
const workspace = await createWorkspace({
  name: "Acme Corporation",
  email: "<EMAIL>",
  password: "secure123",
  ssoConfig: {
    enabled: true,
    domain: "acme.com", 
    protocol: "saml",
    workosOrganizationId: "org_01H9XVN5ZK8N2J3M4P5Q6R7S8T",
    workosConnectionId: "conn_01H9XVN5ZK8N2J3M4P5Q6R7S8T"
  }
});
```

### Data Transfer Objects (DTOs)

#### InitiateSSOLoginDto
```typescript
class InitiateSSOLoginDto {
  @IsUUID('4') workspaceId: string;
  @IsUrl() @IsOptional() redirectUrl?: string;
}
```

#### CheckDomainSSOResponseDto
```typescript
class CheckDomainSSOResponseDto {
  hasSso: boolean;
  workspaceId?: string;
}
```

#### LinkAccountToSSODto
```typescript
class LinkAccountToSSODto {
  @IsString() @IsNotEmpty() ssoUserId: string;
  @IsString() @IsNotEmpty() ssoProviderId: string;
}
```

#### CreateUserWithCompanyAndWorkspaceDto
```typescript
class CreateUserWithCompanyAndWorkspaceDto {
  @IsEmail() email: string;
  @IsNotEmpty() @MinLength(6) password: string;
  @IsString() @IsNotEmpty() companyName: string;
  
  @IsOptional() @IsObject()
  ssoConfig?: {
    enabled: boolean;
    domain: string;
    protocol: 'saml' | 'oauth' | 'oidc';
    workosOrganizationId: string;
    workosConnectionId: string;
  };
}
```

### SSO Service Methods (Optimized)

#### Domain Management Methods
```typescript
class SsoService {
  // Fast indexed domain lookup O(log n)
  async checkDomainSSO(domain: string): Promise<{
    hasSso: boolean;
    workspaceId?: string;
    ssoConfig?: WorkspaceSSO;
  }>;

  // Get all SSO domains for a workspace
  async getWorkspaceSSO(workspaceId: string): Promise<WorkspaceSSO[]>;

  // Add new SSO domain with validation
  async addSSODomain(data: {
    workspaceId: string;
    domain: string;
    workosOrganizationId: string;
    workosConnectionId: string;
    protocol?: 'saml' | 'oauth' | 'oidc';
    additionalConfig?: any;
  }): Promise<WorkspaceSSO>;

  // Remove SSO domain safely
  async removeSSODomain(workspaceId: string, domain: string): Promise<void>;

  // Update domain configuration
  async updateSSODomain(
    workspaceId: string,
    domain: string,
    updates: Partial<WorkspaceSSO>
  ): Promise<WorkspaceSSO>;

  // Migrate JSON config to structured table
  async migrateLegacySSO(workspaceId: string): Promise<WorkspaceSSO[]>;
}
```

#### Security & SAML Methods
```typescript
class SsoService {
  // Generate SAML Service Provider metadata XML
  async getSAMLMetadata(): Promise<string>;

  // Handle SAML Single Logout (SLO) requests
  async handleSAMLLogout(samlRequest: string): Promise<string>;

  // Secure webhook processing with timing-safe comparison
  async handleWebhook(rawBody: Buffer, signature: string, userIP?: string): Promise<{received: boolean}>;

  // Validate redirect URLs to prevent open redirect attacks
  private validateRedirectUrl(url: string | undefined): string | null;
}
```

### Error Responses

Standard error format:
```typescript
{
  "statusCode": 400,
  "message": "SSO not enabled for this workspace",
  "error": "Bad Request"
}
```

Common error codes:
- `400`: SSO not enabled, invalid parameters
- `401`: Invalid webhook signature, authentication failed
- `403`: Domain not allowed, insufficient permissions
- `429`: Rate limit exceeded

## Configuration Guide

### Environment Variables

**Backend (.env)**:
```bash
# Required WorkOS Configuration
WORKOS_API_KEY=wk_live_xxxxxxxxxxxx
WORKOS_CLIENT_ID=client_xxxxxxxxxxxx
WORKOS_REDIRECT_URI=https://app.glacier.eco/api/auth/sso/callback
WORKOS_WEBHOOK_SECRET=wh_secret_xxxxxxxxxxxx
```

**Frontend (.env.example)**:
```bash
# Optional - for client-side SSO features
VITE_WORKOS_CLIENT_ID=client_xxxxxxxxxxxx
VITE_SSO_ENABLED=true
```

### Nginx Configuration

**Location**: `nginx/app.conf`

```nginx
# SSO callback handling - specific route for better control
location /api/auth/sso/ {
    proxy_pass http://backend:3000/auth/sso/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # Important for SSO redirects
    proxy_set_header X-Original-URI $request_uri;
    proxy_redirect off;
    
    # Cookie handling
    proxy_pass_header Set-Cookie;
    proxy_pass_header Cookie;
    
    proxy_http_version 1.1;
    proxy_set_header Connection "";
    
    proxy_read_timeout 180;
    proxy_send_timeout 180;
}
```

### WorkOS Dashboard Configuration

1. **Create Organization**: Set up organization in WorkOS dashboard
2. **Configure Connections**: Add SAML/OIDC identity providers
3. **Set Redirect URI**: `https://your-domain.com/api/auth/sso/callback`
4. **Configure Webhooks**: Point to `https://your-domain.com/api/auth/webhooks/workos`
5. **Copy Credentials**: API Key, Client ID, and Webhook Secret

### Workspace SSO Configuration

Enable SSO for a specific workspace:

```sql
UPDATE workspace 
SET 
  sso_enabled = true,
  sso_config = jsonb_build_object(
    'workosOrganizationId', 'org_xxxxxxxxxxxx',
    'allowedDomains', ARRAY['company.com'],
    'defaultRoleId', (SELECT id FROM role WHERE name = 'CONTRIBUTOR'),
    'autoProvision', true,
    'requireSso', false
  )
WHERE id = 'workspace-uuid';
```

## Deployment Guide

### Migration Steps

1. **Database Migration**:
   ```bash
   npm run migration:run
   ```

2. **Environment Variables**:
   - Add WorkOS environment variables to production
   - Verify configuration with `getWorkOSConfig()`

3. **Nginx Configuration**:
   - Update nginx configuration
   - Test proxy routing to backend

4. **Frontend Build**:
   - Build frontend with SSO components
   - Verify route configuration

5. **WorkOS Configuration**:
   - Configure production redirect URIs
   - Set up webhook endpoints
   - Test with identity providers

### Health Check

Verify SSO functionality:

```bash
# Check domain SSO availability
curl -X GET "https://your-domain.com/api/auth/sso/check-domain/company.com"

# Test SSO initiation (should return auth URL)
curl -X POST "https://your-domain.com/api/auth/sso/init" \
  -H "Content-Type: application/json" \
  -d '{"workspaceId": "workspace-id"}'
```

### Monitoring

Key metrics to monitor:
- SSO login attempts and success rate
- Domain check requests
- Webhook processing status
- Error rates by SSO provider
- User provisioning success rate

## Troubleshooting

### Common Issues

#### 1. "SSO not enabled for this workspace"
**Cause**: Workspace SSO configuration missing
**Solution**: 
```sql
-- Check workspace SSO config
SELECT id, name, sso_enabled, sso_config FROM workspace WHERE id = 'workspace-id';

-- Enable SSO if needed
UPDATE workspace SET sso_enabled = true WHERE id = 'workspace-id';
```

#### 2. "Domain not allowed for SSO access"
**Cause**: Email domain not in allowed domains list
**Solution**:
```sql
-- Add domain to allowed list
UPDATE workspace 
SET sso_config = jsonb_set(
  sso_config, 
  '{allowedDomains}', 
  (sso_config->'allowedDomains') || '["newdomain.com"]'::jsonb
) 
WHERE id = 'workspace-id';
```

#### 3. "Invalid webhook signature"
**Cause**: Webhook secret mismatch
**Solution**:
- Verify `WORKOS_WEBHOOK_SECRET` matches WorkOS dashboard
- Check webhook URL in WorkOS dashboard
- Ensure payload is not modified by proxy

#### 4. SSO button not appearing
**Cause**: Domain check failing
**Solution**:
- Check browser network tab for domain check requests
- Verify API endpoint is reachable
- Check rate limiting logs

#### 5. Callback redirect loops
**Cause**: JWT state parameter issues
**Solution**:
- Check JWT_SECRET configuration
- Verify state parameter expiry (10 minutes)
- Check browser cookies and storage

### Debug Mode

Enable debug logging:

```typescript
// In SsoService
private readonly logger = new Logger(SsoService.name);

// Add detailed logging
this.logger.debug(`SSO initiation for workspace: ${workspaceId}`);
this.logger.debug(`WorkOS profile received: ${JSON.stringify(profile)}`);
```

### Log Analysis

Key log patterns to monitor:

```bash
# SSO events
grep "SSO_EVENT" application.log

# Authentication failures  
grep "SSO authentication failed" application.log

# Webhook processing
grep "WorkOS webhook" application.log

# Domain checks
grep "SSO domain check" application.log
```

## Extension Guide

### Adding New Identity Providers

1. **WorkOS Configuration**:
   - Add new connection in WorkOS dashboard
   - Configure SAML/OIDC settings
   - Test connection

2. **Backend Updates** (usually no changes needed):
   - WorkOS handles provider abstraction
   - Profile mapping is standardized

3. **Frontend Updates** (optional):
   - Add provider-specific UI elements
   - Custom branding or messaging

### Multi-Domain Support

**Current**: Single domain per workspace
**Extension**: Multiple domains per workspace

```typescript
// Enhanced domain checking
async checkMultipleDomains(domains: string[]): Promise<SSOAvailability[]> {
  return Promise.all(
    domains.map(domain => this.checkDomainSSO(domain))
  );
}
```

### Custom User Provisioning

**Current**: Standard role assignment
**Extension**: Custom provisioning logic

```typescript
// Extended provisioning service
@Injectable()
export class CustomProvisioningService {
  async provisionUser(profile: WorkOSProfile, workspace: Workspace): Promise<User> {
    // Custom logic based on profile attributes
    // - Department-based role assignment
    // - Custom user attributes
    // - Integration with HR systems
  }
}
```

### Advanced Workspace Configuration

**Current**: Basic SSO configuration
**Extension**: Advanced settings

```typescript
interface EnhancedSSOConfig {
  allowedDomains?: string[];
  defaultRoleId?: string;
  autoProvision?: boolean;
  workosOrganizationId?: string;
  requireSso?: boolean;
  
  // New fields
  sessionTimeout?: number;
  forceMFA?: boolean;
  allowedProviders?: string[];
  customAttributes?: Record<string, any>;
  provisioningRules?: ProvisioningRule[];
}
```

### Integration with External Systems

#### SCIM (System for Cross-domain Identity Management)
```typescript
@Injectable()
export class SCIMService {
  async syncUsers(organizationId: string): Promise<void> {
    // Sync users between WorkOS and internal systems
  }
  
  async handleUserUpdate(scimEvent: SCIMEvent): Promise<void> {
    // Handle SCIM provisioning events
  }
}
```

#### Audit Logging
```typescript
@Injectable()
export class SSOAuditService {
  async logSSOEvent(event: SSOAuditEvent): Promise<void> {
    // Enhanced audit logging for compliance
    // - User login/logout events
    // - Permission changes
    // - Configuration updates
  }
}
```

### Performance Optimizations

#### Caching Strategies
```typescript
// Redis-based SSO configuration caching
@Injectable()
export class SSOCacheService {
  async getWorkspaceSSO(workspaceId: string): Promise<WorkspaceSSO | null> {
    const cacheKey = `sso:workspace:${workspaceId}`;
    // Check Redis cache first
    // Fall back to database if not found
  }
}
```

#### Batch Operations
```typescript
// Batch user provisioning
async provisionMultipleUsers(profiles: WorkOSProfile[]): Promise<User[]> {
  // Bulk user creation for better performance
  // Transaction-based provisioning
}
```

### Security Enhancements

#### Just-In-Time (JIT) Provisioning
```typescript
interface JITProvisioningConfig {
  enabled: boolean;
  createUsers: boolean;
  updateUsers: boolean;
  attributeMapping: Record<string, string>;
  requiredAttributes: string[];
}
```

#### Session Security
```typescript
// Enhanced session validation
async validateSSOSession(userId: string): Promise<SessionValidation> {
  return {
    isValid: boolean;
    expiresAt: Date;
    requiresReauth: boolean;
    securityLevel: 'low' | 'medium' | 'high';
  };
}
```

### API Extensions

#### GraphQL Support
```typescript
@Resolver(() => SSOProvider)
export class SSOResolver {
  @Query(() => [SSOProvider])
  async workspaceSSOProviders(@Args('workspaceId') workspaceId: string) {
    return this.ssoService.getWorkspaceProviders(workspaceId);
  }
  
  @Mutation(() => SSOLoginResponse)
  async initiateSSOLogin(@Args('input') input: InitiateSSOInput) {
    return this.ssoService.initiateSSOLogin(input.workspaceId, input.redirectUrl);
  }
}
```

#### Webhook Extensions
```typescript
// Support for additional webhook events
async handleExtendedWebhooks(event: WorkOSWebhookEvent): Promise<void> {
  switch (event.type) {
    case 'connection.activated':
      await this.handleConnectionActivated(event.data);
      break;
    case 'connection.deactivated':
      await this.handleConnectionDeactivated(event.data);
      break;
    case 'organization.updated':
      await this.handleOrganizationUpdated(event.data);
      break;
  }
}
```

## Conclusion

This SSO implementation provides a robust, secure, and scalable authentication solution that integrates seamlessly with the existing Glacier Climate Assistant application. The modular design allows for easy extension and customization to meet future requirements while maintaining security best practices and excellent user experience.

For additional support or questions about the implementation, refer to the troubleshooting section or contact the development team. 