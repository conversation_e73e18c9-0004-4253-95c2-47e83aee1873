server {
    listen 80;
    server_name app.glacier.eco;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://$host$request_uri;
    }

}
server {
    listen 443 ssl;
    server_name app.glacier.eco;
    client_max_body_size 100M;

    ssl_certificate /etc/letsencrypt/live/app.glacier.eco/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/app.glacier.eco/privkey.pem;

    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    location /api/queues/ {
        rewrite ^/api/queues/(.*)$ /api/queues/$1 break;
        proxy_pass http://backend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    # SSO callback handling - specific route for better control
    location /api/auth/sso/ {
        proxy_pass http://backend:3000/auth/sso/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Important for SSO redirects
        proxy_set_header X-Original-URI $request_uri;
        proxy_redirect off;
        
        # Cookie handling
        proxy_pass_header Set-Cookie;
        proxy_pass_header Cookie;
        
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        
        proxy_read_timeout 180;
        proxy_send_timeout 180;
    }

    location /api/ {
        # Enable gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;

        proxy_pass http://backend:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        # These 2 are needed so that SSE works
        # https://serverfault.com/questions/801628/for-server-sent-events-sse-what-nginx-proxy-configuration-is-appropriate
        proxy_http_version 1.1;
        proxy_set_header Connection "";

        proxy_read_timeout 180;
        proxy_send_timeout 180;
    }

    location /pgadmin/ {
        proxy_pass http://pgadmin:80/pgadmin/;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_redirect off;
    }

    location /grafana/ {
        proxy_pass http://grafana:3000/grafana/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }

    location / {
        proxy_pass http://frontend:5000;
    }
}