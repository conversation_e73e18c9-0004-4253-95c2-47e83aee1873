BACKEND_DB_HOST=backend-db
BACKEND_DB_PORT=5432
BACKEND_DB_NAME='glacier'
BACKEND_DB_USER='root'
BACKEND_DB_PASSWORD='root'

JWT_SECRET='your-secret-jwt-key-here'

REDIS_HOST=backend-redis

PGADMIN_DEFAULT_PASSWORD=admin
POSTMARK_SERVER_TOKEN='your-postmark-server-token'

CORS_ORIGINS=*

HELICONE_AUTH_API_KEY=sk-helicone-eu-...
AZURE_OPENAI_API_KEY=5cH...
OPENAI_API_KEY=sk-proj-sWs..

AZURE_RESOURCE=https://simon-m2vo32nd-swedencentral.openai.azure.com/

GEMINI_API_KEY=your-gemini-api-key-here

GRAFANA_ROOT_URL=https://app.glacier.eco/grafana
GRAFANA_DEFAULT_PASSWORD=admin

# WorkOS SSO Configuration
WORKOS_API_KEY=wk_live_xxxxxxxxxxxx
WORKOS_CLIENT_ID=client_xxxxxxxxxxxx
WORKOS_REDIRECT_URI=https://app.glacier.eco/api/auth/sso/callback
WORKOS_WEBHOOK_SECRET=wh_secret_xxxxxxxxxxxx