import { Injectable } from '@nestjs/common';
import type { DocumentChunkGenerated } from 'src/types';
import * as XLSX from 'xlsx';
import { DocumentParsingUtils } from './utils';
import {
  TableDetectionService,
  TableRegion,
  TableData,
} from './table-detection.service';
import { MarkdownTableService } from './markdown-table.service';
import { ChunkProcessingService } from './chunk-processing.service';
import { TableAnalysisService } from './table-analysis.service';
import { WorkerLogger } from 'src/shared/logger.service';

@Injectable()
export class SpreadsheetProcessingService {
  private readonly logger = new WorkerLogger(SpreadsheetProcessingService.name);

  constructor(
    private readonly tableDetectionService: TableDetectionService,
    private readonly markdownTableService: MarkdownTableService,
    private readonly chunkProcessingService: ChunkProcessingService,
    private readonly tableAnalysisService: TableAnalysisService
  ) {}

  /**
   * Enhanced spreadsheet parsing with improved table detection for hidden rows/columns
   */
  async parseSpreadsheetToMarkdown(
    path: string,
    userId: string,
    workspaceId: string,
    maxTokens: number = 3000
  ): Promise<DocumentChunkGenerated[]> {
    this.logger.log(
      `Enhanced parsing of spreadsheet to markdown: ${path}, max tokens: ${maxTokens}`
    );

    let workbook: XLSX.WorkBook | null = null;
    const chunkDataArray: DocumentChunkGenerated[] = [];

    try {
      // Read the workbook with full options to preserve all formatting
      workbook = XLSX.readFile(path, {
        cellStyles: true,
        cellNF: true,
        cellDates: true,
        cellFormula: true,
        sheetStubs: true,
        sheetRows: 0,
      });

      this.logger.log(
        `Loaded workbook: ${path}, sheets: ${workbook.SheetNames.join(', ')}`
      );

      const sourceDocumentName = path.split('/').pop();
      let pageNumber = 0;
      let globalChunkNumber = 1; // Track chunk number continuously across all tables

      for (const sheetName of workbook.SheetNames) {
        pageNumber++; // Increment page number for each sheet
        this.logger.log(
          `Processing sheet ${pageNumber}/${workbook.SheetNames.length}: "${sheetName}"`
        );

        const sheet = workbook.Sheets[sheetName];

        // 1. Detect hidden rows and columns
        const { hiddenRows, hiddenCols } =
          this.detectHiddenRowsAndColumns(sheet);
        this.logger.log(
          `Sheet "${sheetName}" has ${hiddenRows.size} hidden rows and ${hiddenCols.size} hidden columns`
        );

        // 2. Detect potential tables using available metadata
        const metadataTables = this.detectNativeTables(workbook, sheetName);

        // 3. Store original merged cells before unmerging
        const originalMerges = sheet['!merges'] ? [...sheet['!merges']] : [];

        // 4. Get the raw data with all cells
        const rawJsonData = XLSX.utils.sheet_to_json(sheet, {
          header: 1,
          defval: '', // Use empty string as default
          blankrows: true, // Keep blank rows
          raw: false, // Get formatted values
        }) as any[][];

        if (rawJsonData.length === 0) {
          this.logger.log(`Sheet "${sheetName}" is empty, skipping`);
          continue;
        }

        const processedJsonData = this.postProcessMergedCells(
          rawJsonData,
          originalMerges
        );

        // 5. Process data with and without hidden cells
        const dataWithHidden = this.normalizeSheetDataWithHiddenCells(
          processedJsonData,
          hiddenRows,
          hiddenCols,
          true // preserve hidden cells for analysis
        );

        // 6. Collect tables from various sources
        let tables: Array<{ headers: any[][]; rows: any[][] }> = [];

        // Process metadata-detected tables
        if (metadataTables.length > 0) {
          for (const tableInfo of metadataTables) {
            this.logger.log(
              `Processing metadata-detected table: ${tableInfo.name}`
            );

            // Extract the table based on its range
            const table = this.extractNativeTable(sheet, tableInfo.range);

            // Adjust for hidden rows/columns
            const adjustedTable = this.adjustTableForHiddenCells(
              table,
              tableInfo.range,
              hiddenRows,
              hiddenCols
            );

            tables.push(adjustedTable);
          }
        }

        // Fallback to our enhanced table detection if needed
        if (tables.length === 0) {
          this.logger.log(
            `No metadata tables found, using enhanced detection algorithms`
          );
          const detectedTables = await this.detectTablesInSheet(
            dataWithHidden,
            userId,
            workspaceId,
            originalMerges
          );
          tables.push(...detectedTables);
        }

        // Deduplicate tables
        tables = this.deduplicateTables(tables);

        this.logger.log(
          `Sheet "${sheetName}" has ${tables.length} tables after processing`
        );

        // 7. Convert tables to markdown and add to chunks
        let tableNumber = 1;
        for (const table of tables) {
          // Skip tables that are too small or empty
          if (table.rows.length === 0 || table.headers.length === 0) {
            this.logger.log(`Skipping empty table #${tableNumber}`);
            tableNumber++;
            continue;
          }

          // Validate table has actual content
          const hasValidHeader = table.headers.some((headerRow) =>
            headerRow.some(
              (cell) =>
                cell !== undefined &&
                cell !== null &&
                String(cell).trim() !== ''
            )
          );

          const hasValidData = table.rows.some((row) =>
            row.some(
              (cell) =>
                cell !== undefined &&
                cell !== null &&
                String(cell).trim() !== ''
            )
          );

          if (!hasValidHeader || !hasValidData) {
            this.logger.log(
              `Skipping table #${tableNumber} (invalid headers or data)`
            );
            tableNumber++;
            continue;
          }

          // Create markdown tables and split them if needed
          const markdownTables =
            this.markdownTableService.splitTableByTokenCount(
              table.headers,
              table.rows,
              maxTokens
            );

          this.logger.log(
            `Table #${tableNumber} split into ${markdownTables.length} chunks`
          );

          // Add each markdown table chunk
          for (const markdown of markdownTables) {
            // Skip empty markdown
            if (!markdown || markdown.trim() === '') continue;

            const chunk = this.chunkProcessingService.createTableChunk({
              text: markdown,
              sourceDocumentName: sourceDocumentName,
              sheetName,
              tableNumber,
              pageNumber,
              chunkNumber: globalChunkNumber++,
            });

            chunkDataArray.push(chunk);
          }

          tableNumber++;
        }
      }

      this.logger.log(
        `Enhanced spreadsheet parsing complete, generated ${chunkDataArray.length} chunks`
      );
      return chunkDataArray;
    } finally {
      // CLEANUP: Clear workbook data
      if (workbook) {
        // Clear all sheet data
        Object.keys(workbook.Sheets).forEach((sheetName) => {
          delete workbook.Sheets[sheetName];
        });
        workbook = null;
      }

      // Force garbage collection hint
      if (global.gc) {
        global.gc();
      }
    }
  }

  /**
   * Enhanced table detection algorithm with progressive consistency enforcement
   */
  private async detectTablesInSheet(
    data: any[][],
    userId: string,
    workspaceId: string,
    merges: any[] = []
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    this.logger.log(
      `Detecting tables in sheet: ${data.length} rows, ${merges.length} merged regions`
    );

    if (data.length === 0) return [];

    // Normalize the data
    const normalizedData = this.tableDetectionService.normalizeSheetData(data);

    // For very large sheets, use a progressive approach instead of trying to process all at once
    if (normalizedData.length > 300) {
      this.logger.log(
        `Large sheet detected (${normalizedData.length} rows), using progressive approach`
      );
      return this.detectTablesProgressively(
        normalizedData,
        userId,
        workspaceId,
        merges
      );
    }

    // Standard processing for smaller sheets
    this.logger.log(
      `Using standard table detection for sheet with ${normalizedData.length} rows`
    );
    const { emptyRows, emptyCols } =
      this.tableDetectionService.findEmptyRowsAndColsAdaptive(normalizedData);
    const regions = this.tableDetectionService.identifyTableRegionsEnhanced(
      normalizedData,
      emptyRows,
      emptyCols
    );

    // Convert regions to tables
    const tables = await this.regionsToTables(
      regions,
      userId,
      workspaceId,
      normalizedData
    );

    // CLEANUP: Clear regions array after processing
    regions.length = 0;

    this.logger.log(
      `Standard detection complete, found ${tables.length} tables`
    );
    return tables;
  }

  /**
   * Progressive table detection for large worksheets
   */
  private async detectTablesProgressively(
    data: any[][],
    userId: string,
    workspaceId: string,
    merges: any[] = []
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    this.logger.log(
      `Starting progressive table detection on ${data.length} rows`
    );

    const rowCount = data.length;
    const chunkSize = 150; // Process in reasonable chunks
    const overlap = 50; // Ensure chunks overlap to maintain consistency

    // Analyze the first chunk to detect the primary table pattern
    const firstChunk = data.slice(0, Math.min(chunkSize, rowCount));
    const { emptyRows: firstEmptyRows, emptyCols: firstEmptyCols } =
      this.tableDetectionService.findEmptyRowsAndColsAdaptive(firstChunk);

    // Detect column structure from the first chunk - this is critical for consistency
    const columnStructure = this.tableAnalysisService.analyzeColumnStructure(
      firstChunk,
      firstEmptyCols
    );

    // Strategy determination - based on first chunk analysis
    const analysisResult =
      this.tableDetectionService.analyzeSheetStructure(firstChunk);
    const isContinuousTable = analysisResult.continuousTableProbability > 0.7;
    this.logger.log(
      `Sheet analysis: continuous table probability: ${analysisResult.continuousTableProbability.toFixed(2)}, detected ${analysisResult.estimatedHeaderRows} header rows`
    );

    // If we detect a single continuous table structure, handle it specially
    if (isContinuousTable) {
      this.logger.log(
        `Detected continuous table structure, handling as single table`
      );
      return this.tableAnalysisService.handleContinuousTable(
        data,
        columnStructure,
        (data) =>
          this.tableDetectionService.detectHeaderRowsWithLLM(
            data,
            userId,
            workspaceId
          )
      );
    }

    // Process chunks with awareness of global structure
    const allRegions: Array<TableRegion & { confidence: number }> = [];

    // Process each chunk
    for (
      let startRow = 0;
      startRow < rowCount;
      startRow += chunkSize - overlap
    ) {
      const endRow = Math.min(startRow + chunkSize, rowCount);

      // Extract the current chunk
      const chunk = data.slice(startRow, endRow);

      // Process this chunk
      const { emptyRows, emptyCols } =
        this.tableDetectionService.findEmptyRowsAndColsAdaptive(chunk);

      // Force column structure consistency with first chunk when appropriate
      const consistentEmptyCols =
        this.tableAnalysisService.enforceColumnConsistency(
          emptyCols,
          columnStructure
        );

      // Detect regions in this chunk
      const regions = this.tableDetectionService.identifyTableRegionsEnhanced(
        chunk,
        emptyRows,
        consistentEmptyCols
      );

      // Adjust region positions to account for chunk offset
      const offsetRegions = regions.map((region) => ({
        ...region,
        startRow: region.startRow + startRow,
        confidence: this.tableAnalysisService.assessTableConfidenceAdaptive(
          this.tableDetectionService.extractRegionData(chunk, region),
          chunk.length,
          chunk[0].length
        ),
      }));

      // Add to our collection
      allRegions.push(...offsetRegions);
    }

    // Apply global consistency enforcement
    const cohesiveRegions = this.tableAnalysisService.enforceGlobalConsistency(
      allRegions,
      data
    );

    // CLEANUP: Clear large intermediate arrays
    allRegions.length = 0;

    this.logger.log(
      `Progressive detection complete, found ${cohesiveRegions.length} table regions`
    );
    const result = await this.regionsToTables(
      cohesiveRegions,
      userId,
      workspaceId,
      data
    );
    this.logger.log(`Converted regions to ${result.length} tables`);

    // CLEANUP: Clear cohesive regions after processing
    cohesiveRegions.length = 0;

    return result;
  }

  /**
   * Convert regions to tables
   */
  private async regionsToTables(
    regions: TableRegion[],
    userId: string,
    workspaceId: string,
    data: any[][]
  ): Promise<{ headers: any[][]; rows: any[][] }[]> {
    const tables: { headers: any[][]; rows: any[][] }[] = [];

    for (const region of regions) {
      // Extract region data
      const regionData = this.tableDetectionService.extractRegionData(
        data,
        region
      );

      // Skip regions that are too small
      if (regionData.length < 2 || regionData[0].length < 2) continue;

      // Analyze region data to detect headers
      const headerRowCount =
        await this.tableDetectionService.detectHeaderRowsWithLLM(
          regionData,
          userId,
          workspaceId
        );

      // Apply more robust header count determination
      const safeHeaderCount =
        this.tableDetectionService.calculateOptimalHeaderCount(
          headerRowCount,
          regionData
        );

      // Create the table
      tables.push({
        headers: regionData.slice(0, safeHeaderCount),
        rows: regionData.slice(safeHeaderCount),
      });
    }

    return tables;
  }

  /**
   * Detects hidden rows and columns in a worksheet
   */
  detectHiddenRowsAndColumns(sheet: XLSX.WorkSheet): {
    hiddenRows: Set<number>;
    hiddenCols: Set<number>;
  } {
    const hiddenRows = new Set<number>();
    const hiddenCols = new Set<number>();

    // Process hidden rows
    if (sheet['!rows']) {
      for (let r = 0; r < sheet['!rows'].length; r++) {
        if (sheet['!rows'][r] && sheet['!rows'][r].hidden) {
          hiddenRows.add(r);
        }
      }
    }

    // Process hidden columns
    if (sheet['!cols']) {
      for (let c = 0; c < sheet['!cols'].length; c++) {
        if (sheet['!cols'][c] && sheet['!cols'][c].hidden) {
          hiddenCols.add(c);
        }
      }
    }

    this.logger.log(
      `Detected ${hiddenRows.size} hidden rows and ${hiddenCols.size} hidden columns`
    );
    return { hiddenRows, hiddenCols };
  }

  /**
   * Normalize sheet data with awareness of hidden rows and columns
   */
  normalizeSheetDataWithHiddenCells(
    data: any[][],
    hiddenRows: Set<number>,
    hiddenCols: Set<number>,
    preserveHidden: boolean = false
  ): any[][] {
    this.logger.log(
      `Normalizing sheet data: ${data.length} rows, ${hiddenRows.size} hidden rows, ${hiddenCols.size} hidden columns`
    );

    if (data.length === 0) return [];

    const maxCols = Math.max(...data.map((row) => row.length));
    const normalizedData: any[][] = [];

    for (let r = 0; r < data.length; r++) {
      // Skip hidden rows if not preserving them
      if (!preserveHidden && hiddenRows.has(r)) continue;

      const row = data[r];
      const normalizedRow: any[] = [];

      for (let c = 0; c < maxCols; c++) {
        // Skip hidden columns if not preserving them
        if (!preserveHidden && hiddenCols.has(c)) continue;

        const value = c < row.length ? row[c] : '';
        normalizedRow.push(value !== undefined && value !== null ? value : '');
      }

      normalizedData.push(normalizedRow);
    }

    this.logger.log(
      `Sheet normalized to: ${normalizedData.length} rows x ${normalizedData[0]?.length || 0} columns`
    );
    return normalizedData;
  }

  /**
   * Detects potential tables in a workbook using available metadata
   */
  detectNativeTables(
    workbook: XLSX.WorkBook,
    sheetName: string
  ): Array<{
    name: string;
    ref: string;
    range: {
      s: { r: number; c: number };
      e: { r: number; c: number };
    };
  }> {
    this.logger.log(`Detecting native Excel tables in sheet: ${sheetName}`);

    const tables: Array<{
      name: string;
      ref: string;
      range: {
        s: { r: number; c: number };
        e: { r: number; c: number };
      };
    }> = [];

    // Check if the Workbook object exists
    if (workbook.Workbook) {
      // Look for tables in the Names collection as SheetJS doesn't expose Tables directly
      if (workbook.Workbook.Names) {
        for (const name of workbook.Workbook.Names) {
          // Check if this name is associated with the current sheet
          // and follows table naming patterns
          if (
            name.Sheet === workbook.SheetNames.indexOf(sheetName) &&
            (name.Name.startsWith('Table') || name.Name.includes('_Table'))
          ) {
            try {
              // Parse the reference to get the table range
              if (
                name.Ref &&
                name.Ref.includes('!') &&
                name.Ref.includes(':')
              ) {
                const range = DocumentParsingUtils.parseExcelReference(
                  name.Ref
                );
                if (range) {
                  tables.push({
                    name: name.Name,
                    ref: name.Ref,
                    range,
                  });
                }
              }
            } catch (error) {
              this.logger.warn(
                `Error parsing name reference: ${error.message}`
              );
            }
          }
        }
      }

      // Check for other types of named ranges that might be tables
      if (workbook.Workbook.Names) {
        for (const name of workbook.Workbook.Names) {
          // Look for data ranges (common pattern for tables)
          if (
            name.Sheet === workbook.SheetNames.indexOf(sheetName) &&
            (name.Name.endsWith('Data') || name.Name.includes('Range'))
          ) {
            try {
              if (
                name.Ref &&
                name.Ref.includes('!') &&
                name.Ref.includes(':')
              ) {
                const range = DocumentParsingUtils.parseExcelReference(
                  name.Ref
                );
                if (range) {
                  tables.push({
                    name: name.Name,
                    ref: name.Ref,
                    range,
                  });
                }
              }
            } catch (error) {
              this.logger.warn(
                `Error parsing data range reference: ${error.message}`
              );
            }
          }
        }
      }
    }

    // If we couldn't find tables through metadata, we can try to infer them
    // from the sheet content itself (looking for formatting patterns)
    if (tables.length === 0) {
      this.logger.log(
        `No native tables found in metadata, will rely on detection algorithms`
      );
    } else {
      this.logger.log(
        `Detected ${tables.length} potential tables from metadata in sheet: ${sheetName}`
      );
    }

    return tables;
  }

  /**
   * Alternative method to check for tables using sheet formatting clues
   */
  detectTablesFromFormatting(sheet: XLSX.WorkSheet): Array<{
    name: string;
    range: {
      s: { r: number; c: number };
      e: { r: number; c: number };
    };
  }> {
    const tables: Array<{
      name: string;
      range: {
        s: { r: number; c: number };
        e: { r: number; c: number };
      };
    }> = [];

    // Check if the sheet has defined names that look like table ranges
    const range = sheet['!ref'] ? XLSX.utils.decode_range(sheet['!ref']) : null;
    if (!range) return tables;

    // Look for distinct formatted regions that might be tables
    // This approach is simplified - in a full implementation, you'd analyze
    // cell formatting to identify table-like structures

    this.logger.log(`Attempting to detect tables from sheet formatting`);

    // For now, just return the entire sheet as a potential table
    // (real implementation would be more sophisticated)
    tables.push({
      name: 'DetectedTable',
      range: {
        s: { r: range.s.r, c: range.s.c },
        e: { r: range.e.r, c: range.e.c },
      },
    });

    return tables;
  }

  /**
   * Extracts data from a native Excel table
   */
  extractNativeTable(
    sheet: XLSX.WorkSheet,
    tableRange: {
      s: { r: number; c: number };
      e: { r: number; c: number };
    }
  ): { headers: any[][]; rows: any[][] } {
    const { s, e } = tableRange;

    // Extract all cells in the table range
    const tableData: any[][] = [];
    for (let r = s.r; r <= e.r; r++) {
      const row: any[] = [];
      for (let c = s.c; c <= e.c; c++) {
        const cellRef = XLSX.utils.encode_cell({ r, c });
        const cell = sheet[cellRef];
        row.push(cell ? cell.v : '');
      }
      tableData.push(row);
    }

    // Most Excel tables have a single header row, but we'll check for formatting clues
    // For now, use a reasonable default (typically 1 header row)
    const headerRowCount = 1;

    const headers = tableData.slice(0, headerRowCount);
    const rows = tableData.slice(headerRowCount);

    this.logger.log(
      `Native table extracted with ${headers.length} header rows and ${rows.length} data rows`
    );
    return { headers, rows };
  }

  /**
   * Enhanced unmerging of cells in worksheet
   */
  enhancedUnmergeCells(sheet: XLSX.WorkSheet): void {
    this.logger.log(`Enhanced unmerging of cells in worksheet`);

    const merges = sheet['!merges'];
    if (!merges) {
      this.logger.log('No merged cells found in worksheet');
      return;
    }

    this.logger.log(`Found ${merges.length} merged regions to process`);

    for (const merge of merges) {
      const cellAddress = XLSX.utils.encode_cell(merge.s);
      const cell = sheet[cellAddress];

      if (!cell) continue; // No value to propagate

      // Get the value and type of the starting cell
      const value = cell.v;
      const type = cell.t;
      const formula = cell.f; // Preserve formula if present

      // Calculate merge dimensions
      const rowSpan = merge.e.r - merge.s.r + 1;
      const colSpan = merge.e.c - merge.s.c + 1;

      // Loop over all cells in the merged range
      for (let r = merge.s.r; r <= merge.e.r; ++r) {
        for (let c = merge.s.c; c <= merge.e.c; ++c) {
          const address = { r, c };
          const cellRef = XLSX.utils.encode_cell(address);

          // For the first cell, add merge metadata with exact same pattern as original
          if (
            r === merge.s.r &&
            c === merge.s.c &&
            (rowSpan > 1 || colSpan > 1)
          ) {
            sheet[cellRef] = {
              v: `|<r${rowSpan}#c${colSpan}>${value}|`,
              t: type,
              f: formula,
            };
          } else {
            // For other cells in the merged range, use the same value
            sheet[cellRef] = {
              v: `lalal`,
              t: type,
            };
          }
        }
      }
    }

    // Remove the merges after processing
    delete sheet['!merges'];
    this.logger.log('Enhanced cell unmerging complete');
  }

  /**
   * Post-processes the table data to handle merged cells properly with precise tracking
   */
  postProcessMergedCells(data: any[][], originalMerges: any[]): any[][] {
    if (!originalMerges || originalMerges.length === 0 || data.length === 0) {
      return data;
    }

    this.logger.log(
      `Post-processing ${originalMerges.length} merged regions in ${data.length}x${data[0].length} data grid`
    );

    // Create a deep copy of the data
    const processedData = JSON.parse(JSON.stringify(data));

    // Get dimensions
    const rowCount = data.length;
    const colCount = Math.max(...data.map((row) => row.length));

    // Track each cell's merge status in a 2D grid
    type MergeInfo = {
      isMerged: boolean;
      isFirstCell: boolean;
      firstCellRow: number;
      firstCellCol: number;
      rowSpan: number;
      colSpan: number;
      value: any;
    };

    // Initialize merge map with safe dimensions
    const mergeMap: Array<Array<MergeInfo | null>> = [];
    for (let r = 0; r < rowCount; r++) {
      mergeMap[r] = [];
      for (let c = 0; c < colCount; c++) {
        mergeMap[r][c] = null;
      }
    }

    // Log merge boundaries for debugging
    this.logger.log(`Processing merges for data grid ${rowCount}x${colCount}`);

    // First, validate all merges are within bounds
    const validMerges = originalMerges.filter((merge) => {
      const isValid =
        merge.s.r >= 0 &&
        merge.s.r < rowCount &&
        merge.s.c >= 0 &&
        merge.s.c < colCount &&
        merge.e.r >= 0 &&
        merge.e.r < rowCount &&
        merge.e.c >= 0 &&
        merge.e.c < colCount;

      if (!isValid) {
        this.logger.warn(
          `Skipping out-of-bounds merge: (${merge.s.r},${merge.s.c}) to (${merge.e.r},${merge.e.c})`
        );
      }
      return isValid;
    });

    // Process each merge region
    for (let i = 0; i < validMerges.length; i++) {
      const merge = validMerges[i];
      const rowSpan = merge.e.r - merge.s.r + 1;
      const colSpan = merge.e.c - merge.s.c + 1;

      // Get value from the first cell
      const firstCellValue = data[merge.s.r]?.[merge.s.c] || '';

      this.logger.debug(
        `Merge #${i}: (${merge.s.r},${merge.s.c}) spans ${rowSpan}x${colSpan}, value: "${firstCellValue}"`
      );

      // Mark all cells in this merged region
      for (let r = merge.s.r; r <= merge.e.r; r++) {
        for (let c = merge.s.c; c <= merge.e.c; c++) {
          if (r < rowCount && c < colCount) {
            mergeMap[r][c] = {
              isMerged: true,
              isFirstCell: r === merge.s.r && c === merge.s.c,
              firstCellRow: merge.s.r,
              firstCellCol: merge.s.c,
              rowSpan,
              colSpan,
              value: firstCellValue,
            };
          }
        }
      }
    }

    // Second pass: Update the cell values based on merge status
    let firstCellsProcessed = 0;
    let secondaryCellsProcessed = 0;

    for (let r = 0; r < rowCount; r++) {
      for (let c = 0; c < colCount; c++) {
        const mergeInfo = mergeMap[r][c];

        // Skip cells not in any merge
        if (!mergeInfo) continue;

        if (r >= processedData.length) continue; // Safety check
        if (c >= (processedData[r]?.length || 0)) continue; // Safety check

        if (mergeInfo.isFirstCell) {
          // First cell of a merge gets special formatting
          if (mergeInfo.rowSpan > 1 || mergeInfo.colSpan > 1) {
            processedData[r][c] =
              `|<r${mergeInfo.rowSpan}#c${mergeInfo.colSpan}>${mergeInfo.value}|`;
            firstCellsProcessed++;
          }
        } else {
          // Secondary cells in a merge get <rm> tag
          processedData[r][c] = `<rm>${mergeInfo.value}</rm>`;
          secondaryCellsProcessed++;
        }
      }
    }

    this.logger.log(
      `Processed ${firstCellsProcessed} first cells and ${secondaryCellsProcessed} secondary cells`
    );

    // CLEANUP: Clear original data references
    originalMerges.length = 0;

    return processedData;
  }

  /**
   * Adjust table data to account for hidden rows and columns
   */
  adjustTableForHiddenCells(
    table: { headers: any[][]; rows: any[][] },
    range: { s: { r: number; c: number }; e: { r: number; c: number } },
    hiddenRows: Set<number>,
    hiddenCols: Set<number>
  ): { headers: any[][]; rows: any[][] } {
    // Create mapping from original indices to adjusted indices (after removing hidden cells)
    const rowMapping: number[] = [];
    const colMapping: number[] = [];

    let adjustedRowIdx = 0;
    for (let r = range.s.r; r <= range.e.r; r++) {
      rowMapping[r] = hiddenRows.has(r) ? -1 : adjustedRowIdx++;
    }

    let adjustedColIdx = 0;
    for (let c = range.s.c; c <= range.e.c; c++) {
      colMapping[c] = hiddenCols.has(c) ? -1 : adjustedColIdx++;
    }

    // Process headers (we need to map row/col indices and skip hidden cells)
    const adjustedHeaders: any[][] = [];
    for (
      let headerRowIdx = 0;
      headerRowIdx < table.headers.length;
      headerRowIdx++
    ) {
      const originalRow = range.s.r + headerRowIdx;
      if (rowMapping[originalRow] === -1) continue; // Skip hidden row

      const adjustedRow: any[] = [];
      for (let c = range.s.c; c <= range.e.c; c++) {
        if (colMapping[c] === -1) continue; // Skip hidden column

        const colOffset = c - range.s.c;
        const headerRow = table.headers[headerRowIdx];
        adjustedRow.push(
          colOffset < headerRow.length ? headerRow[colOffset] : ''
        );
      }

      adjustedHeaders.push(adjustedRow);
    }

    // Process data rows
    const adjustedRows: any[][] = [];
    for (let dataRowIdx = 0; dataRowIdx < table.rows.length; dataRowIdx++) {
      const originalRow = range.s.r + table.headers.length + dataRowIdx;
      if (rowMapping[originalRow] === -1) continue; // Skip hidden row

      const adjustedRow: any[] = [];
      for (let c = range.s.c; c <= range.e.c; c++) {
        if (colMapping[c] === -1) continue; // Skip hidden column

        const colOffset = c - range.s.c;
        const dataRow = table.rows[dataRowIdx];
        adjustedRow.push(colOffset < dataRow.length ? dataRow[colOffset] : '');
      }

      adjustedRows.push(adjustedRow);
    }

    return { headers: adjustedHeaders, rows: adjustedRows };
  }

  /**
   * Deduplicate tables that might be detected multiple ways
   */
  deduplicateTables(
    tables: Array<{ headers: any[][]; rows: any[][] }>
  ): Array<{ headers: any[][]; rows: any[][] }> {
    if (tables.length <= 1) return tables;

    const unique: Array<{ headers: any[][]; rows: any[][] }> = [];
    const seen = new Set<string>();

    // Create signatures for tables to identify duplicates
    for (const table of tables) {
      if (table.headers.length === 0 || table.rows.length === 0) continue;

      // Create a simple signature based on header content and table dimensions
      const headerSample = table.headers[0]
        .slice(0, 3)
        .map((h) => String(h || ''))
        .join('|');
      const dimensions = `${table.headers.length}:${table.rows.length}:${table.headers[0].length}`;
      const signature = `${headerSample}:${dimensions}`;

      if (!seen.has(signature)) {
        seen.add(signature);
        unique.push(table);
      }
    }

    return unique;
  }
}
