import {
  AlertTriangleIcon,
  CircleCheckBigIcon,
  CheckCircleIcon,
  CircleIcon,
  HistoryIcon,
  LoaderCircle,
  SaveIcon,
  WandSparklesIcon,
} from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';

import { Label } from '../ui/label';
import { Button } from '../ui/button';
import { TipTapEditor } from '../ui/tiptap/tiptap-editor';
import IconRenderer from '../ui/icons';
import { StatusSelector } from '../ui/statusSelector';
import { useToast } from '../ui/use-toast';
import { MemberSelector } from './MemberSelector';

import { DocumentLinks } from './DocumentLinksModal';
import { CommentSection } from './Comments';
import { AiGenerateDatapointConfirmModal } from './AiGenerateDpConfirmModal';
import { GeneratedContent } from './GeneratedContent';
import EsrsInfo from './EsrsInfo';
import { CommentGenerationSection } from './super-admin-tools/CommentGenerations';
import {
  MATERIAL_TOPIC_ORDER,
  MaterialTopicList,
  MaterialTopicsType,
} from './MaterialTopicList';
import { VersionHistoryItem, VersionHistoryModal } from './VersionHistoryModal';

import { CommentType, DatapointRequestStatus } from '@/types';
import { useDatapointRequest } from '@/hooks/useDatapointRequest';
import {
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { DatapointRequestData, EventType } from '@/types/project';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { loadMaterialTopics } from '@/api/datapoint/datapoint-request.api';
import {
  getVersionHistory,
  restoreVersion,
} from '@/api/workspace-settings/useWorkspaceSettings';
import { IUser } from '@/types/user';
import { usePermissions } from '@/context/permissionsContext';

export function DatapointRequestItem({
  datapointRequest,
  projectId,
  members,
}: {
  datapointRequest: DatapointRequestData;
  projectId: string;
  members: Pick<IUser, 'id' | 'name'>[];
}) {
  const { toast } = useToast();
  const [versionHistory, setVersionHistory] = useState<
    VersionHistoryItem[] | null
  >(null);
  const [showVersionHistory, setShowVersionHistory] = useState(false);

  const {
    content,
    status,
    esrsDatapoint,
    datapointGenerations,
    isDirty,
    isLoadingReviewWithAi,
    isLoadingGenerateWithAi,
    handleInputChange,
    handleReportDatapointStatusChange,
    handleSave,
    canReviewWithAi,
    handleReviewWithAi,
    canGenerateWithAi,
    handleGenerateWithAi,
    confirmAiDialogOpen,
    setConfirmAiDialogOpen,
    handleUpdateDatapointGenerationStatus,
    updateResponsiblePerson,
    refetchDataRequest,
    contentVersion,
  } = useDatapointRequest({ datapointRequest });
  const { userPermissions } = usePermissions();

  const isSuperAdminUser = useMemo(
    () => userPermissions.hasSuperAdminPermission,
    [userPermissions.hasSuperAdminPermission]
  );

  const [materialTopics, setMaterialTopics] = useState<MaterialTopicsType[]>(
    []
  );
  const [isExpanded, setIsExpanded] = useState(false);

  const isComplete = useMemo(() => {
    return status === DatapointRequestStatus.CompleteData;
  }, [status]);

  useEffect(() => {
    if (!materialTopics.length && isExpanded && isSuperAdminUser) {
      loadMaterialTopics({ datapointRequestId: datapointRequest.id })
        .then((data) => {
          const sordedMaterialTopics = data.sort(
            (a: MaterialTopicsType, b: MaterialTopicsType) => {
              return (
                MATERIAL_TOPIC_ORDER.indexOf(a.level) -
                MATERIAL_TOPIC_ORDER.indexOf(b.level)
              );
            }
          );
          setMaterialTopics(sordedMaterialTopics);
        })
        .catch((err) => {
          console.log(err);
        });
    }
  }, [isExpanded]);

  const { allow: userCanReviewWithAi, tooltip: canReviewWithAiTooltipText } =
    canReviewWithAi();
  const {
    allow: userCanGenerateWithAi,
    tooltip: canGenerateWithAiTooltipText,
  } = canGenerateWithAi();

  const handleVersionHistoryClick = async () => {
    try {
      const history: VersionHistoryItem[] = await getVersionHistory(
        datapointRequest.id
      );
      setVersionHistory(history);
      setShowVersionHistory(true);
    } catch (error) {
      console.error('Failed to fetch version history:', error);
    }
  };

  const handleRestoreVersion = async (versionId: string) => {
    try {
      await restoreVersion(versionId);
      toast({
        variant: 'success',
        title: 'Version restored successfully',
        description: 'The version has been restored successfully.',
      });
    } catch (error) {
      console.error('Failed to restore version:', error);
      toast({
        title: 'Error restoring version',
        description: 'An error occurred while restoring the version.',
        variant: 'destructive',
      });
    } finally {
      setShowVersionHistory(false);
      refetchDataRequest();
    }
  };

  return (
    <AccordionItem
      className="bg-slate-50 px-5 py-2 rounded-lg"
      key={datapointRequest.id}
      value={datapointRequest.id}
    >
      <AccordionTrigger onClick={() => setIsExpanded(!isExpanded)}>
        <div className="space-y-4 text-left" style={{ userSelect: 'text' }}>
          <span className="tracking-wide">
            <span className="font-normal">{esrsDatapoint.datapointId}: </span>
            <span className="">{esrsDatapoint.name}</span>
          </span>
          <div className="flex justify-start items-center gap-6">
            <div className="flex items-center space-x-2">
              <DocumentLinks
                documentChunkCount={
                  datapointRequest.documentChunkCount as number
                }
                datapointRequestId={datapointRequest.id}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Label>Status: </Label>
              <StatusSelector
                currentStatus={status}
                statuses={datapointStatuses}
                onStatusChange={handleReportDatapointStatusChange}
                disabled={!userPermissions.canApproveDp}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Label>Responsible: </Label>
              <MemberSelector
                members={members}
                action={updateResponsiblePerson}
                currentUser={datapointRequest.responsiblePerson?.id}
                placeholder="Select User"
                disabled={!userPermissions.canAssignDp}
              />
            </div>

            <div className="flex items-center space-x-2">
              {/*Removed for now*/}
              {false && (
                <Button
                  onClick={(e) => e.stopPropagation()}
                  variant="link"
                  size="xs"
                  className="underline"
                >
                  Explain Datapoint
                </Button>
              )}
            </div>
          </div>
        </div>
      </AccordionTrigger>
      <AccordionContent>
        {isSuperAdminUser && (
          <div className="text-md text-red-500">
            <h1>ADMIN ONLY</h1>
            <p>Datapoint-Request ID: {datapointRequest.id}</p>
            <p>
              Datatype:{' '}
              {JSON.stringify(datapointRequest.esrsDatapoint.dataType)}
            </p>
          </div>
        )}
        <div className="flex flex-col gap-1 mt-2">
          <span className="font-semibold">Text for Datapoint</span>
          <div className="relative">
            <TipTapEditor
              isEditable={!isComplete}
              content={content}
              refId={datapointRequest.id}
              setContent={(newValue) => handleInputChange(newValue)}
            />
            {userPermissions.canViewVersionHistory && (
              <Button
                variant={'link'}
                className="p-0 absolute right-4 bottom-4 text-foreground text-sm font-normal underline"
                onClick={handleVersionHistoryClick}
              >
                <HistoryIcon className="h-4 w-4 mr-2" />
                Version History
              </Button>
            )}
          </div>
          <div className="mt-3">
            <Tooltip>
              <TooltipTrigger>
                <Button
                  onClick={() => handleSave(datapointRequest.id)}
                  variant={'default'}
                  disabled={!userPermissions.canEditDp || !isDirty || isComplete}
                >
                  <SaveIcon className="h-4 w-4 mr-2" />
                  Save
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {isDirty ? 'Save changes' : 'No changes to save'}
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant={'outline'}
                  disabled={!userCanGenerateWithAi || isComplete}
                  onClick={() => setConfirmAiDialogOpen(true)}
                  className="ml-2"
                >
                  {isLoadingGenerateWithAi ? (
                    <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <IconRenderer
                      iconName={'HammerSparcle'}
                      className="h-4 w-4 mr-2"
                    />
                  )}
                  Generate with AI
                </Button>
              </TooltipTrigger>
              <TooltipContent>{canGenerateWithAiTooltipText}</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger>
                <Button
                  variant="outline"
                  className="ml-2"
                  onClick={() => handleReviewWithAi()}
                  disabled={!userCanReviewWithAi || isComplete}
                >
                  {isLoadingReviewWithAi ? (
                    <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <WandSparklesIcon className="h-4 w-4 mr-2" />
                  )}
                  Review with AI
                </Button>
              </TooltipTrigger>
              <TooltipContent>{canReviewWithAiTooltipText}</TooltipContent>
            </Tooltip>
            {!isComplete && (
              <Tooltip>
                <TooltipTrigger>
                  <Button
                    variant={'forest'}
                    className={'ml-2'}
                    onClick={() => {
                      handleReportDatapointStatusChange(
                        DatapointRequestStatus.CompleteData
                      );
                    }}
                    disabled={!userPermissions.canApproveDp}
                  >
                    <CircleCheckBigIcon className="h-4 w-4 mr-2" />
                    Approve Text
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {'Approve the text for this datapoint request'}
                </TooltipContent>
              </Tooltip>
            )}
          </div>
          <div className="mt-4">
            <EsrsInfo esrsDatapoint={[esrsDatapoint]} source="datapoint" />
          </div>
          {isSuperAdminUser && materialTopics.length > 0 && (
            <div className="mt-4">
              <span className="underline">Material Topics</span>
              <ul>
                {materialTopics?.map((topic) => (
                  <MaterialTopicList key={topic.id} topic={topic} />
                ))}
              </ul>
            </div>
          )}
          {isSuperAdminUser && datapointGenerations.length > 0 && (
            <GeneratedContent
              generationContents={datapointGenerations}
              generationType={EventType.DatapointRequest}
              handleApproveOrReject={handleUpdateDatapointGenerationStatus}
            />
          )}
          {userPermissions.canUpdateAiGenerationStatusOnDp && (
            <CommentGenerationSection
              projectId={projectId}
              savedComments={datapointRequest.commentGenerations}
              updateCallback={refetchDataRequest}
            />
          )}
          <CommentSection
            savedComments={datapointRequest.comments}
            projectId={projectId}
            commentableId={datapointRequest.id}
            commentableType={CommentType.DatapointRequest}
            updateCallback={refetchDataRequest}
          />
        </div>
        <AiGenerateDatapointConfirmModal
          open={confirmAiDialogOpen}
          setOpen={setConfirmAiDialogOpen}
          callback={handleGenerateWithAi}
          datapointRequest={datapointRequest}
        />
      </AccordionContent>
      {versionHistory && (
        <VersionHistoryModal
          open={showVersionHistory}
          onOpenChange={setShowVersionHistory}
          versionHistory={versionHistory}
          users={members}
          contentVersion={contentVersion}
          handleRestoreVersion={handleRestoreVersion}
        />
      )}
    </AccordionItem>
  );
}

const datapointStatuses: {
  value: DatapointRequestStatus;
  label: string;
  icon: React.FC<any>;
  color: string;
}[] = [
  {
    value: DatapointRequestStatus.NotReported,
    label: 'DP Not Reported',
    icon: CircleIcon,
    color: '#718096', // Darker gray for "Not Reported"
  },
  {
    value: DatapointRequestStatus.IncompleteData,
    label: 'DP Incomplete',
    icon: AlertTriangleIcon,
    color: '#D69E2E', // Darker yellow for "Incomplete Data"
  },
  {
    value: DatapointRequestStatus.CompleteData,
    label: 'DP Complete',
    icon: CheckCircleIcon,
    color: '#3182CE', // Darker blue for "Complete Data"
  },
];
