import { Module } from '@nestjs/common';
import { UsersService } from './users.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from './entities/user.entity';
import { UserPromptContext } from './entities/user-prompt-context.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { UserWorkspace } from './entities/user-workspace.entity';
import { Token } from './entities/token.entity';
import { UsersController } from './users.controller';
import { LlmService } from '../llm/services/llm.service';
import { PerplexityService } from '../util/perplexity.service';
import { EmailService } from '../external/email.service';
import { Company } from '../workspace/entities/company.entity';
import { WorkspaceSSO } from '../workspace/entities/workspace-sso.entity';
import { EmailModule } from 'src/external/email.module';
import { WorkspaceService } from 'src/workspace/workspace.service';
import { WorkspaceModule } from 'src/workspace/workspace.module';
import { Role } from './entities/role.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      UserPromptContext,
      Token,
      Company,
      Workspace,
      WorkspaceSSO,
      UserWorkspace,
      Role,
    ]),
    EmailModule,
    WorkspaceModule,
  ],
  providers: [
    UsersService,
    LlmService,
    PerplexityService,
    EmailService,
    WorkspaceService,
  ],
  exports: [UsersService, TypeOrmModule],
  controllers: [UsersController],
})
export class UsersModule {}
