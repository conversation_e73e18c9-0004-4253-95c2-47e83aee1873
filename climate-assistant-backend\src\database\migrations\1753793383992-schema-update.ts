import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration: SSO (Single Sign-On) Implementation Schema Update
 *
 * This migration adds the necessary database schema changes to support SSO authentication
 * alongside the existing password-based authentication system.
 *
 * Changes Applied:
 *
 * User Table Modifications:
 * - authMethod: Specifies the authentication method used ('password', 'sso', etc.)
 * - ssoProviderId: Identifier for the SSO provider (e.g., 'google', 'azure', 'okta')
 * - ssoUserId: The unique user identifier from the SSO provider
 * - lastSsoLogin: Timestamp tracking the user's last SSO login attempt
 *
 * Workspace Table Modifications:
 * - ssoEnabled: Boolean flag indicating if SSO is enabled for the workspace
 * - ssoConfig: JSON configuration object storing SSO provider settings and metadata
 *
 * Data Migration:
 * - All existing users are set to 'password' authentication method by default
 * - Existing workspaces have SSO disabled by default
 *
 */

export class SchemaUpdate1753793383992 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add SSO fields to user table
    await queryRunner.query(
      `ALTER TABLE "user" ADD COLUMN "authMethod" character varying`
    );

    await queryRunner.query(
      `UPDATE "user" SET "authMethod" = 'password' WHERE "authMethod" IS NULL`
    );

    await queryRunner.query(
      `ALTER TABLE "user" ADD COLUMN "ssoProviderId" character varying`
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD COLUMN "ssoUserId" character varying`
    );
    await queryRunner.query(
      `ALTER TABLE "user" ADD COLUMN "lastSsoLogin" TIMESTAMP`
    );

    // Add SSO fields to workspace table
    await queryRunner.query(
      `ALTER TABLE "workspace" ADD COLUMN "ssoEnabled" boolean NOT NULL DEFAULT false`
    );
    await queryRunner.query(
      `ALTER TABLE "workspace" ADD COLUMN "ssoConfig" json`
    );

    // Create index for SSO user lookup
    await queryRunner.query(
      `CREATE INDEX "IDX_user_sso_provider_user" ON "user" ("ssoProviderId", "ssoUserId") WHERE "ssoProviderId" IS NOT NULL AND "ssoUserId" IS NOT NULL`
    );

    // Create index for email lookup (already exists but ensuring it's there for SSO)
    await queryRunner.query(
      `CREATE INDEX IF NOT EXISTS "IDX_user_email" ON "user" ("email")`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop indexes
    await queryRunner.query(
      `DROP INDEX IF EXISTS "IDX_user_sso_provider_user"`
    );
    await queryRunner.query(`DROP INDEX IF EXISTS "IDX_user_email"`);

    // Remove SSO fields from workspace table
    await queryRunner.query(`ALTER TABLE "workspace" DROP COLUMN "ssoConfig"`);
    await queryRunner.query(`ALTER TABLE "workspace" DROP COLUMN "ssoEnabled"`);

    // Remove SSO fields from user table
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "lastSsoLogin"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "ssoUserId"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "ssoProviderId"`);
    await queryRunner.query(`ALTER TABLE "user" DROP COLUMN "authMethod"`);
  }
}
