import {
  IsEmail,
  Is<PERSON>num,
  IsNotEmpty,
  Is<PERSON><PERSON>al,
  IsString,
  IsU<PERSON><PERSON>,
  MinLength,
  IsUrl,
  IsBoolean,
  IsObject,
} from 'class-validator';
import { USER_ROLES } from 'src/constants';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @IsEmail({}, { message: 'Keine gültige Email' })
  email: string;

  @IsNotEmpty({ message: 'Passwort darf nicht leer sein' })
  password: string;
}

export interface LoginResponseSuccess {
  message: string;
}

export interface LogoutResponseSuccess {
  message: string;
}

export class RegisterWithCompanyDto {
  @IsEmail({}, { message: 'Email Id is not valid' })
  email: string;

  @IsNotEmpty({ message: 'Password cannot be empty' })
  password: string;

  @IsString()
  @IsNotEmpty({ message: 'Company name cannot be empty' })
  companyName: string;

  @IsString()
  @IsOptional()
  projectType: string;

  @IsEnum(USER_ROLES, { message: 'Role must be a valid enum value' })
  @IsOptional()
  role: USER_ROLES;
}

export class PasswordResetRequestDto {
  @IsUUID('4', { message: 'Invalid user ID format' })
  @IsNotEmpty({ message: 'User ID cannot be empty' })
  userId: string;
}
export class CreateUserWithCompanyAndWorkspaceDto {
  @IsEmail({}, { message: 'Invalid email address' })
  email: string;

  @IsOptional()
  @MinLength(6, { message: 'Password must be at least 6 characters long' })
  password?: string;

  @IsString()
  @IsNotEmpty({ message: 'Company name cannot be empty' })
  companyName: string;

  @IsOptional()
  @IsObject()
  ssoConfig?: {
    enabled: boolean;
    domain: string;
    protocol: 'saml' | 'oauth' | 'oidc';
    workosOrganizationId: string;
    workosConnectionId: string;
  };
}

// SSO DTOs
export class InitiateSSOLoginDto {
  @ApiProperty({
    description: 'Workspace ID for SSO authentication',
    example: 'uuid-workspace-id',
  })
  @IsUUID('4', { message: 'Invalid workspace ID format' })
  @IsNotEmpty({ message: 'Workspace ID cannot be empty' })
  workspaceId: string;

  @ApiProperty({
    description: 'Optional redirect URL after successful authentication',
    example: 'https://app.glacier.eco/dashboard',
    required: false,
  })
  @IsUrl({}, { message: 'Invalid redirect URL format' })
  @IsOptional()
  redirectUrl?: string;
}

export class CheckDomainSSOResponseDto {
  @ApiProperty({
    description: 'Whether the domain has SSO configured',
    example: true,
  })
  hasSso: boolean;

  @ApiProperty({
    description: 'Workspace ID if SSO is available',
    example: 'uuid-workspace-id',
    required: false,
  })
  workspaceId?: string;
}

export class InitiateSSOResponseDto {
  @ApiProperty({
    description: 'WorkOS authorization URL to redirect user to',
    example: 'https://auth.workos.com/sso/authorize?...',
  })
  authorizationUrl: string;
}

export class LinkAccountToSSODto {
  @ApiProperty({
    description: 'SSO user ID from the identity provider',
    example: 'sso_user_xxxxxxxxxxxx',
  })
  @IsString()
  @IsNotEmpty({ message: 'SSO user ID cannot be empty' })
  ssoUserId: string;

  @ApiProperty({
    description: 'SSO provider/connection ID',
    example: 'conn_xxxxxxxxxxxx',
  })
  @IsString()
  @IsNotEmpty({ message: 'SSO provider ID cannot be empty' })
  ssoProviderId: string;
}

export class SSOProviderDto {
  @ApiProperty({
    description: 'Connection/provider ID',
    example: 'conn_xxxxxxxxxxxx',
  })
  id: string;

  @ApiProperty({
    description: 'Provider name (e.g., Google, Microsoft)',
    example: 'Google',
  })
  name: string;

  @ApiProperty({
    description: 'Provider type',
    example: 'saml',
  })
  type: string;
}

export class GetSSOProvidersResponseDto {
  @ApiProperty({
    description: 'List of available SSO providers',
    type: [SSOProviderDto],
  })
  providers: SSOProviderDto[];

  @ApiProperty({
    description: 'Whether SSO is enabled for the workspace',
    example: true,
  })
  ssoEnabled: boolean;
}
