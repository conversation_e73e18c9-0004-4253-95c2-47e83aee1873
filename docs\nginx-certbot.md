# NGINX & Certbot Management

This document outlines the steps and procedures for managing SSL certificates using Certbot along with NGINX in the Glacier Climate Assistant environment. Follow these instructions to check certificate status, renew expired certificates, and set up fresh certificates.

---

## Checking Certificate Status

To verify the current SSL certificate status on your server, run:

```bash
docker exec -it <nginx_or_certbot_container_name_or_id> certbot certificates
```

> **Note:** Ensure that port 80 (HTTP) is enabled (e.g., on Azure or your cloud provider) so that <PERSON><PERSON><PERSON> can complete the HTTP-01 challenge.

---

## Preparing for Certificate Renewal

After an SSL certificate expires, HTTPS requests will fail. To renew the certificate, you may need to temporarily simplify the NGINX configuration to only serve HTTP for the ACME challenge.

### Create a Minimal NGINX Configuration

Create or update the NGINX configuration (e.g., `./nginx/app.conf`) with the following basic settings:

```nginx
server {
    listen 80;
    server_name dev.glacier.eco;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://$host$request_uri;
    }
}
```

This configuration directs ACME challenge requests to the correct location and forces all other traffic to HTTPS once the certificate is renewed.

### Restart NGINX and Certbot

After updating the configuration, restart the related containers:

```bash
docker compose up -d nginx certbot
```

---

## Renewing an Expired Certificate Manually

If your certificate has expired, you can force a renewal by executing:

```bash
docker exec -it <nginx_or_certbot_container_name_or_id> certbot renew --force-renewal
```

This command will trigger Certbot to renew all managed certificates.

---

## Setting Up a Fresh Certificate

If you need to issue a new certificate from scratch, follow these steps:

1. **Access the Certbot Container:**

   ```bash
   docker compose exec certbot /bin/sh
   ```

2. **Run Certbot to Generate a New Certificate:**

   Within the Certbot container, execute:

   ```bash
   docker compose exec certbot certbot certonly --webroot \
      --webroot-path=/var/www/certbot \
      -d dev.glacier.eco \
      --email <EMAIL> \
      --agree-tos --no-eff-email
   ```

   Replace `your_domain.com` and `<EMAIL>` with your actual domain name and email address.

3. **Generate Strong DH Parameters:**

   Still inside the container (or from a host with OpenSSL installed), generate `ssl-dhparams.pem`:

   ```bash
   openssl dhparam -out /etc/letsencrypt/ssl-dhparams.pem 2048
   ```

4. **Update SSL Options for NGINX:**

   Download the recommended NGINX SSL configuration file and place it at `/etc/letsencrypt/options-ssl-nginx.conf`:

   ```bash
   curl -o /etc/letsencrypt/options-ssl-nginx.conf https://raw.githubusercontent.com/certbot/certbot/master/certbot-nginx/certbot_nginx/_internal/tls_configs/options-ssl-nginx.conf
   ```

5. **Restore NGINX to Its Full HTTPS Configuration:**

   Once the new certificate is in place, update your NGINX configuration to re-enable the HTTPS server block as needed.

6. **Reload NGINX:**

   Apply the new configuration without restarting the container:

   ```bash
   docker compose exec nginx nginx -s reload
   ```

---

## Summary

- **Certificate Status:** Check using `certbot certificates` inside the relevant container.
- **Renewal:** Use a minimal NGINX configuration to allow HTTP challenges, then renew with `certbot renew --force-renewal`.
- **Fresh Setup:** For a new certificate, use `certbot certonly` with the webroot method, generate strong DH parameters, update SSL configurations, and finally reload NGINX.

For any changes or updates, always ensure that your domain settings and Docker configuration align with your production environment requirements.
