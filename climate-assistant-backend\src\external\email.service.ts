import { Injectable } from '@nestjs/common';
import { User } from '../users/entities/user.entity';
import { PostmarkService } from './postmark.service';
import { WorkerLogger } from 'src/shared/logger.service';

const TEMPLATE_ALIAS = {
  RESET_PASSWORD: 'ai-reset-password',
  INVITE_USER: 'ai-invite-user',
};

@Injectable()
export class EmailService {
  constructor(private readonly postmarkService: PostmarkService) {}

  private readonly logger = new WorkerLogger(EmailService.name);

  async sendPasswordReset({
    email,
    userName,
    resetToken,
    origin,
  }: {
    email: string;
    userName: string;
    resetToken: string;
    origin: string;
  }): Promise<void> {
    this.logger.log(`Sending password reset email to: ${email}`);
    this.logger.log(`Reset token: ${resetToken}`);

    await this.postmarkService.sendTemplatedEmail({
      templateAlias: TEMPLATE_ALIAS.RESET_PASSWORD,
      to: email,
      templateModel: {
        name: userName,
        reset_link: `${origin}/reset-password?token=${resetToken}`,
      },
    });

    this.logger.log('Password reset email sent successfully.');
  }

  async inviteUser({
    token,
    invitingUser,
    email,
    origin,
    workspace,
    isSSO = false,
  }: {
    token: string;
    invitingUser: User;
    email: string;
    origin: string;
    workspace?: { name: string; ssoEnabled: boolean };
    isSSO?: boolean;
  }): Promise<void> {
    this.logger.log(`Sending invite user email to: ${email} (SSO: ${isSSO})`);

    let inviteLink: string;
    let templateModel: any;

    if (isSSO && workspace?.ssoEnabled) {
      // For SSO workspaces, direct users to SSO login
      const emailDomain = email.split('@')[1];
      inviteLink = `${origin}/sso-login?email=${encodeURIComponent(email)}&invited=true`;
      
      templateModel = {
        inviter_name: invitingUser.name || invitingUser.email,
        workspace_name: workspace.name,
        invite_link: inviteLink,
        is_sso: true,
        email_domain: emailDomain,
      };
    } else {
      // Traditional password-based invitation
      inviteLink = `${origin}/reset-password?token=${token}&invite=true`;
      
      templateModel = {
        inviter_name: invitingUser.name || invitingUser.email,
        workspace_name: workspace?.name || 'the workspace',
        invite_link: inviteLink,
        is_sso: false,
      };
    }

    await this.postmarkService.sendTemplatedEmail({
      templateAlias: TEMPLATE_ALIAS.INVITE_USER,
      to: email,
      templateModel,
    });

    this.logger.log('Email sent successfully.');
  }
}
