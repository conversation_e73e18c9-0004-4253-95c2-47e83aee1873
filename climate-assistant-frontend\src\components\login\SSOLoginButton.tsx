import React from 'react';
import { Button } from '@/components/ui/button';
import { Building2, LoaderCircle } from 'lucide-react';

interface SSOLoginButtonProps {
  workspaceId: string;
  onSSOLogin: (workspaceId: string) => void;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
}

export const SSOLoginButton: React.FC<SSOLoginButtonProps> = ({
  workspaceId,
  onSSOLogin,
  isLoading = false,
  disabled = false,
  className = '',
}) => {
  const handleClick = () => {
    if (!disabled && !isLoading) {
      onSSOLogin(workspaceId);
    }
  };

  return (
    <Button
      variant="outline"
      className={`w-full ${className}`}
      onClick={handleClick}
      disabled={disabled || isLoading}
      type="button"
    >
      {isLoading ? (
        <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <Building2 className="mr-2 h-4 w-4" />
      )}
      Continue with SSO
    </Button>
  );
};
