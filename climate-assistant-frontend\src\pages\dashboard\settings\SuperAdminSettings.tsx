import { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  LoaderCircle,
  PlusCircle,
  FileTextIcon,
  Shield,
  AlertCircle,
} from 'lucide-react';

import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { buttonVariants } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';
import {
  createWorkspace,
  createStarterProject,
} from '@/api/workspace-settings/workspace-settings.api';

import { useWorkspaces } from '@/hooks/useWorkspaces';
import { Language } from '@/types';
import { WorkspaceSwitcher } from '@/components/WorkspaceSwitcher';

const SuperAdminSettings = ({
  currentWorkspaceId,
}: {
  currentWorkspaceId: string;
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const { refetchWorkspaces } = useWorkspaces();
  const { toast } = useToast();

  // New state for workspace creation
  const [newWorkspaceName, setNewWorkspaceName] = useState('');
  const [newWorkspaceEmail, setNewWorkspaceEmail] = useState('');
  const [newWorkspacePassword, setNewWorkspacePassword] = useState('');

  // SSO Configuration State
  const [ssoEnabled, setSsoEnabled] = useState(false);
  const [ssoDomain, setSsoDomain] = useState('');
  const [ssoProtocol, setSsoProtocol] = useState<'saml' | 'oauth' | 'oidc'>(
    'saml'
  );
  const [workosOrganizationId, setWorkosOrganizationId] = useState('');
  const [workosConnectionId, setWorkosConnectionId] = useState('');

  const [creationReference, setCreationReference] = useState<{
    userId: string;
    workspaceId: string;
    projectId: string;
  } | null>(null);

  // New function to handle workspace creation
  const handleCreateWorkspace = async () => {
    // Basic validation - password only required if SSO is not enabled
    const basicValidationFailed = 
      !newWorkspaceName.trim() || 
      !newWorkspaceEmail.trim() || 
      (!ssoEnabled && !newWorkspacePassword.trim());

    if (basicValidationFailed) {
      const missingFields = [];
      if (!newWorkspaceName.trim()) missingFields.push('workspace name');
      if (!newWorkspaceEmail.trim()) missingFields.push('email');
      if (!ssoEnabled && !newWorkspacePassword.trim()) missingFields.push('password');

      toast({
        variant: 'destructive',
        title: 'Validation Error',
        description: `The following fields are required: ${missingFields.join(', ')}.`,
      });
      return;
    }

    // SSO validation if enabled
    if (ssoEnabled) {
      if (
        !ssoDomain.trim() ||
        !workosOrganizationId.trim() ||
        !workosConnectionId.trim()
      ) {
        toast({
          variant: 'destructive',
          title: 'SSO Configuration Error',
          description: 'All SSO fields are required when SSO is enabled.',
        });
        return;
      }

      // Basic domain validation
      const domainRegex =
        /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
      if (!domainRegex.test(ssoDomain)) {
        toast({
          variant: 'destructive',
          title: 'Invalid Domain',
          description: 'Please enter a valid domain (e.g., company.com)',
        });
        return;
      }

      // WorkOS ID format validation
      if (!workosOrganizationId.startsWith('org_')) {
        toast({
          variant: 'destructive',
          title: 'Invalid WorkOS Organization ID',
          description: 'Organization ID must start with "org_"',
        });
        return;
      }

      if (!workosConnectionId.startsWith('conn_')) {
        toast({
          variant: 'destructive',
          title: 'Invalid WorkOS Connection ID',
          description: 'Connection ID must start with "conn_"',
        });
        return;
      }
    }

    try {
      setIsCreating(true);

      // Create workspace with SSO config
      const createWorkspaceRequest: any = {
        name: newWorkspaceName,
        email: newWorkspaceEmail,
        // Add SSO configuration to the payload
        ssoConfig: ssoEnabled
          ? {
              enabled: true,
              domain: ssoDomain,
              protocol: ssoProtocol,
              workosOrganizationId,
              workosConnectionId,
            }
          : undefined,
      };

      // Only add password if SSO is not enabled
      if (!ssoEnabled) {
        createWorkspaceRequest.password = newWorkspacePassword;
      }

      const createNewWorkspace = await createWorkspace(createWorkspaceRequest);

      const createNewProject = await createStarterProject({
        workspaceId: createNewWorkspace.workspace.id,
        userId: createNewWorkspace.user.id,
        createProjectRequest: {
          name: 'Starter Project',
          primaryContentLanguage: 'EN' as keyof Language,
          type: 'CSRD',
        },
      });

      setCreationReference({
        userId: createNewWorkspace.user.id,
        workspaceId: createNewWorkspace.workspace.id,
        projectId: createNewProject.id,
      });

      refetchWorkspaces();

      // Clear the form
      setNewWorkspaceName('');
      setNewWorkspaceEmail('');
      setNewWorkspacePassword(''); // Always clear password field
      setSsoEnabled(false);
      setSsoDomain('');
      setSsoProtocol('saml');
      setWorkosOrganizationId('');
      setWorkosConnectionId('');

      toast({
        variant: 'success',
        description: `Workspace created successfully${ssoEnabled ? ' with SSO configuration' : ''}.`,
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'An unknown error occurred.',
      });
      console.error('Failed to create workspace:', error);
    }
    setIsCreating(false);
  };

  return (
    <div className="max-w-3xl">
      <div className="mt-10 grid gap-5">
        {/* Admin Tools Section */}
        <div className="mb-10">
          <Label className="text-2xl font-semibold mb-4 block">
            Admin Tools
          </Label>
          <div className="flex gap-4">
            <Link
              to="/admin/prompts"
              className={cn(
                buttonVariants({ variant: 'default' }),
                'flex gap-2'
              )}
            >
              <FileTextIcon className="h-4 w-4" />
              Prompt Management
            </Link>
          </div>
        </div>

        <Label className="text-2xl font-semibold">Switch Workspace</Label>
        <p className="text-gray-600 mt-2">
          As a Super Admin, you can switch between different workspaces.
        </p>

        {/* Workspace Switcher Component */}
        <div className="mt-4">
          <WorkspaceSwitcher currentWorkspaceId={currentWorkspaceId} />
        </div>

        {/* New Workspace Creation Section */}
        <div className="mt-10">
          <Label className="text-2xl font-semibold">Create New Workspace</Label>
          <p className="text-gray-600 mt-2">
            Create a new workspace with optional SSO configuration for
            enterprise authentication.
          </p>

          <div className="mt-4 grid gap-4">
            {/* Basic Workspace Information */}
            <div>
              <Label htmlFor="workspace-name" className="mb-2 block">
                Workspace Name
              </Label>
              <Input
                id="workspace-name"
                placeholder="Enter workspace name"
                value={newWorkspaceName}
                onChange={(e) => setNewWorkspaceName(e.target.value)}
                className="w-[400px]"
              />
            </div>

            <div>
              <Label htmlFor="workspace-email" className="mb-2 block">
                Admin Email
              </Label>
              <Input
                id="workspace-email"
                type="email"
                placeholder="Enter admin email address"
                value={newWorkspaceEmail}
                onChange={(e) => setNewWorkspaceEmail(e.target.value)}
                className="w-[400px]"
              />
            </div>

            {/* Password field - only show if SSO is not enabled */}
            {!ssoEnabled ? (
              <div>
                <Label htmlFor="workspace-password" className="mb-2 block">
                  Login Password
                </Label>
                <Input
                  id="workspace-password"
                  type="text"
                  placeholder="Enter a password"
                  value={newWorkspacePassword}
                  onChange={(e) => setNewWorkspacePassword(e.target.value)}
                  className="w-[400px]"
                />
              </div>
            ) : (
              <div className="p-3 bg-glacier-green/10 border border-glacier-green/20 rounded-lg">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-glacier-green" />
                  <span className="text-sm font-medium text-glacier-bluedark">
                    Password not required
                  </span>
                </div>
                <p className="text-sm text-glacier-bluedark/70 mt-1">
                  Users will authenticate through their organization's SSO provider instead of a password.
                </p>
              </div>
            )}

            {/* SSO Configuration Section */}
            <div className="mt-6 p-4 border rounded-lg bg-glacier-bluedark/5">
              <div className="flex items-center gap-3 mb-4">
                <Shield className="h-5 w-5 text-glacier-greenmid" />
                <Label className="text-lg font-medium">SSO Configuration</Label>
                <Switch
                  checked={ssoEnabled}
                  onCheckedChange={setSsoEnabled}
                  size="md"
                />
              </div>

              {ssoEnabled && (
                <div className="grid gap-4 ml-8">
                  <div className="flex items-start gap-2 p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
                    <AlertCircle className="h-4 w-4 text-blue-600 mt-0.5" />
                    <div className="text-sm text-blue-800">
                      <p className="font-medium">SSO Setup Required</p>
                      <p>
                        You'll need WorkOS Organization ID and Connection ID
                        from your WorkOS dashboard.
                      </p>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="sso-domain" className="mb-2 block">
                      Company Domain
                    </Label>
                    <Input
                      id="sso-domain"
                      placeholder="company.com"
                      value={ssoDomain}
                      onChange={(e) => setSsoDomain(e.target.value)}
                      className="w-[400px]"
                    />
                    <p className="text-sm text-gray-500 mt-1">
                      Users with emails from this domain will be redirected to
                      SSO
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="sso-protocol" className="mb-2 block">
                      SSO Protocol
                    </Label>
                    <Select
                      value={ssoProtocol}
                      onValueChange={(value: 'saml' | 'oauth' | 'oidc') =>
                        setSsoProtocol(value)
                      }
                    >
                      <SelectTrigger className="w-[400px]">
                        <SelectValue placeholder="Select SSO protocol" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="saml">SAML 2.0</SelectItem>
                        <SelectItem value="oauth">OAuth 2.0</SelectItem>
                        <SelectItem value="oidc">OpenID Connect</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="workos-org-id" className="mb-2 block">
                      WorkOS Organization ID
                    </Label>
                    <Input
                      id="workos-org-id"
                      placeholder="org_xxxxxxxxxxxxxxxxxxxxxxxxxx"
                      value={workosOrganizationId}
                      onChange={(e) => setWorkosOrganizationId(e.target.value)}
                      className={cn(
                        'w-[400px]',
                        workosOrganizationId &&
                          !workosOrganizationId.startsWith('org_')
                          ? 'border-red-500 focus:border-red-500'
                          : ''
                      )}
                    />
                    {workosOrganizationId &&
                      !workosOrganizationId.startsWith('org_') && (
                        <p className="text-sm text-red-600 mt-1">
                          Organization ID should start with "org_"
                        </p>
                      )}
                  </div>

                  <div>
                    <Label htmlFor="workos-conn-id" className="mb-2 block">
                      WorkOS Connection ID
                    </Label>
                    <Input
                      id="workos-conn-id"
                      placeholder="conn_xxxxxxxxxxxxxxxxxxxxxxxxxx"
                      value={workosConnectionId}
                      onChange={(e) => setWorkosConnectionId(e.target.value)}
                      className={cn(
                        'w-[400px]',
                        workosConnectionId &&
                          !workosConnectionId.startsWith('conn_')
                          ? 'border-red-500 focus:border-red-500'
                          : ''
                      )}
                    />
                    {workosConnectionId &&
                      !workosConnectionId.startsWith('conn_') && (
                        <p className="text-sm text-red-600 mt-1">
                          Connection ID should start with "conn_"
                        </p>
                      )}
                  </div>
                </div>
              )}
            </div>

            {/* Success Alert */}
            {creationReference && (
              <div className="bg-glacier-greenmid/10 border-l-4 border-glacier-greenmid p-4 rounded">
                <p className="font-medium text-glacier-greenmid">
                  Workspace created successfully!
                </p>
                <div className="mt-2 text-sm text-gray-600">
                  <p>User ID: {creationReference.userId}</p>
                  <p>Workspace ID: {creationReference.workspaceId}</p>
                  <p>Project ID: {creationReference.projectId}</p>
                </div>
              </div>
            )}

            <div className="flex gap-2">
              <button
                className={cn(
                  buttonVariants({ variant: 'default' }),
                  'flex gap-2',
                  isCreating ? 'cursor-not-allowed opacity-50' : ''
                )}
                onClick={handleCreateWorkspace}
                disabled={
                  isCreating ||
                  !newWorkspaceName ||
                  !newWorkspaceEmail ||
                  (!ssoEnabled && !newWorkspacePassword)
                }
              >
                {isCreating && (
                  <LoaderCircle className="h-4 w-4 animate-spin" />
                )}
                <PlusCircle className="h-4 w-4" />
                Create Workspace
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperAdminSettings;
