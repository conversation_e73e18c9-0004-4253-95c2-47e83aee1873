
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { useEffect } from 'react';

import { USER_QUERY_KEY } from '../apiConstants';

import {
  fetchUserProfile,
  login,
  resetPassword,
  sendPasswordResetEmail,
} from '@/api/authentication/authentication.api.ts';
import { IUser } from '@/types/user';
import { useAuth } from '@/context/AuthContext';

export const useAuthentication = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const { signOut: supabaseSignOut } = useAuth();

  const { data, isLoading } = useQuery<IUser | null>({
    queryKey: [USER_QUERY_KEY],
    retry: 0,
    queryFn: fetchUserProfile,
    staleTime: 1000 * 60 * 60,
  });

  const loginMutation = useMutation({
    mutationFn: login,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEY] });
      navigate('/');
    },
  });

  const logoutMutation = useMutation({
    mutationFn: supabaseSignOut,
    onSuccess: async () => {
      await queryClient.setQueryData([USER_QUERY_KEY], () => null);
      queryClient.clear();
      localStorage.removeItem('access_token');
      
      navigate('/login');
    },
  });

  const sessionReloadMutation = useMutation({
    mutationFn: fetchUserProfile,
    onSuccess: async () => {
      queryClient.clear();
    },
  });

  const sendPasswordResetEmailMutation = useMutation({
    mutationFn: sendPasswordResetEmail,
  });

  const resetPasswordMutation = useMutation({
    mutationFn: resetPassword,
    onSuccess: async () => {
      await queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEY] });
      navigate('/dashboard');
    },
  });

  return {
    user: data as IUser | null,
    isLoading,
    isLoginPending: loginMutation.isPending,
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    sendPasswordResetEmail: sendPasswordResetEmail.mutate,
    resetPassword: resetPassword.mutate,
    restPasswordLoading: resetPassword.isPending,
    sendPasswordResetEmailLoading: sendPasswordResetEmail.isPending,
    loginErrors: loginMutation.error ? String(loginMutation.error) : null,
    sessionReloadMutation,
  };
};
