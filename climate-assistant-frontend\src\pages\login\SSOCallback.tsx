import React, { useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useQueryClient } from '@tanstack/react-query';
import { USER_QUERY_KEY } from '@/api/apiConstants';
import { UserLoading } from '@/components/UserLoading';
import { useToast } from '@/components/ui/use-toast';

export const SSOCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  useEffect(() => {
    const error = searchParams.get('error');

    if (error) {
      handleSSOError(error);
      navigate('/login');
      return;
    }

    // SSO was successful if we reach here without error
    // The backend should have already set the authentication cookie
    // Refresh user data and redirect to dashboard
    queryClient.invalidateQueries({ queryKey: [USER_QUERY_KEY] });
    queryClient.invalidateQueries({ queryKey: ['permissions'] });

    // Small delay to allow queries to complete
    setTimeout(() => {
      navigate('/dashboard');
    }, 500);
  }, [searchParams, navigate, queryClient, toast]);

  const handleSSOError = (error: string) => {
    const errorMessages: Record<string, string> = {
      sso_failed: 'SSO authentication failed. Please try again.',
      sso_not_enabled: 'SSO is not enabled for your organization',
      domain_not_allowed: 'Your email domain is not authorized for SSO',
      sso_provider_error: 'Authentication provider error. Please try again.',
      user_deactivated: 'Your SSO account has been deactivated',
      invalid_state: 'Invalid SSO state. Please try logging in again.',
      expired_state: 'SSO session expired. Please try logging in again.',
    };

    toast({
      title: 'SSO Login Failed',
      description:
        errorMessages[error] || 'An unexpected error occurred during SSO login',
      variant: 'destructive',
    });
  };

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <UserLoading />
        <p className="mt-4 text-gray-600">Completing SSO authentication...</p>
      </div>
    </div>
  );
};
