import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { WorkOS } from '@workos-inc/node';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { Workspace } from '../workspace/entities/workspace.entity';
import { UserWorkspace } from '../users/entities/user-workspace.entity';
import { Token, TokenType } from '../users/entities/token.entity';
import { UsersService } from '../users/users.service';
import { JwtService } from '@nestjs/jwt';
import { getWorkOSConfig } from '../util/config';
import { WorkspaceService } from '../workspace/workspace.service';
import { USER_ROLES } from '../constants';
import {
  WebhookProcessorService,
  WebhookEvent,
} from './webhook-processor.service';
import { WorkspaceSSO } from '../workspace/entities/workspace-sso.entity';
import * as crypto from 'crypto';

export interface CreateSSOUserDto {
  email: string;
  name: string;
  authMethod: 'sso';
  ssoUserId: string;
  ssoProviderId: string;
  lastSsoLogin: Date;
}

@Injectable()
export class SsoService {
  private workos: WorkOS;
  private readonly logger = new Logger(SsoService.name);

  constructor(
    private configService: ConfigService,
    private usersService: UsersService,
    private jwtService: JwtService,
    private workspaceService: WorkspaceService,
    private webhookProcessor: WebhookProcessorService,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(Workspace)
    private workspaceRepository: Repository<Workspace>,
    @InjectRepository(UserWorkspace)
    private userWorkspaceRepository: Repository<UserWorkspace>,
    @InjectRepository(WorkspaceSSO)
    private workspaceSSORepository: Repository<WorkspaceSSO>,
    @InjectRepository(Token)
    private tokenRepository: Repository<Token>
  ) {
    const workosConfig = getWorkOSConfig();
    this.workos = new WorkOS(workosConfig.apiKey);
  }

  async initiateSSOLogin(
    workspaceId: string,
    redirectUrl?: string
  ): Promise<string> {
    const workspace = await this.workspaceRepository.findOne({
      where: { id: workspaceId },
    });

    if (!workspace?.ssoEnabled || !workspace.ssoConfig?.workosOrganizationId) {
      throw new BadRequestException('SSO not enabled for this workspace');
    }

    // Validate redirect URL before signing into state
    const validatedRedirectUrl = this.validateRedirectUrl(redirectUrl);

    // Sign state parameter for security
    const state = this.jwtService.sign(
      { workspaceId, redirectUrl: validatedRedirectUrl, timestamp: Date.now() },
      { expiresIn: '10m' }
    );

    const workosConfig = getWorkOSConfig();
    const authorizationUrl = this.workos.sso.getAuthorizationUrl({
      organization: workspace.ssoConfig.workosOrganizationId,
      clientId: workosConfig.clientId,
      redirectUri: workosConfig.redirectUri,
      state,
    });

    return authorizationUrl;
  }

  async handleSSOCallback(code: string, state: string): Promise<string> {
    console.log('SSO Service Debug:', {
      codeReceived: !!code,
      stateReceived: !!state,
      // stateLength: state?.length,
      // statePreview: state?.substring(0, 50),
      flowType: state ? 'SP-initiated' : 'IdP-initiated',
    });

    let workspaceId: string;
    let redirectUrl: string = '/dashboard'; // Default redirect

    // Get user profile from WorkOS first
    const workosConfig = getWorkOSConfig();
    const { profile } = await this.workos.sso.getProfileAndToken({
      code,
      clientId: workosConfig.clientId,
    });

    // Check if this is IdP-initiated (no state) or SP-initiated (has state)
    if (!state) {
      // IdP-initiated SSO: Find workspace by organization ID from the profile
      console.log('Handling IdP-initiated SSO flow');

      // First check the new WorkspaceSSO table
      const ssoConfig = await this.workspaceSSORepository.findOne({
        where: {
          workosOrganizationId: profile.organizationId,
          enabled: true,
        },
        relations: ['workspace'],
      });

      if (ssoConfig) {
        workspaceId = ssoConfig.workspaceId;
      } else {
        // Fallback: Query legacy workspace.ssoConfig
        const workspaces = await this.workspaceRepository.find({
          where: { ssoEnabled: true },
        });

        const matchingWorkspace = workspaces.find(
          (ws) => ws.ssoConfig?.workosOrganizationId === profile.organizationId
        );

        if (matchingWorkspace) {
          workspaceId = matchingWorkspace.id;
        } else {
          this.logger.error('No workspace found for organization', {
            organizationId: profile.organizationId,
            email: profile.email,
          });
          throw new UnauthorizedException(
            'No workspace configured for this organization'
          );
        }
      }
    } else {
      // SP-initiated SSO: Verify and decode state
      let decodedState: any;

      try {
        decodedState = this.jwtService.verify(state);
        console.log('State decoded successfully:', {
          workspaceId: decodedState.workspaceId,
          hasRedirectUrl: !!decodedState.redirectUrl,
        });

        workspaceId = decodedState.workspaceId;
        redirectUrl = decodedState.redirectUrl || '/dashboard';
      } catch (error) {
        console.error('JWT Verification Error:', {
          error: error.message,
          stateValue: state,
          stateLength: state?.length,
        });

        throw new UnauthorizedException('Invalid or expired SSO state');
      }
    }

    // CRITICAL: Validate organization ID to prevent cross-organization access
    const workspace = await this.workspaceRepository.findOne({
      where: { id: workspaceId },
    });

    if (!workspace) {
      throw new UnauthorizedException('Workspace not found');
    }

    // For IdP-initiated, we already validated the organization above
    // For SP-initiated, validate organization matches workspace config
    if (state && workspace.ssoConfig?.workosOrganizationId) {
      if (profile.organizationId !== workspace.ssoConfig.workosOrganizationId) {
        this.logger.error('Organization ID mismatch', {
          expected: workspace.ssoConfig.workosOrganizationId,
          received: profile.organizationId,
          workspaceId,
          email: profile.email,
        });
        throw new UnauthorizedException(
          'Invalid organization for this workspace'
        );
      }
    }

    const user = await this.provisionSSOUser(profile, workspaceId);

    // Log SSO event
    await this.logSSOEvent({
      type: 'login',
      userId: user.id,
      workspaceId: workspaceId,
      email: user.email,
      ssoProvider: profile.connectionType,
    });

    // Generate JWT token using the same pattern as AuthService
    const payload = {
      sub: user.id,
      id: user.id,
      email: user.email,
      workspaceId: workspaceId,
      authMethod: 'sso',
    };

    return await this.jwtService.signAsync(payload);
  }

  async provisionSSOUser(profile: any, workspaceId: string): Promise<User> {
    let user = await this.userRepository.findOne({
      where: [
        { email: profile.email },
        { ssoUserId: profile.id, ssoProviderId: profile.connectionId },
      ],
    });

    const workspace = await this.workspaceRepository.findOne({
      where: { id: workspaceId },
    });

    if (!workspace) {
      throw new BadRequestException('Workspace not found');
    }

    // Validate domain restrictions using optimized WorkspaceSSO table
    const emailDomain = profile.email.split('@')[1]?.toLowerCase();
    if (emailDomain) {
      const ssoConfig = await this.workspaceSSORepository.findOne({
        where: {
          domain: emailDomain,
          workspaceId: workspaceId,
          enabled: true,
        },
      });

      if (!ssoConfig) {
        // Fallback to legacy JSON config if not found in new table
        if (workspace.ssoConfig?.allowedDomains?.length > 0) {
          const isAllowed = workspace.ssoConfig.allowedDomains.some(
            (allowed) => emailDomain === allowed.toLowerCase()
          );
          if (!isAllowed) {
            throw new UnauthorizedException(
              'Domain not allowed for SSO access'
            );
          }
        } else {
          throw new UnauthorizedException('Domain not allowed for SSO access');
        }
      }
    }

    if (!user) {
      // Check if auto-provisioning is allowed or if user has a valid invitation
      if (!workspace.allowAutoProvisioning) {
        const validInvitation = await this.checkValidInvitation(profile.email, workspaceId);
        if (!validInvitation) {
          throw new UnauthorizedException(
            'Auto-provisioning is disabled for this workspace. Please contact your administrator for an invitation.'
          );
        }
      }

      // Create new user
      user = await this.createSSOUser({
        email: profile.email,
        name:
          `${profile.firstName || ''} ${profile.lastName || ''}`.trim() ||
          profile.email.split('@')[0],
        authMethod: 'sso',
        ssoUserId: profile.id,
        ssoProviderId: profile.connectionId,
        lastSsoLogin: new Date(),
      });
    } else {
      // Update existing user with SSO info
      user.ssoUserId = profile.id;
      user.ssoProviderId = profile.connectionId;
      user.lastSsoLogin = new Date();
      if (!user.authMethod) {
        user.authMethod = 'sso';
      }
      await this.userRepository.save(user);
    }

    // Ensure user has access to workspace
    // If auto-provisioning is disabled, use the role from invitation
    let roleId = workspace.ssoConfig?.defaultRoleId;
    if (!workspace.allowAutoProvisioning && !user.userWorkspaces?.length) {
      const invitation = await this.checkValidInvitation(profile.email, workspaceId);
      if (invitation) {
        roleId = invitation.roleId;
        // Mark invitation as accepted
        await this.markInvitationAsAccepted(invitation.id);
      }
    }

    await this.ensureWorkspaceAccess(user.id, workspaceId, roleId);

    return user;
  }

  async checkDomainSSO(domain: string): Promise<{
    hasSso: boolean;
    workspaceId?: string;
    ssoConfig?: WorkspaceSSO;
  }> {
    const normalizedDomain = domain.toLowerCase().trim();

    // Fast indexed lookup - no JSON operations!
    const ssoConfig = await this.workspaceSSORepository.findOne({
      where: {
        domain: normalizedDomain,
        enabled: true,
      },
      relations: ['workspace'],
    });

    return {
      hasSso: !!ssoConfig,
      workspaceId: ssoConfig?.workspaceId,
      ssoConfig: ssoConfig,
    };
  }

  async linkExistingUserToSSO(
    userId: string,
    ssoUserId: string,
    ssoProviderId: string
  ): Promise<User> {
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new BadRequestException('User not found');
    }

    user.ssoUserId = ssoUserId;
    user.ssoProviderId = ssoProviderId;
    user.authMethod = 'sso';

    return await this.userRepository.save(user);
  }

  async getWorkspaceProviders(workspaceId: string): Promise<any> {
    // First check optimized table for domain configurations
    const ssoConfigs = await this.workspaceSSORepository.find({
      where: { workspaceId, enabled: true },
    });

    if (ssoConfigs.length > 0) {
      // Use optimized data structure
      const providers = ssoConfigs.map((config) => ({
        id: config.workosConnectionId,
        organizationId: config.workosOrganizationId,
        domain: config.domain,
        protocol: config.protocol,
        type: config.protocol.toUpperCase(),
        name: `${config.domain} ${config.protocol.toUpperCase()}`,
      }));

      return {
        providers,
        ssoEnabled: true,
        domains: ssoConfigs.map((c) => c.domain),
      };
    }

    // Fallback to legacy approach
    const workspace = await this.workspaceRepository.findOne({
      where: { id: workspaceId },
    });

    if (!workspace?.ssoEnabled || !workspace.ssoConfig?.workosOrganizationId) {
      return { providers: [] };
    }

    try {
      // Get connections for the organization from WorkOS
      const connections = await this.workos.sso.listConnections({
        organizationId: workspace.ssoConfig.workosOrganizationId,
      });

      return {
        providers: connections.data || [],
        ssoEnabled: true,
      };
    } catch (error) {
      this.logger.error('Failed to fetch WorkOS connections:', error);
      return { providers: [] };
    }
  }

  async validateSSOSession(userId: string): Promise<boolean> {
    const user = await this.userRepository.findOne({
      where: { id: userId, authMethod: 'sso' },
    });

    if (!user?.ssoUserId || !user?.ssoProviderId) {
      return false;
    }

    // For now, just check if user has SSO fields
    // Note: WorkOS doesn't have a direct user validation API in the current SDK
    // This could be enhanced later if needed
    return true;
  }

  private async createSSOUser(userData: CreateSSOUserDto): Promise<User> {
    const user = this.userRepository.create({
      ...userData,
      email: userData.email.toLowerCase(),
    });

    return await this.userRepository.save(user);
  }

  private async ensureWorkspaceAccess(
    userId: string,
    workspaceId: string,
    defaultRoleId?: string
  ): Promise<void> {
    const existingAccess = await this.userWorkspaceRepository.findOne({
      where: { userId, workspaceId },
    });

    if (!existingAccess) {
      let roleId = defaultRoleId;

      if (!roleId) {
        const contributorRole = await this.workspaceService.findRoleByName(
          USER_ROLES.Contributor
        );
        roleId = contributorRole?.id;
      }

      if (!roleId) {
        throw new Error('Default role not found');
      }

      const userWorkspace = this.userWorkspaceRepository.create({
        userId,
        workspaceId,
        roleId,
        joinedAt: new Date(),
      });

      await this.userWorkspaceRepository.save(userWorkspace);
    }
  }

  async handleWebhook(
    rawBody: Buffer,
    signature: string,
    userIP?: string
  ): Promise<{ received: boolean }> {
    const workosConfig = getWorkOSConfig();

    // Step 1: Validate WorkOS IP (if configured)
    if (userIP && !this.isWorkOSIP(userIP)) {
      this.logger.warn(`Webhook from non-WorkOS IP: ${userIP}`);
      throw new UnauthorizedException('Invalid source IP');
    }

    if (!workosConfig.webhookSecret) {
      this.logger.error('Webhook secret not configured - rejecting webhook');
      throw new UnauthorizedException('Webhook not configured');
    }

    try {
      // Step 2: Parse payload for timestamp validation
      let payload: any;
      try {
        payload = JSON.parse(rawBody.toString());
      } catch (error) {
        throw new BadRequestException('Invalid JSON payload');
      }

      // Step 3: Validate timestamp (replay attack prevention)
      const now = Math.floor(Date.now() / 1000);
      const timestamp = this.extractTimestampFromSignature(signature);
      const tolerance = 300; // 5 minutes

      if (Math.abs(now - timestamp) > tolerance) {
        this.logger.warn(
          `Webhook timestamp outside tolerance: ${timestamp} vs ${now}`
        );
        throw new UnauthorizedException('Request timestamp outside tolerance');
      }

      // Step 4: Verify signature using timing-safe comparison
      const expectedSignature = this.computeWebhookSignature(
        rawBody.toString(),
        timestamp,
        workosConfig.webhookSecret
      );

      if (!this.timingSafeEqual(signature, expectedSignature)) {
        this.logger.error('Webhook signature verification failed');
        throw new UnauthorizedException('Invalid webhook signature');
      }

      // Step 5: Immediate response (before processing)
      // Process webhook asynchronously to prevent timeout vulnerabilities
      setImmediate(() => {
        this.processWebhookAsync(payload).catch((error) => {
          this.logger.error('Async webhook processing failed:', error);
        });
      });

      // Return immediately to WorkOS
      return { received: true };
    } catch (error) {
      this.logger.error('Webhook verification failed:', error);
      throw error;
    }
  }

  /**
   * Process webhook asynchronously to prevent timeout vulnerabilities
   */
  private async processWebhookAsync(payload: any): Promise<void> {
    try {
      const webhookEvent: WebhookEvent = {
        id: payload.id || `${payload.event}_${Date.now()}`,
        event: payload.event,
        data: payload.data,
        createdAt: payload.created_at || new Date().toISOString(),
        organizationId: payload.organization_id,
      };

      await this.webhookProcessor.processWebhookEvent(webhookEvent);
    } catch (error) {
      this.logger.error('Webhook async processing failed:', error);
    }
  }

  /**
   * Validate if IP is from WorkOS (implement based on WorkOS IP ranges)
   */
  private isWorkOSIP(ip: string): boolean {
    // WorkOS IP ranges (these should be configured from WorkOS documentation)
    const workosIPRanges = [
      // Add actual WorkOS IP ranges here
      // Example: '************/24'
    ];

    // For now, allow all IPs (remove this in production)
    // TODO: Implement proper IP range checking
    return true;
  }

  /**
   * Extract timestamp from WorkOS signature header
   */
  private extractTimestampFromSignature(signature: string): number {
    // WorkOS signature format: t=timestamp,v1=signature
    const parts = signature.split(',');
    for (const part of parts) {
      if (part.startsWith('t=')) {
        return parseInt(part.substring(2), 10);
      }
    }
    throw new BadRequestException('Invalid signature format');
  }

  /**
   * Compute expected webhook signature using HMAC-SHA256
   */
  private computeWebhookSignature(
    payload: string,
    timestamp: number,
    secret: string
  ): string {
    const signedPayload = `${timestamp}.${payload}`;
    const signature = crypto
      .createHmac('sha256', secret)
      .update(signedPayload, 'utf8')
      .digest('hex');
    return `t=${timestamp},v1=${signature}`;
  }

  /**
   * Timing-safe comparison to prevent timing attacks
   */
  private timingSafeEqual(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    const bufferA = Buffer.from(a);
    const bufferB = Buffer.from(b);
    return crypto.timingSafeEqual(bufferA, bufferB);
  }

  /**
   * Generate SAML Service Provider metadata XML
   * This metadata describes our service to identity providers
   */
  async getSAMLMetadata(): Promise<string> {
    const baseUrl =
      process.env.NODE_ENV === 'production'
        ? 'https://app.glacier.eco'
        : 'https://dev.glacier.eco';

    const entityId = `${baseUrl}/api/auth/saml/metadata`;
    const acsUrl = `${baseUrl}/api/auth/sso/callback`;
    const sloUrl = `${baseUrl}/api/auth/sso/logout`;

    // Generate SAML SP metadata XML
    const metadata = `<?xml version="1.0" encoding="UTF-8"?>
<md:EntityDescriptor xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata"
                     entityID="${entityId}">
  <md:SPSSODescriptor AuthnRequestsSigned="false" 
                      WantAssertionsSigned="true" 
                      protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
    
    <!-- Name ID Formats supported -->
    <md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified</md:NameIDFormat>
    <md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</md:NameIDFormat>
    
    <!-- Assertion Consumer Service - where SAML responses are sent -->
    <md:AssertionConsumerService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                                 Location="${acsUrl}"
                                 index="0"
                                 isDefault="true"/>
    
    <!-- Single Logout Service -->
    <md:SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                           Location="${sloUrl}"/>
    <md:SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect"
                           Location="${sloUrl}"/>
  </md:SPSSODescriptor>
  
  <!-- Contact Information -->
  <md:ContactPerson contactType="technical">
    <md:EmailAddress><EMAIL></md:EmailAddress>
  </md:ContactPerson>
  
  <!-- Organization Information -->
  <md:Organization>
    <md:OrganizationName xml:lang="en">Glacier Climate Assistant</md:OrganizationName>
    <md:OrganizationDisplayName xml:lang="en">Glacier Climate Assistant</md:OrganizationDisplayName>
    <md:OrganizationURL xml:lang="en">https://glacier.eco</md:OrganizationURL>
  </md:Organization>
</md:EntityDescriptor>`;

    return metadata;
  }

  /**
   * Handle SAML Single Logout (SLO) requests
   * This processes logout requests from identity providers
   */
  async handleSAMLLogout(samlRequest: string): Promise<string> {
    try {
      // In a full implementation, you would:
      // 1. Parse and validate the SAML LogoutRequest
      // 2. Extract user information
      // 3. Invalidate the user's session
      // 4. Generate a proper LogoutResponse

      // For now, we'll generate a basic successful response
      const baseUrl =
        process.env.NODE_ENV === 'production'
          ? 'https://app.glacier.eco'
          : 'https://dev.glacier.eco';

      const responseId = `_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const issueInstant = new Date().toISOString();

      const logoutResponse = `<?xml version="1.0" encoding="UTF-8"?>
<samlp:LogoutResponse xmlns:samlp="urn:oasis:names:tc:SAML:2.0:protocol"
                      xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion"
                      ID="${responseId}"
                      Version="2.0"
                      IssueInstant="${issueInstant}"
                      Destination=""
                      InResponseTo="">
  <saml:Issuer>${baseUrl}/api/auth/saml/metadata</saml:Issuer>
  <samlp:Status>
    <samlp:StatusCode Value="urn:oasis:names:tc:SAML:2.0:status:Success"/>
  </samlp:Status>
</samlp:LogoutResponse>`;

      this.logger.log('SAML SLO processed successfully');
      return logoutResponse;
    } catch (error) {
      this.logger.error('Failed to process SAML logout:', error);
      throw new BadRequestException('Invalid SAML logout request');
    }
  }

  /**
   * Log SSO events for audit trail
   */
  async logSSOEvent(event: {
    type: 'login' | 'logout' | 'provision' | 'link' | 'error';
    userId?: string;
    workspaceId: string;
    email?: string;
    ssoProvider?: string;
    error?: string;
  }) {
    this.logger.log('SSO_EVENT', {
      ...event,
      timestamp: new Date().toISOString(),
    });
  }

  // ===== OPTIMIZED SSO MANAGEMENT METHODS =====

  /**
   * Get all SSO domains for a workspace
   */
  async getWorkspaceSSO(workspaceId: string): Promise<WorkspaceSSO[]> {
    return await this.workspaceSSORepository.find({
      where: { workspaceId, enabled: true },
      order: { domain: 'ASC' },
    });
  }

  /**
   * Add SSO domain to workspace
   */
  async addSSODomain(data: {
    workspaceId: string;
    domain: string;
    workosOrganizationId: string;
    workosConnectionId: string;
    protocol?: 'saml' | 'oauth' | 'oidc';
    additionalConfig?: {
      defaultRoleId?: string;
      attributeMapping?: Record<string, string>;
      customSettings?: Record<string, any>;
    };
  }): Promise<WorkspaceSSO> {
    const normalizedDomain = data.domain.toLowerCase().trim();

    // Check if domain already exists
    const existing = await this.workspaceSSORepository.findOne({
      where: { domain: normalizedDomain },
    });

    if (existing) {
      throw new BadRequestException(
        `Domain ${normalizedDomain} is already configured for SSO`
      );
    }

    const ssoConfig = this.workspaceSSORepository.create({
      workspaceId: data.workspaceId,
      domain: normalizedDomain,
      workosOrganizationId: data.workosOrganizationId,
      workosConnectionId: data.workosConnectionId,
      protocol: data.protocol || 'saml',
      additionalConfig: data.additionalConfig,
      enabled: true,
    });

    return await this.workspaceSSORepository.save(ssoConfig);
  }

  /**
   * Remove SSO domain
   */
  async removeSSODomain(workspaceId: string, domain: string): Promise<void> {
    const normalizedDomain = domain.toLowerCase().trim();

    const result = await this.workspaceSSORepository.delete({
      workspaceId,
      domain: normalizedDomain,
    });

    if (result.affected === 0) {
      throw new BadRequestException(
        `Domain ${normalizedDomain} not found for workspace`
      );
    }
  }

  /**
   * Update SSO domain configuration
   */
  async updateSSODomain(
    workspaceId: string,
    domain: string,
    updates: Partial<{
      workosOrganizationId: string;
      workosConnectionId: string;
      protocol: 'saml' | 'oauth' | 'oidc';
      enabled: boolean;
      additionalConfig: any;
    }>
  ): Promise<WorkspaceSSO> {
    const normalizedDomain = domain.toLowerCase().trim();

    const ssoConfig = await this.workspaceSSORepository.findOne({
      where: { workspaceId, domain: normalizedDomain },
    });

    if (!ssoConfig) {
      throw new BadRequestException(
        `Domain ${normalizedDomain} not found for workspace`
      );
    }

    Object.assign(ssoConfig, updates);
    return await this.workspaceSSORepository.save(ssoConfig);
  }

  /**
   * Migrate legacy JSON config to new table structure
   * Used for gradual migration from old format
   */
  async migrateLegacySSO(workspaceId: string): Promise<WorkspaceSSO[]> {
    const workspace = await this.workspaceRepository.findOne({
      where: { id: workspaceId },
    });

    if (!workspace?.ssoConfig?.allowedDomains) {
      return [];
    }

    const results: WorkspaceSSO[] = [];
    const { ssoConfig } = workspace;

    for (const domain of ssoConfig.allowedDomains) {
      try {
        // Check if already migrated
        const existing = await this.workspaceSSORepository.findOne({
          where: { domain: domain.toLowerCase(), workspaceId },
        });

        if (!existing) {
          const config = await this.addSSODomain({
            workspaceId,
            domain,
            workosOrganizationId: ssoConfig.workosOrganizationId,
            workosConnectionId: (ssoConfig as any).connectionId || 'legacy',
            protocol: (ssoConfig as any).protocol || 'saml',
            additionalConfig: {
              defaultRoleId: ssoConfig.defaultRoleId,
              attributeMapping: (ssoConfig as any).attributeMapping,
            },
          });
          results.push(config);
          this.logger.log(
            `Migrated domain ${domain} for workspace ${workspaceId}`
          );
        }
      } catch (error) {
        this.logger.warn(`Failed to migrate domain ${domain}:`, error.message);
      }
    }

    return results;
  }

  /**
   * Validate redirect URLs to prevent open redirect attacks
   * Allows only allowlisted internal paths and same-origin URLs
   */
  private validateRedirectUrl(url: string | undefined): string | null {
    if (!url) return null;

    // Allowlist of safe internal paths
    const allowedPaths = [
      '/login',
      '/dashboard',
      '/projects',
      '/documents',
      '/settings',
      '/profile',
    ];

    // Check if it's a relative path
    if (url.startsWith('/')) {
      // Extract just the path (ignore query parameters for validation)
      const pathOnly = url.split('?')[0];

      // Allow exact matches or subpaths of allowed paths
      const isAllowed = allowedPaths.some(
        (allowed) => pathOnly === allowed || pathOnly.startsWith(allowed + '/')
      );

      return isAllowed ? url : null;
    }

    // For absolute URLs, only allow same origin
    try {
      const urlObj = new URL(url);
      const allowedOrigins = [
        'https://app.glacier.eco',
        'https://dev.glacier.eco',
      ];

      if (process.env.NODE_ENV !== 'production') {
        allowedOrigins.push('http://localhost:5173', 'http://localhost:8080');
      }

      return allowedOrigins.includes(urlObj.origin) ? url : null;
    } catch {
      // Invalid URL format
      return null;
    }
  }

  /**
   * Check if user has a valid invitation for the workspace
   */
  private async checkValidInvitation(email: string, workspaceId: string): Promise<{
    id: number;
    roleId: string;
  } | null> {
    // Find user by email
    const user = await this.userRepository.findOne({
      where: { email },
      relations: ['userWorkspaces'],
    });

    if (!user) {
      return null;
    }

    // Check if user already has workspace access
    const existingAccess = user.userWorkspaces?.find(
      (uw) => uw.workspaceId === workspaceId
    );

    if (existingAccess) {
      // User already has access, no need for invitation
      return { id: 0, roleId: existingAccess.roleId };
    }

    // Check for valid invitation token
    const invitation = await this.tokenRepository.findOne({
      where: {
        user: { id: user.id },
        type: TokenType.WorkspaceInvite,
      },
      relations: ['user', 'user.userWorkspaces'],
    });

    if (!invitation) {
      return null;
    }

    // Check if token is expired
    if (invitation.expiresAt < new Date()) {
      return null;
    }

    // Find the workspace access that was created during invitation
    const workspaceAccess = user.userWorkspaces?.find(
      (uw) => uw.workspaceId === workspaceId
    );

    if (!workspaceAccess) {
      return null;
    }

    return {
      id: invitation.id,
      roleId: workspaceAccess.roleId,
    };
  }

  /**
   * Mark invitation as accepted (delete the token)
   */
  private async markInvitationAsAccepted(invitationId: number): Promise<void> {
    if (invitationId > 0) {
      await this.tokenRepository.delete({ id: invitationId });
    }
  }
}
