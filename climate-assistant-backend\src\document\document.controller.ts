import {
  Body,
  Controller,
  Delete,
  Get,
  Header,
  HttpStatus,
  NotFoundException,
  Param,
  Post,
  Put,
  Request,
  Res,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { fileInterceptor } from '../util/upload-utils';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { DocumentService } from './document.service';
import { DatapointDocumentChunkService } from 'src/datapoint-document-chunk/datapoint-document-chunk.service';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { DocumentDatapointLinkingDto } from './entities/document-datapoint-linking.dto';
import { SystemPermissions } from 'src/constants';
import { DocumentChunkGuard, DocumentGuard } from './document.guard';
import { AuthGuard } from 'src/auth/auth.guard';
import { join } from 'path';
import { createReadStream, promises as fsPromises } from 'fs';
import { Response } from 'express';
import { PermissionGuard } from 'src/auth/guard/permission.guard';
import { Permissions } from 'src/auth/decorators/permissions.decorator';
import { WorkerLogger } from 'src/shared/logger.service';

@ApiTags('documents')
@UseGuards(AuthGuard)
@UseGuards(PermissionGuard)
@Controller('documents')
export class DocumentController {
  constructor(
    private readonly documentService: DocumentService,
    private readonly datapointDocumentChunkService: DatapointDocumentChunkService,
    @InjectDataSource() private dataSource: DataSource
  ) {}

  private readonly logger = new WorkerLogger(DocumentController.name);

  @Get('')
  @ApiOperation({ summary: 'Get all document uploads' })
  @ApiResponse({
    status: 200,
    description: 'Document uploads retrieved successfully',
  })
  async getDocumentUploads(@Request() req) {
    const workspace = req.user.workspaceId;

    const documentUploads =
      await this.documentService.getWorkspaceDocumentUploads(workspace);
    return documentUploads;
  }

  @Post('')
  @UseInterceptors(fileInterceptor)
  @ApiOperation({ summary: 'Upload a new document' })
  @ApiResponse({ status: 201, description: 'Document uploaded successfully' })
  async saveDocument(
    @Request() req,
    @UploadedFile() file: Express.Multer.File
  ) {
    const workspaceId = req.user.workspaceId;
    const userId = req.user.id;
    const { path, originalname } = file;
    const {
      documentType,
      esrsCategory: esrsCategoryString,
      year: yearString,
      month: monthString,
      day: dayString,
      remarks,
    } = req.body;

    const esrsCategory = esrsCategoryString
      ? esrsCategoryString.split(',')
      : [];
    const year = yearString ? parseInt(yearString) : null;
    const month =
      monthString && monthString !== '' && parseInt(monthString)
        ? parseInt(monthString)
        : null;
    const day =
      dayString && dayString !== '' && parseInt(dayString)
        ? parseInt(dayString)
        : null;

    await this.documentService.saveDocument({
      originalname,
      path,
      workspaceId,
      userId,
      documentType,
      esrsCategory,
      year,
      month,
      day,
      remarks,
    });

    return { message: 'Document uploaded and queued for extraction' };
  }

  @UseGuards(DocumentGuard)
  @Get('/:id')
  @ApiOperation({ summary: 'Get a specific document by ID' })
  @ApiResponse({ status: 200, description: 'Document retrieved successfully' })
  async getDocument(@Param('id') id: string) {
    return await this.documentService.getDocumentData(id);
  }

  @UseGuards(DocumentGuard)
  @Put('/:id')
  @ApiOperation({ summary: 'Update document settings' })
  @ApiResponse({ status: 200, description: 'Document settings updated' })
  async updateDocument(@Request() req, @Param('id') id: string) {
    const workspaceId = req.user.workspaceId;
    const userId = req.user.id;
    const { documentType, esrsCategory, year, month, day, remarks } = req.body;

    await this.documentService.updateDocumentSettings({
      id,
      workspaceId,
      userId,
      documentType,
      esrsCategory,
      year,
      month,
      day,
      remarks,
    });

    return { message: 'Document updated successfully' };
  }

  @UseGuards(DocumentGuard)
  @Get(':id/download')
  @Header('Access-Control-Expose-Headers', 'Content-Disposition')
  async downloadFile(
    @Request() req,
    @Param('id') id: string,
    @Res() res: Response
  ) {
    const document = req.document;

    const filePath = join(process.cwd(), document.path);
    try {
      await fsPromises.access(filePath);
    } catch (err) {
      throw new NotFoundException(`File does not exist on the server.`);
    }
    const safeFileName = document.name.replace(/[^\w.-]/g, '_');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${safeFileName}"`
    );
    res.setHeader('Content-Type', 'application/octet-stream');
    res.status(HttpStatus.OK);

    const fileStream = createReadStream(filePath);
    fileStream.pipe(res);
  }

  @UseGuards(DocumentGuard)
  @Post('/:id/extract-chunks')
  @ApiOperation({ summary: 'Extract chunks from a document' })
  @ApiResponse({
    status: 200,
    description: 'Document chunks extracted successfully',
  })
  async extractChunks(
    @Request() req,
    @Param('id') id: string
  ): Promise<{ message: string }> {
    //This is started and result is not awaited

    this.logger.log('Document Extraction Started');

    await this.documentService.extractDocumentChunks(
      id,
      req.user.id,
      req.body.premiumMode
    );
    return { message: 'Document Extraction Finished' };
  }

  @UseGuards(DocumentGuard)
  @Post('/:id/link-to-datapoints')
  @Permissions(SystemPermissions.SWITCH_WORKSPACE) //TODO : this might not be the right permission, currently checking for super admin
  @ApiOperation({ summary: 'Link document to datapoints' })
  @ApiResponse({
    status: 200,
    description: 'Document linked to datapoints successfully',
  })
  async linkDocumentToDatapoints(
    @Request() req,
    @Param('id') documentId: string,
    @Body() payload: DocumentDatapointLinkingDto
  ): Promise<{}> {
    //This is started and result is not awaited
    const response =
      await this.datapointDocumentChunkService.linkDocumentChunksToDatapoints(
        documentId,
        req.user.id,
        payload.maxNumberOfChunks
      );
    return JSON.stringify(response);
  }

  @UseGuards(DocumentGuard)
  @Delete('/:id')
  @ApiOperation({ summary: 'Delete a specific document by ID' })
  @ApiResponse({ status: 200, description: 'Document deleted successfully' })
  async deleteDocument(@Request() req, @Param('id') id: string) {
    const workspaceId = req.user.workspaceId;
    const userId = req.user.id;
    await this.documentService.deleteDocument(id, workspaceId, userId);
    return { message: 'Document deleted successfully' };
  }

  //TODO: REMOVE THIS AGAIN -> JUST FOR DEMO REASONS
  @Get('/all-links')
  @ApiOperation({ summary: 'Get all document links' })
  @ApiResponse({
    status: 200,
    description: 'Document links retrieved successfully',
  })
  async returnAllDocumentLinks() {
    const result = await this.dataSource.query(
      `Select esrs_datapoint."datapointId", content from esrs_datapoint cross join document_chunk where document_chunk."matchingsJson" like '%' || esrs_datapoint."datapointId" ||'%'`
    );
    return result;
  }

  @UseGuards(DocumentChunkGuard)
  @Get('/chunk/:id')
  @ApiOperation({ summary: 'Get a specific document chunk by ID' })
  @ApiResponse({
    status: 200,
    description: 'Document chunk retrieved successfully',
  })
  async getDocumentChunk(@Param('id') id: string) {
    return await this.documentService.getDocumentChunk(id);
  }

  @Delete('/chunk/:id')
  @Permissions(SystemPermissions.SWITCH_WORKSPACE) //TODO : this might not be the right permission, currently checking for super admin
  @ApiOperation({ summary: 'Delete a specific document chunk by ID' })
  @ApiResponse({
    status: 200,
    description: 'Document chunk deleted successfully',
  })
  async deleteDocumentChunk(@Request() req, @Param('id') id: string) {
    const workspaceId = req.user.workspaceId;
    const userId = req.user.id;
    return await this.documentService.deleteDocumentChunk({
      id,
      workspaceId,
      userId,
    });
  }

  @UseGuards(DocumentChunkGuard)
  @Post('/chunk/:id/link-datapoints')
  @ApiOperation({ summary: 'Link datapoints to a document chunk' })
  @ApiResponse({
    status: 200,
    description: 'Datapoints linked to document chunk successfully',
  })
  async linkDatapointsToChunk(
    @Param('id') id: string,
    @Request() req,
    @Body()
    body: {
      datapointRequestId: string;
      linked: boolean;
    }[]
  ): Promise<{ message: string }> {
    const userId = req.user.id;
    await this.documentService.bulkUpdateDocumentChunkMap({
      documentChunkId: id,
      userId,
      data: body,
    });
    return { message: 'Datapoints linked to document chunk' };
  }
}
