import { useEffect, useMemo, useState } from 'react';
import {
  CircleCheckBigIcon,
  HistoryIcon,
  LoaderCircle,
  SaveIcon,
  WandSparklesIcon,
  EditIcon,
} from 'lucide-react';

import { Button } from '../ui/button';
import IconRenderer from '../ui/icons';
import { TipTapEditor } from '../ui/tiptap/tiptap-editor';
import ConfirmDialog from '../ConfirmDialog';
import { toast } from '../ui/use-toast';

import { GeneratedContent } from './GeneratedContent';
import { AiGenerateReportTextConfirmModal } from './AiGenerateDrConfirmModal';
import { VersionHistoryItem, VersionHistoryModal } from './VersionHistoryModal';
import EsrsInfo from './EsrsInfo';

import { DataRequestData, EventType } from '@/types/project';
import { useReportText } from '@/hooks/use-reporttext';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { formatDate } from '@/lib/utils';
import { DataRequestStatus } from '@/types';
import {
  getVersionHistory,
  restoreVersion,
} from '@/api/workspace-settings/useWorkspaceSettings';
import { fetchAllMembers } from '@/api/workspace-settings/workspace-settings.api';
import { IUser } from '@/types/user';
import { usePermissions } from '@/context/permissionsContext';

export function ReportText({
  dataRequest,
  members,
  updateCallback,
}: {
  dataRequest: DataRequestData;
  members: { id: string; name: string }[];
  updateCallback?: () => void;
}) {
  const {
    reportText,
    setReportText,
    esrsDatapoints,
    handleApprove,
    handleSave,
    canReviewWithAi,
    handleReviewWithAi,
    canGenerateWithAi,
    handleGenerateWithAi,
    canApproveReportText,
    isLoadingGenerateWithAi,
    isLoadingReviewWithAi,
    confirmAiDialogOpen,
    setConfirmAiDialogOpen,
    dataRequestGeneration,
    updateDrGenerationStatus,
    unapproveDataRequest,
    getApproveReportTextTooltipContent,
    isDirty,
    contentVersion,
  } = useReportText({ dataRequest, updateCallback });
  const [isEditable, setIsEditable] = useState(false);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [versionHistory, setVersionHistory] = useState<
    VersionHistoryItem[] | null
  >(null);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [users, setUsers] = useState<IUser[]>([]);
  const { userPermissions } = usePermissions();

  useEffect(() => {
    fetchAllMembers().then((users) => {
      setUsers(users);
    });
  }, []);

  useEffect(() => {
    setIsEditable(dataRequest.status !== DataRequestStatus.Complete);
  }, [dataRequest.status]);

  const {
    allow: userCanGenerateWithAi,
    tooltip: canGenerateWithAiTooltipText,
  } = canGenerateWithAi();
  const { allow: userCanReviewWithAi, tooltip: canReviewWithAiTooltipText } =
    canReviewWithAi();

  const approvedByMember = useMemo(() => {
    return members.find((member) => member.id === dataRequest.approvedBy);
  }, [members, dataRequest.approvedBy]);

  const handleEditClick = () => {
    setConfirmDialogOpen(true);
  };

  const handleVersionHistoryClick = async () => {
    try {
      const history: VersionHistoryItem[] = await getVersionHistory(
        dataRequest.id
      );
      setVersionHistory(history);
      setShowVersionHistory(true);
    } catch (error) {
      console.error('Failed to fetch version history:', error);
    }
  };

  const handleConfirmEdit = async () => {
    try {
      await unapproveDataRequest();
      setIsEditable(true);
    } catch (e) {
      toast({
        variant: 'destructive',
        description: 'Unable to edit the text, please try again.',
      });
    } finally {
      setConfirmDialogOpen(false);
    }
  };

  const handleRestoreVersion = async (versionId: string) => {
    try {
      await restoreVersion(versionId);
      toast({
        variant: 'success',
        title: 'Version restored successfully',
        description: 'The version has been restored successfully.',
      });
    } catch (error) {
      console.error('Failed to restore version:', error);
      toast({
        title: 'Error restoring version',
        description: 'An error occurred while restoring the version.',
        variant: 'destructive',
      });
    } finally {
      setShowVersionHistory(false);
      updateCallback?.();
    }
  };

  return (
    <div className="my-3 space-y-3">
      <div className="relative">
        {!isEditable && (
          <div className="flex justify-between items-center">
            <div className="flex space-x-4">
              {approvedByMember && (
                <p>
                  Approved by: <span>{approvedByMember.name}</span>{' '}
                </p>
              )}
              {dataRequest.approvedAt && (
                <p>
                  Approved at:{' '}
                  <span>{formatDate(dataRequest.approvedAt)}</span>{' '}
                </p>
              )}
            </div>
            <Button
              variant="outline"
              className="ml-2"
              onClick={handleEditClick}
            >
              <EditIcon className="h-4 w-4 mr-2" />
              Edit Text
            </Button>
          </div>
        )}
        <TipTapEditor
          content={reportText}
          setContent={setReportText}
          isEditable={isEditable}
        />
        {userPermissions.canViewVersionHistory && (
          <Button
            variant={'link'}
            className="p-0 absolute right-4 bottom-2 text-foreground text-sm font-normal underline"
            onClick={handleVersionHistoryClick}
            disabled={!userPermissions.canViewVersionHistory}
          >
            <HistoryIcon className="h-4 w-4 mr-2" />
            Version History
          </Button>
        )}
      </div>

      {isEditable && (
        <div className="space-x-3">
          <Tooltip>
            <TooltipTrigger>
              <Button
                onClick={handleSave}
                variant={'default'}
                disabled={!isDirty}
              >
                <SaveIcon className="h-4 w-4 mr-2" />
                Save
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {isDirty ? 'Save changes' : 'No changes to save'}
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger>
              <Button
                variant={'outline'}
                disabled={!userCanGenerateWithAi || isLoadingGenerateWithAi}
                onClick={() =>
                  // Previously, the following condition was used to determine whether to open the dialog:
                  // reportText.trim() !== ''
                  //   ? setConfirmAiDialogOpen(true)
                  //   : handleGenerateWithAi()
                  setConfirmAiDialogOpen(true)
                }
                className="ml-2"
              >
                {isLoadingGenerateWithAi ? (
                  <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <IconRenderer
                    iconName={'HammerSparcle'}
                    className="h-4 w-4 mr-2"
                  />
                )}
                Generate with AI
              </Button>
            </TooltipTrigger>
            <TooltipContent>{canGenerateWithAiTooltipText}</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger>
              <Button
                variant="outline"
                className="ml-2"
                onClick={() => handleReviewWithAi()}
                disabled={!userCanReviewWithAi}
              >
                {isLoadingReviewWithAi ? (
                  <LoaderCircle className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <WandSparklesIcon className="h-4 w-4 mr-2" />
                )}
                Review with AI
              </Button>
            </TooltipTrigger>
            <TooltipContent>{canReviewWithAiTooltipText}</TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger>
              <Button
                variant={'forest'}
                onClick={handleApprove}
                disabled={!canApproveReportText}
              >
                <CircleCheckBigIcon className="h-4 w-4 mr-2" />
                Approve Text
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {getApproveReportTextTooltipContent()}
            </TooltipContent>
          </Tooltip>
        </div>
      )}

      <div className="mt-4">
        <EsrsInfo esrsDatapoint={esrsDatapoints} source="datarequest" />
      </div>

      {dataRequestGeneration &&
        userPermissions.canUpdateAiGenerationStatusOnDr && (
          <GeneratedContent
            generationContents={dataRequestGeneration}
            generationType={EventType.DataRequest}
            handleApproveOrReject={updateDrGenerationStatus}
          />
        )}

      <AiGenerateReportTextConfirmModal
        open={confirmAiDialogOpen}
        setOpen={setConfirmAiDialogOpen}
        callback={handleGenerateWithAi}
        dataRequest={dataRequest}
      />

      <ConfirmDialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={handleConfirmEdit}
        title="Edit Report Text"
        description="Are you sure you want to edit the report text?"
      />

      {versionHistory && (
        <VersionHistoryModal
          open={showVersionHistory}
          onOpenChange={setShowVersionHistory}
          versionHistory={versionHistory}
          users={users}
          contentVersion={contentVersion}
          handleRestoreVersion={handleRestoreVersion}
        />
      )}
    </div>
  );
}
