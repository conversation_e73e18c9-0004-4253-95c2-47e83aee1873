<br><br>

<p align="center">
  <img src="climate-assistant-frontend/public/logo-md.png" alt="Glacier Climate Assistant Architecture" width="600">
</p>
<br><br>

# Glacier Climate Assistant

**Glacier Climate Assistant** is a comprehensive AI-powered sustainability reporting and compliance platform designed to help organizations meet their climate and ESG (Environmental, Social, Governance) disclosure requirements.

## What This Application Does

### 🤖 **AI-Powered ESRS Reporting**

- **Automated Content Generation**: Uses advanced AI to generate compliant ESRS (European Sustainability Reporting Standards) datapoint content
- **Gap Analysis & Review**: AI analyzes existing sustainability reports to identify compliance gaps and provides actionable recommendations
- **Multi-Language Support**: Generates reports in multiple languages based on company preferences

### 📄 **Document Intelligence**

- **Smart Document Processing**: Automatically extracts and chunks uploaded sustainability documents, reports, and policies
- **Vector-Based Search**: Links document content to specific ESRS requirements using AI-powered classification
- **Citation Management**: Maintains proper source attribution and references throughout generated content

### 💬 **Interactive Climate Assistant**

- **Conversational AI Interface**: Chat-based interaction for sustainability guidance and initiative recommendations
- **Initiative Suggestions**: AI generates tailored climate action recommendations based on company context
- **Detailed Action Plans**: Provides comprehensive implementation guides, cost estimates, partner recommendations, and funding opportunities

### 🎯 **Materiality Assessment Integration**

- **Topic Prioritization**: Integrates materiality assessments to focus on most relevant sustainability topics
- **Hierarchical Topic Management**: Organizes ESRS topics (E1-E5, S1-S4, G1) with company-specific relevance
- **Compliance Prioritization**: Ensures material topics receive comprehensive reporting treatment

### 🏢 **Enterprise-Ready Platform**

- **Multi-Workspace Support**: Separate environments for different organizations or business units
- **SSO Integration**: Enterprise authentication with SAML 2.0 and OAuth support
- **Role-Based Access Control**: Different permission levels for various user types
- **Real-Time Collaboration**: Live updates and notifications for team collaboration

### 📊 **Regulatory Compliance Focus**

- **ESRS Standards**: Full coverage of European Sustainability Reporting Standards
- **GRI Integration**: Supports Global Reporting Initiative indicators
- **CSRD Compliance**: Aligned with Corporate Sustainability Reporting Directive requirements
- **ISO 50001 Support**: Energy management system guidance

## Target Users

- **Sustainability Managers** preparing ESRS reports
- **Compliance Teams** ensuring regulatory adherence
- **ESG Consultants** supporting multiple clients
- **Corporate Executives** overseeing sustainability strategy
- **Internal Auditors** reviewing sustainability disclosures

## Key Value Propositions

1. **Efficiency**: Reduces manual effort in sustainability reporting by 80%+
2. **Compliance**: Ensures adherence to complex ESRS requirements
3. **Quality**: AI-powered gap analysis improves report completeness
4. **Scalability**: Handles multiple projects and organizations simultaneously
5. **Intelligence**: Provides actionable insights for climate action planning

The platform essentially transforms the complex, time-consuming process of sustainability reporting into an efficient, AI-assisted workflow while ensuring regulatory compliance and providing strategic guidance for climate action.

---

## Technical Architecture

Glacier Climate Assistant is a multi-service platform designed to provide climate assistance through a collection of interconnected components. This repository contains the core code and configurations for the following services:

- **Frontend:** A React application serving the climate assistant user interface.
- **Backend:** A NestJS (Node.js) API that powers the climate assistant.
- **NGINX:** A reverse proxy responsible for routing requests to the appropriate services and managing SSL certificates.
- **Backend DB:** A PostgreSQL database for the backend.
- **Redis:** An in-memory data store used for caching and ephemeral data storage to improve application performance.
- **PM2:** A production process manager for Node.js applications that provides clustering, monitoring, and automatic restarts.
- **Grafana:** A monitoring and observability platform that provides dashboards and alerting for system metrics and application performance.

> **Important:** All critical environment variables are defined in the `.env` file. When running the project locally, copy `.env-example` to `.env` and update the credentials as necessary.

---

## Frontend

### Development

1. **Use Node v18.17.0 or higher:**
   ```bash
   nvm use 18.17.0
   ```
2. **Install dependencies:**
   ```bash
   npm install
   ```
3. **Start the development server:**
   ```bash
   npm run dev
   ```

### Production

- The production build is executed automatically in the Dockerfile. To build manually:
  ```bash
  npm run build
  ```

### Architecture & Tools

- **State Management:** tanstack/query
- **Styling:** shadcn/tailwind
- **Linting:** ESLint
- **Formatting:** Prettier

---

## Backend

### Development

1. **Use Node v20.9.0 or higher:**
   ```bash
   nvm use 20.9.0
   ```
2. **Install dependencies:**
   ```bash
   npm install
   ```
3. **Start the server in development mode (watch mode enabled):**
   ```bash
   npm run start:dev
   ```

### Production

- The production build is executed automatically in the Dockerfile. To build manually:
  ```bash
  npm run build
  ```

### Database Migrations

- **Generate a new migration:**
  ```bash
  npm run migration:generate
  ```
- **Run migrations locally:**
  ```bash
  npm run migration:run
  ```
- **Run migrations in production:**  
  _(Usually not needed as they run automatically on startup)_
  ```bash
  npm run migration:run:prod
  ```

### Architecture & Tools

- **Database Management:** TypeORM
- **Authentication:** JWT with HTTP-only cookies

---

## Redis

Redis is included in the Docker Compose setup to handle caching and ephemeral data storage.

### Key Details

- **Image:** `redis:7-alpine`
- **Port:** 6379
- **Healthcheck:** Uses `redis-cli ping`

### Usage in Local Development

- **Access:** Other services can connect to Redis using `redis:6379`.
- **Verification:** To check if Redis is running, enter the container and run:
  ```bash
  docker compose exec redis sh
  redis-cli ping
  ```

### Production

- Redis starts automatically with:
  ```bash
  docker compose up -d
  ```
- For enhanced security, consider configuring network-level access controls or adding password protection as needed.

---

## Additional Documentation

For more detailed information on specific aspects of the project, please refer to the documentation in the [`docs`](docs) directory:

### **Core Infrastructure & Architecture**

- **[Backend Architecture](docs/backend-architecture.md)**
- **[Process Queue](docs/process-queue.md)**
- **[Server-Side Events](docs/server-side-events.md)**
- **[Redis Pub/Sub Implementation](docs/redis-pubsub-implementation.md)**
- **[Migrations](docs/migrations/updating-dr-dp.md)**

### **Data Processing & Services**

- **[Datapoint Generation](docs/services/datapoint-generation-process.md)**
- **[Prompt Management Integration](docs/services/prompt-management-system.md)**

### **Authentication & Security**

- **[Roles & Permissions](docs/roles-permission.md)**

### **Process Management**

- **[PM2 Setup](docs/pm2-setup.md)**
- **[PM2 Complete Implementation](docs/pm2-complete-implementation.md)**

### **Server Monitoring**

- **[Base Configuration](docs/server-monitoring/base-configuration.md)**
- **[PM2 Cluster Monitoring](docs/server-monitoring/pm2-cluster-monitoring.md)**
- **[PM2 Monitoring Quickstart](docs/server-monitoring/pm2-monitoring-quickstart.md)**

### **Server & Maintenance**

- **[NGINX & Certbot Management](docs/nginx-certbot.md)**
- **[Database Backup Procedures](docs/database-backup.md)**
- **[Ubuntu Container Recovery](docs/ubuntu-container-recovery.md)**
- **[CI/CD Processes](docs/github-cicd.md)**

### **General Guides**

- **[Prompt Guide](docs/prompt-guide.md)**
