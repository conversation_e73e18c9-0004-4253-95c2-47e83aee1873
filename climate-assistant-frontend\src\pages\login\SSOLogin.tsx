import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { LoaderCircle, Building2, ArrowLeft, Shield } from 'lucide-react';

import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import {
  checkDomainSSO,
  initiateSSOLogin,
} from '@/api/authentication/authentication.api';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

export const SSOLogin: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const [isChecking, setIsChecking] = useState(false);
  const [ssoAvailable, setSSOAvailable] = useState<boolean | null>(null);
  const [workspaceId, setWorkspaceId] = useState<string | null>(null);
  const [isInitiating, setIsInitiating] = useState(false);
  const [isInvited, setIsInvited] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  // Handle invitation URL parameters
  useEffect(() => {
    const email = searchParams.get('email');
    const invited = searchParams.get('invited');
    
    if (email) {
      form.setValue('email', email);
      setIsInvited(invited === 'true');
      
      // Auto-check SSO for invited users
      if (invited === 'true') {
        checkSSO(email);
      }
    }
  }, [searchParams, form]);

  const checkSSO = async (email: string) => {
    if (!email || !email.includes('@')) return;

    setIsChecking(true);
    try {
      const domain = email.split('@')[1];
      const { hasSso, workspaceId: ssoWorkspaceId } =
        await checkDomainSSO(domain);

      setSSOAvailable(hasSso);
      setWorkspaceId(ssoWorkspaceId || null);

      if (!hasSso) {
        toast({
          title: 'SSO Not Available',
          description:
            'Your domain does not have SSO configured. Please use the regular login page or contact your IT administrator.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('SSO check failed:', error);
      setSSOAvailable(false);
      toast({
        title: 'SSO Check Failed',
        description:
          'Unable to check SSO availability. Please try again or contact support.',
        variant: 'destructive',
      });
    } finally {
      setIsChecking(false);
    }
  };

  const handleSSOLogin = async () => {
    if (!workspaceId) return;

    setIsInitiating(true);
    try {
      const response = await initiateSSOLogin(workspaceId);
      window.location.href = response.authorizationUrl;
    } catch (error) {
      console.error('SSO initiation failed:', error);
      toast({
        title: 'SSO Login Failed',
        description:
          'Unable to initiate SSO login. Please try again or contact support.',
        variant: 'destructive',
      });
    } finally {
      setIsInitiating(false);
    }
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    await checkSSO(values.email);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Back to Regular Login */}
        <div className="flex justify-center">
          <Button
            variant="ghost"
            onClick={() => navigate('/login')}
            className="text-glacier-bluedark hover:bg-glacier-green/10"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Regular Login
          </Button>
        </div>

        <Card className="border-slate-200">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-glacier-green/10 rounded-full flex items-center justify-center">
              <Shield className="w-8 h-8 text-glacier-green" />
            </div>
            <CardTitle className="text-2xl font-bold text-glacier-bluedark">
              {isInvited ? 'Welcome! Complete Your Invitation' : 'Enterprise SSO Login'}
            </CardTitle>
            <CardDescription className="text-slate-600">
              {isInvited 
                ? 'You\'ve been invited to join a workspace. Use your organization\'s SSO to get started.'
                : 'Sign in using your organization\'s single sign-on'
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {isInvited && (
              <Alert className="border-glacier-green/20 bg-glacier-green/5">
                <Shield className="h-4 w-4 text-glacier-green" />
                <AlertDescription className="text-glacier-bluedark">
                  <strong>Workspace Invitation:</strong> You've been invited to collaborate. 
                  Enter your work email below to continue with SSO.
                </AlertDescription>
              </Alert>
            )}
            
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-glacier-bluedark">
                        Work Email Address
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          type="email"
                          {...field}
                          className="border-slate-300 focus:border-glacier-blue"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {!ssoAvailable && (
                  <Button
                    type="submit"
                    className="w-full bg-glacier-bluedark hover:bg-glacier-bluedark/90 text-white rounded-full"
                    disabled={isChecking}
                  >
                    {isChecking ? (
                      <>
                        <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                        Checking SSO Availability...
                      </>
                    ) : (
                      'Continue with SSO'
                    )}
                  </Button>
                )}
              </form>
            </Form>

            {/* SSO Status */}
            {ssoAvailable === true && workspaceId && (
              <Alert className="bg-glacier-green/5 border-glacier-green/20">
                <Shield className="h-4 w-4 text-glacier-green" />
                <AlertDescription className="text-glacier-bluedark">
                  <strong>SSO Available!</strong> Your organization has single
                  sign-on configured. Click below to continue with your
                  company's identity provider.
                </AlertDescription>
              </Alert>
            )}

            {ssoAvailable === false && (
              <Alert className="bg-orange-50 border-orange-200">
                <AlertDescription className="text-orange-800">
                  <strong>SSO Not Available</strong> - Your domain doesn't have
                  SSO configured. Contact your IT administrator or use the
                  regular login page.
                </AlertDescription>
              </Alert>
            )}

            {/* SSO Login Button */}
            {ssoAvailable && workspaceId && (
              <Button
                onClick={handleSSOLogin}
                className="w-full bg-glacier-green hover:bg-glacier-green/90 text-white rounded-full"
                disabled={isInitiating}
              >
                {isInitiating ? (
                  <>
                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                    Redirecting to SSO...
                  </>
                ) : (
                  <>
                    <Building2 className="mr-2 h-4 w-4" />
                    Continue with Single Sign-On
                  </>
                )}
              </Button>
            )}

            {/* Help Text */}
            <div className="text-center space-y-2">
              <p className="text-sm text-slate-600">
                Need help? Contact your IT administrator about SSO setup.
              </p>
              <div className="flex items-center justify-center space-x-4 text-xs text-slate-500">
                <button
                  onClick={() => navigate('/sso')}
                  className="hover:text-glacier-blue underline"
                >
                  Learn about SSO
                </button>
                <span>•</span>
                <button
                  onClick={() => navigate('/sso-setup')}
                  className="hover:text-glacier-blue underline"
                >
                  IT Setup Guide
                </button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Help */}
        <div className="text-center">
          <p className="text-sm text-slate-600 mb-4">
            Don't have SSO? Use the regular login page instead.
          </p>
          <Button
            variant="outline"
            onClick={() => navigate('/login')}
            className="border-glacier-bluedark text-glacier-bluedark hover:bg-glacier-bluedark hover:text-white rounded-full"
          >
            Go to Regular Login
          </Button>
        </div>
      </div>
    </div>
  );
};
