import { Injectable, OnModuleDestroy } from '@nestjs/common';
import { Processor, Process, InjectQueue } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import { Redis } from 'ioredis';
import OpenAI from 'openai';
import { LlmService } from './llm.service';
import {
  LlmTokenTrackingService,
  TokenUsageRecord,
} from './llm-token-tracking.service';
import { LLM_MODELS } from 'src/constants';
import { TimeoutError } from 'src/middleware/error.middleware';
import { LLM_PROVIDER, RequestStatus } from '../enums';
import { JobQueue, JobProcessor } from 'src/types/jobs';
import { getRedisHost } from '../../env-helper';
import {
  MODEL_LIMITS,
  RATE_LIMIT_CONFIG,
  TokenUsage,
  getRedisKey,
} from '../../utils/llm-rate-limit.constants';
import { WorkerLogger } from 'src/shared/logger.service';

const seed = 123;

interface HandleRequestProps {
  tokens?: number;
  model: LLM_MODELS;
  messages: OpenAI.Chat.ChatCompletionMessageParam[];
  temperature: number;
  json: boolean;
  taskType: string;
  taskRelatedEntityId: string;
  workspaceId: string;
  userId: string;
  llmProvider?: LLM_PROVIDER;
  taskSubtype?: string;
  projectId?: string;
  modelVersion?: string;
  endpoint?: string;
}

@Injectable()
@Processor(JobProcessor.LlmRequest)
export class LlmRateLimiterService implements OnModuleDestroy {
  private readonly logger = new WorkerLogger(LlmRateLimiterService.name);
  private readonly redis: Redis;

  constructor(
    @InjectQueue(JobQueue.LlmRequest) private readonly llmQueue: Queue,
    private readonly llmService: LlmService,
    private readonly llmTokenTrackingService: LlmTokenTrackingService
  ) {
    this.redis = new Redis({
      host: getRedisHost(),
      port: 6379,
    });
  }

  @Process({ name: JobProcessor.LlmRequest, concurrency: 10 })
  async processLlmRequest(job: Job) {
    try {
      const request = JSON.parse(job.data.payload);
      return await this.processJob(request);
    } catch (error) {
      throw error;
    }
  }

  private async getModelUsage(model: LLM_MODELS): Promise<TokenUsage> {
    const pipeline = this.redis.pipeline();
    pipeline.get(getRedisKey(model, 'tokens'));
    pipeline.get(getRedisKey(model, 'requests'));
    pipeline.get(getRedisKey(model, 'lastReset'));

    const results = await pipeline.exec();

    return {
      tokensUsed: parseInt(results[0][1] as string) || 0,
      requestsUsed: parseInt(results[1][1] as string) || 0,
      lastReset: parseInt(results[2][1] as string) || 0,
    };
  }

  private async updateModelUsage(
    model: LLM_MODELS,
    tokensToAdd: number,
    options?: TokenUsageRecord
  ): Promise<void> {
    const pipeline = this.redis.pipeline();
    pipeline.incrby(getRedisKey(model, 'tokens'), tokensToAdd);
    pipeline.incr(getRedisKey(model, 'requests'));

    // Set expiration for keys to prevent memory leaks (expire after 2 minutes)
    pipeline.expire(
      getRedisKey(model, 'tokens'),
      RATE_LIMIT_CONFIG.REDIS_KEY_EXPIRY
    );
    pipeline.expire(
      getRedisKey(model, 'requests'),
      RATE_LIMIT_CONFIG.REDIS_KEY_EXPIRY
    );
    pipeline.expire(
      getRedisKey(model, 'lastReset'),
      RATE_LIMIT_CONFIG.REDIS_KEY_EXPIRY
    );

    await pipeline.exec();

    const usage = await this.getModelUsage(model);
    this.logger.debug(
      `Model ${model} - Updated tokens used: ${usage.tokensUsed}, ` +
        `Requests used: ${usage.requestsUsed}`
    );

    if (options) {
      await this.llmTokenTrackingService.recordTokenUsage(options);
    }
  }

  private async resetModelUsage(model: LLM_MODELS): Promise<void> {
    const currentTime = Date.now();
    const pipeline = this.redis.pipeline();
    pipeline.set(getRedisKey(model, 'tokens'), 0);
    pipeline.set(getRedisKey(model, 'requests'), 0);
    pipeline.set(getRedisKey(model, 'lastReset'), currentTime);

    // Set expiration for keys (expire after 2 minutes)
    pipeline.expire(
      getRedisKey(model, 'tokens'),
      RATE_LIMIT_CONFIG.REDIS_KEY_EXPIRY
    );
    pipeline.expire(
      getRedisKey(model, 'requests'),
      RATE_LIMIT_CONFIG.REDIS_KEY_EXPIRY
    );
    pipeline.expire(
      getRedisKey(model, 'lastReset'),
      RATE_LIMIT_CONFIG.REDIS_KEY_EXPIRY
    );

    await pipeline.exec();
  }

  private async resetTokensIfNeeded(model: LLM_MODELS): Promise<void> {
    const usage = await this.getModelUsage(model);
    const currentTime = Date.now();

    if (currentTime - usage.lastReset >= RATE_LIMIT_CONFIG.RESET_INTERVAL) {
      await this.resetModelUsage(model);
      this.logger.log(`Tokens reset for model ${model}`);
    }
  }

  private exceedsTokenLimit(tokens: number, model: LLM_MODELS): boolean {
    const limits = MODEL_LIMITS[model];
    return tokens > limits.maxTokensPerMinute;
  }

  async processJob(request) {
    await this.resetTokensIfNeeded(request.model);

    if (!(await this.canMakeRequest(request.tokens, request.model))) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return await this.processJob(request);
    }
    return await this.makeRequest(request);
  }

  async handleRequest(
    request: HandleRequestProps,
    priority: number = 1
  ): Promise<any> {
    const tokens = this.llmService.calculateTokens(request.messages);

    if (this.exceedsTokenLimit(tokens, request.model)) {
      throw new Error(
        `Request exceeds maximum token limit of ${MODEL_LIMITS[request.model].maxTokensPerMinute} for model ${request.model}`
      );
    }
    const payload = JSON.stringify({ tokens, ...request });
    const job = await this.llmQueue.add(
      JobQueue.LlmRequest,
      {
        payload,
      },
      {
        priority,
      }
    );
    try {
      const resp = await job.finished();
      return resp;
    } catch (error) {
      this.logger.error('Error processing job:', error);
      throw new Error(`Job failed: ${error.message}`);
    }
  }

  private async canMakeRequest(
    tokens: number,
    model: LLM_MODELS
  ): Promise<boolean> {
    const usage = await this.getModelUsage(model);
    const limits = MODEL_LIMITS[model];
    return (
      usage.tokensUsed + tokens <= limits.maxTokensPerMinute &&
      usage.requestsUsed < limits.maxRequestsPerMinute
    );
  }

  private async makeRequest({
    tokens,
    model,
    messages,
    temperature,
    json,
    llmProvider,
    taskType,
    taskSubtype,
    taskRelatedEntityId,
    workspaceId,
    userId,
    projectId,
    modelVersion,
  }: HandleRequestProps): Promise<any> {
    const titleParams: OpenAI.Chat.ChatCompletionCreateParams = {
      model,
      messages: messages as OpenAI.Chat.ChatCompletionMessageParam[],
      seed,
      temperature,
      response_format: json
        ? {
            type: 'json_object',
          }
        : undefined,
    };

    // We allow one retry if we hit a rate-limit error (429) with a valid wait time <= 30
    // starting attempt at 1 for better readability - first attempt is not a retry
    let attempts = 1;
    const maxAttempts = 5;

    while (attempts < maxAttempts) {
      // Decide which model to use this round:
      const currentModel = model;

      // Adjust OpenAI client & params based on the current model
      let openai;
      if (currentModel === LLM_MODELS['o3-mini']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.llmService.openAiClient;
        llmProvider = LLM_PROVIDER.OpenAI;
      } else if (currentModel === LLM_MODELS['o3']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.llmService.openAiClient;
        llmProvider = LLM_PROVIDER.OpenAI;
      } else if (currentModel === LLM_MODELS['o4-mini']) {
        delete titleParams.temperature;
        titleParams.reasoning_effort = 'medium';
        openai = this.llmService.azureo4miniClient;
        llmProvider = LLM_PROVIDER.Azure;
      } else {
        // Restore the temperature
        titleParams.temperature = temperature;
        // Remove reasoning_effort if previously set
        delete (titleParams as any).reasoning_effort;
        openai = this.llmService.azureOpenAiClient;
        llmProvider = LLM_PROVIDER.Azure;
      }

      titleParams.model = currentModel;

      if (!(await this.canMakeRequest(tokens, currentModel))) {
        continue;
      }
      // Update Redis counters for rate limiting
      await this.updateModelUsage(model, tokens);
      let startTime;

      try {
        let response = '';
        let promptTokens = 0;
        let completionTokens = 0;
        startTime = Date.now();

        if (titleParams.model === 'deepseek-r1') {
          response = await this.llmService.callDeepSeek(messages);
        } else {
          let timeoutId: NodeJS.Timeout;
          const timeoutPromise = new Promise<OpenAI.Chat.ChatCompletion>(
            (_, reject) => {
              timeoutId = setTimeout(
                () =>
                  reject(new TimeoutError('Request timed out after 3 minutes')),
                3 * 60 * 1000 // 3 minutes
              );
            }
          );

          try {
            const result: OpenAI.Chat.ChatCompletion = await Promise.race([
              openai.chat.completions.create(titleParams),
              timeoutPromise,
            ]);
            response = result.choices[0].message.content ?? '';
            promptTokens = result.usage?.prompt_tokens ?? 0;
            completionTokens = result.usage?.completion_tokens ?? 0;
          } finally {
            // Always cleanup timeout to prevent memory leaks
            if (timeoutId) {
              clearTimeout(timeoutId);
            }
          }
        }

        const responseTimeMs = Date.now() - startTime;
        // Update Redis and DB with complete token information
        await this.updateModelUsage(model, completionTokens, {
          inputTokens: promptTokens,
          outputTokens: completionTokens,
          model: currentModel,
          taskType,
          status: RequestStatus.Success,
          responseTimeMs,
          llmProvider,
          taskSubtype,
          taskRelatedEntityId,
          workspaceId,
          userId,
          projectId,
          modelVersion,
        });

        return {
          response: json ? JSON.parse(response) : response,
          status: 200,
          token: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_cost: this.llmService.calculateCost({
              model,
              inputTokens: promptTokens,
              outputTokens: completionTokens,
            }),
          },
        };
      } catch (error: any) {
        attempts++;

        if (error instanceof TimeoutError) {
          this.logger.log(
            `Timeout Error: ${error.message}. Retrying attempt ${attempts}/${maxAttempts}.`
          );
          continue; // Retry
        }

        const httpStatus = error?.status || error?.response?.status;

        const isInvalidChatRequest = httpStatus === 400;
        if (isInvalidChatRequest) {
          const errorMessage =
            'The request was either filtered due to the content management policy or exceeded context length.';
          await this.updateModelUsage(model, tokens, {
            inputTokens: tokens,
            outputTokens: 0,
            responseTimeMs: Date.now() - startTime,
            model: currentModel,
            modelVersion,
            status: RequestStatus.Failed,
            llmProvider,
            taskType,
            taskSubtype,
            taskRelatedEntityId,
            workspaceId,
            userId,
            projectId,
          });

          return {
            response: errorMessage,
            status: 400,
            token: {
              prompt_tokens: tokens,
              completion_tokens: 0,
              total_cost: 0,
            },
          };
        }

        const isRateLimit = httpStatus === 429;

        const retryAfterHeader =
          error?.headers?.['retry-after'] ||
          error?.response?.headers?.['retry-after'];
        const waitTime = parseInt(retryAfterHeader ?? '60', 10);

        this.logger.log(
          `Error: ${error.message} - Status: ${httpStatus} - Retry-After: ${waitTime}s`
        );

        // If it's a rate limit error with a valid waitTime and we haven't exceeded maxAttempts:
        //  1) If the waitTime is <= 240, we wait and then retry
        //  2) If the waitTime > 240 or we've already retried once, throw the error
        if (isRateLimit && attempts < maxAttempts) {
          if (waitTime > 240) {
            throw new Error(
              `Rate limit hit, but wait time (${waitTime}s) exceeds 240 seconds. Aborting.`
            );
          }
          // Wait for the indicated or default waitTime
          const safeWait = Math.max(waitTime, 30) + 5; // e.g. wait at least 30 second + 5 seconds buffer
          await new Promise((resolve) => setTimeout(resolve, safeWait * 1000));
        } else {
          // For any other error, or if we've already retried once, rethrow it
          throw error;
        }
      }
    }

    // If we got here, we've exhausted retries
    throw new Error('Max retry attempts reached. Aborting.');
  }

  async onModuleDestroy() {
    await this.redis.quit();
    this.logger.log('Redis connection closed');
  }
}
