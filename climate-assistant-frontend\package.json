{"name": "climate-assistant-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "tsc -b && vite build --mode production", "lint:fix": "eslint . --fix", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "prettier": "prettier --config \".prettierrc\"  \"./**/*.tsx\" \"./**/*.ts\" --write", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.51.1", "@tanstack/react-query-devtools": "^5.51.1", "@tanstack/react-table": "^8.20.5", "@tiptap/extension-bullet-list": "^2.9.1", "@tiptap/extension-character-count": "^2.9.1", "@tiptap/extension-color": "^2.9.1", "@tiptap/extension-heading": "^2.9.1", "@tiptap/extension-highlight": "^2.9.1", "@tiptap/extension-horizontal-rule": "^2.9.1", "@tiptap/extension-link": "^2.9.1", "@tiptap/extension-ordered-list": "^2.9.1", "@tiptap/extension-paragraph": "^2.9.1", "@tiptap/extension-subscript": "^2.9.1", "@tiptap/extension-superscript": "^2.9.1", "@tiptap/extension-table": "^2.9.1", "@tiptap/extension-table-cell": "^2.9.1", "@tiptap/extension-table-header": "^2.9.1", "@tiptap/extension-table-row": "^2.9.1", "@tiptap/extension-text-align": "^2.9.1", "@tiptap/extension-text-style": "^2.9.1", "@tiptap/extension-typography": "^2.9.1", "@tiptap/extension-underline": "^2.9.1", "@tiptap/pm": "^2.9.1", "@tiptap/react": "^2.9.1", "@tiptap/starter-kit": "^2.9.1", "@types/react-scroll-to-bottom": "4.2.5", "axios": "^1.7.2", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "lucide-react": "^0.407.0", "markdown-to-jsx": "^7.4.7", "marked": "^14.1.3", "mermaid": "^11.6.0", "openai": "^4.52.4", "path-to-regexp": "^8.2.0", "react": "^18.3.1", "react-compiler-runtime": "^19.1.0-rc.2", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.2", "react-multi-email": "^1.0.25", "react-router-dom": "^6.28.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.51.1", "@types/canvas-confetti": "^1.6.4", "@types/mixpanel-browser": "2.49.1", "@types/node": "^22.17.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "babel-plugin-react-compiler": "^19.1.0-rc.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "eslint-plugin-react-refresh": "^0.4.7", "postcss": "^8.4.39", "prettier": "^3.3.2", "tailwindcss": "^3.4.4", "typescript": "^5.2.2", "vite": "^5.3.1"}}