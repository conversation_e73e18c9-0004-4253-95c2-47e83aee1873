import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WorkspaceService } from './workspace.service';
import { Workspace } from './entities/workspace.entity';
import { DocumentChunk } from '../document/entities/document-chunk.entity';
import { Document } from '../document/entities/document.entity';
import { UserWorkspace } from '../users/entities/user-workspace.entity';
import { User } from '../users/entities/user.entity';
import { Token } from '../users/entities/token.entity';
import { EmailService } from '../external/email.service';
import { WorkspaceController } from './workspace.controller';
import { LlmService } from '../llm/services/llm.service';
import { VersionHistory } from './entities/version-history.entity';
import { EmailModule } from 'src/external/email.module';
import { Company } from './entities/company.entity';
import { Role } from '../users/entities/role.entity';
import { RolePermission } from '../auth/entities/role-permission.entity';
import { SharedModule } from '../shared/shared.module';
import { WorkspaceSSO } from './entities/workspace-sso.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Workspace,
      DocumentChunk,
      Document,
      UserWorkspace,
      Company,
      User,
      Token,
      VersionHistory,
      Role,
      RolePermission,
      WorkspaceSSO,
    ]),
    EmailModule,
    SharedModule,
  ],
  providers: [WorkspaceService, EmailService, LlmService],
  exports: [WorkspaceService, TypeOrmModule, EmailService],
  controllers: [WorkspaceController],
})
export class WorkspaceModule {}
