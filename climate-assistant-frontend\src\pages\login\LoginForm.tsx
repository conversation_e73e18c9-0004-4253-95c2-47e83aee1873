import { LoaderCircle } from 'lucide-react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';
import { useState, useCallback } from 'react';

import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form.tsx';
import { Input } from '@/components/ui/input.tsx';
import { Button } from '@/components/ui/button.tsx';
import { useAuthentication } from '@/api/authentication/authentication.query.ts';
import { SSOLoginButton } from '@/components/login/SSOLoginButton';
import {
  checkDomainSSO,
  initiateSSOLogin,
} from '@/api/authentication/authentication.api';
import { useToast } from '@/components/ui/use-toast';
import { useNavigate } from 'react-router-dom';
import { Building2 } from 'lucide-react';

interface ILoginFormProps {
  switchToForgotPassword: () => void;
}
const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(6, { message: 'Password must be at least 6 characters' }),
});
const LoginForm = ({ switchToForgotPassword }: ILoginFormProps) => {
  const { login, loginErrors, isLoginPending } = useAuthentication();
  const { toast } = useToast();
  const navigate = useNavigate();

  // SSO state
  const [showSSOOption, setShowSSOOption] = useState(false);
  const [ssoWorkspaceId, setSSOWorkspaceId] = useState<string | null>(null);
  const [ssoLoading, setSSOLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Check for SSO availability when email changes
  const handleEmailBlur = useCallback(async () => {
    const email = form.getValues('email');
    if (email && email.includes('@')) {
      const domain = email.split('@')[1];
      try {
        const { hasSso, workspaceId } = await checkDomainSSO(domain);

        if (hasSso) {
          setShowSSOOption(true);
          setSSOWorkspaceId(workspaceId);
        } else {
          setShowSSOOption(false);
          setSSOWorkspaceId(null);
        }
      } catch (error) {
        console.error('SSO check failed:', error);
        setShowSSOOption(false);
        setSSOWorkspaceId(null);
      }
    }
  }, [form]);

  const handleSSOLogin = async (workspaceId: string) => {
    setSSOLoading(true);
    try {
      const response = await initiateSSOLogin(workspaceId);
      window.location.href = response.authorizationUrl;
    } catch (error) {
      console.error('SSO initiation failed:', error);
      toast({
        title: 'SSO Login Failed',
        description: 'Unable to initiate SSO login. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setSSOLoading(false);
    }
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    await login(values);
  };

  return (
    <div className="flex flex-col w-full max-w-[380px]">
      <div className="font-semibold text-3xl text-center mb-8">
        Welcome Back
      </div>
      <div className="mb-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* SSO Option */}
            {showSSOOption && ssoWorkspaceId && (
              <div className="mb-6">
                <SSOLoginButton
                  workspaceId={ssoWorkspaceId}
                  onSSOLogin={handleSSOLogin}
                  isLoading={ssoLoading}
                />
                <div className="relative my-4">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Or continue with password
                    </span>
                  </div>
                </div>
              </div>
            )}

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Email"
                      {...field}
                      onBlur={() => {
                        field.onBlur();
                        handleEmailBlur();
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="Password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            {loginErrors && (
              <div className="space-y-2">
                {Array.isArray(loginErrors) ? (
                  <div className="text-red-500 text-sm">{loginErrors[0]}</div>
                ) : (
                  <div className="text-red-500 text-sm">{loginErrors}</div>
                )}
                {/* SSO guidance for specific error types */}
                {(Array.isArray(loginErrors) ? loginErrors[0] : loginErrors)?.includes('SSO') && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center gap-2 text-blue-800">
                      <Building2 className="h-4 w-4" />
                      <span className="text-sm font-medium">Need SSO Login?</span>
                    </div>
                    <p className="text-sm text-blue-700 mt-1">
                      Use our{' '}
                      <button
                        type="button"
                        onClick={() => navigate('/sso-login')}
                        className="underline hover:no-underline font-medium"
                      >
                        Enterprise SSO Login
                      </button>{' '}
                      page instead.
                    </p>
                  </div>
                )}
              </div>
            )}
            <Button type="submit" className="w-full">
              {isLoginPending ? (
                <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Login
            </Button>
          </form>
        </Form>

        {/* Separator and Enterprise SSO Option */}
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Enterprise Login
            </span>
          </div>
        </div>

        {/* Enterprise SSO Login Button */}
        <Button
          variant="outline"
          onClick={() => navigate('/sso-login')}
          className="w-full border-glacier-bluedark text-glacier-bluedark hover:bg-glacier-bluedark hover:text-white rounded-full mb-4"
        >
          <Building2 className="mr-2 h-4 w-4" />
          Enterprise SSO Login
        </Button>

        <div style={{ textAlign: 'center' }}>
          <Button
            variant="link"
            onClick={switchToForgotPassword}
            className="text-sm"
          >
            Forgot Password?
          </Button>
        </div>
      </div>
      <div className="text-center text-xs">
        If you have any questions, feel free to reach out to us via{' '}
        <a href="mailto:<EMAIL>"><EMAIL></a>. We will
        review your reply within one business day.
      </div>
    </div>
  );
};
export default LoginForm;
