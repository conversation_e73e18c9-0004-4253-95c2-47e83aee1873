# CLI Scripts

This directory contains CLI scripts for backend operations.

## Quick Start

1. Copy the example: `cp examples/simple-script-example.ts your-script.ts`
2. Edit the script with your logic
3. Run it: `ts-node -r tsconfig-paths/register src/scripts/your-script.ts`

## Example

See `examples/simple-script-example.ts` for the basic pattern.

## Available Scripts

### AI Miss Analysis Script (`analyze-ai-misses.ts`)

Analyzes why the AI failed to link certain document chunks to specific ESRS datapoints. This script:

1. Identifies chunks that were manually linked (not by AI)
2. Runs 3 validation rounds using the original AI prompts
3. For persistent misses, provides detailed analysis of why the AI failed

**Usage:**
```bash
ts-node -r tsconfig-paths/register src/scripts/analyze-ai-misses.ts <workspaceId>
```

**Example:**
```bash
ts-node -r tsconfig-paths/register src/scripts/analyze-ai-misses.ts 12345678-1234-1234-1234-123456789012
```

**Output:**
- Creates a JSON file: `ai-miss-analysis-<workspaceId>-<timestamp>.json`
- Contains detailed analysis of each missed chunk including:
  - Validation results from 3 rounds
  - Miss analysis with reasons and recommendations
  - Statistics on revalidation success rate

## Running Scripts

Instead of adding npm script commands, run directly:

```bash
# Run any script
ts-node -r tsconfig-paths/register src/scripts/your-script.ts

# With arguments
ts-node -r tsconfig-paths/register src/scripts/your-script.ts -- --arg1=value --arg2
```

## Base Classes

- `CliRunner` - Use this base class for most scripts (see example)
- Provides NestJS application context and error handling 